#!/usr/bin/env bash

NC='\033[0m' # No Color
RED='\033[0;31m'
GREEN='\033[0;32m'
ORANGE='\033[0;33m'


function renderCommands {
	echo -e "${ORANGE}Welcome in Kaufino Helper ;)${NC}"
		echo -e "${ORANGE}Coding standard:${NC}"
    echo -e "  ${GREEN}check${NC}					Checks current code with PHP_Sniffer"
    echo -e "  ${GREEN}fix${NC}					Fix current code with PHP_Sniffer."
    echo ""
    exit 0
}


function check {
	eval "vendor/bin/phpcs --standard=ruleset.xml --extensions=php --tab-width=4 --cache -sp app"
}

function fix {
	eval "vendor/bin/phpcbf --standard=ruleset.xml --extensions=php --tab-width=4 --cache -sp app"
}

function start {
    checkIsNotInDocker

    # check no other project is running
#    if [ -n "$(docker ps | grep -e 80 -e 443 -e 8025 -e 1025 -e 8085 | grep -v dinghy-http-proxy)" ]; then
#        echo "Looks like another project in docker is running. Please stop it first."
#        echo "Be a good user and just press enter ;-)"
#        read MULTIPLE_RUN_CONFIRMATION
#        if [ "$MULTIPLE_RUN_CONFIRMATION" != "I know this will fail and make a mess" ]; then
#            exit 1
#        fi
#    fi

	docker compose up -d --build

	if [ "$1" != "--soft" ]; then
    	docker exec -ti kaufinoWebserver composer install --ignore-platform-reqs
	fi

	docker exec -ti kaufinoWebserver mkdir -p temp/sessions temp/cache temp/proxies log www/upload www/webtemp www/webtemp/js www/webtemp/css
	docker exec -ti kaufinoWebserver rm -rf temp/sessions/* temp/cache/* temp/proxies/* www/webtemp/css/* www/webtemp/js/*
	docker exec -ti kaufinoWebserver chmod 777 -R log temp www/upload www/webtemp

	docker exec -ti kaufinoWebserver php bin/console orm:schema-tool:update --force
#	docker exec -ti kaufinoWebserver php bin/console dbal:import bin/import.sql
  docker exec -ti kaufinoWebserver php bin/console kaufino:import-sql-data:run


	echo -e "${GREEN}==== Environment ready :) enjoy ====${NC}"
}

function stop {
    checkIsNotInDocker
	docker-compose down
}

function restart {
    stop ${1}
    start ${1}
}

function goIntoContainer {
    checkIsNotInDocker
    docker exec -ti kaufinoWebserver bash
}

function isInLocalhost {
    HELPER_DOCKER_BINARY_EXISTS=`which docker`
    if [ -n "$HELPER_DOCKER_BINARY_EXISTS" ]
    then
        echo "true"
    else
        echo "false"
    fi
}

function containersRunning {
    HELPER_DOCKER_CONTAINERS_RUNNING=`docker ps | grep kaufinoWebserver`
    if [ -n "$HELPER_DOCKER_CONTAINERS_RUNNING" ]; then
        echo "true"
    else
        echo "false"
    fi
}

function checkIsInDocker {
    if [ "$(isInLocalhost)" = "true" ]; then
        echo -e "${RED}This command is only available from docker container, not from localhost.${NC}"
        exit 1
    fi
}

function checkIsNotInDocker {
    if [ "$(isInLocalhost)" = "false" ]; then
        echo -e "${RED}This command is only available from localhost, not from docker container.${NC}"
        exit 1
    fi
}

function ensureRunInContainer {
    if [ $(isInLocalhost) = "true" ] && [ $(containersRunning) = "true" ]; then
        docker exec -ti kaufinoWebserver $@
    else
        $@
    fi
}

