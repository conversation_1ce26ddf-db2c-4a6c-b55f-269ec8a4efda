
Letado
=============

Projekt, který to natře své konkurenci :))


Synchronizace se stevem
------------

sta<PERSON><PERSON><PERSON> o<PERSON>:

	php bin/console  kaufino:process-shops

sta<PERSON>en<PERSON> letáků:

	php bin/console kaufino:process-leaflets

stažení dealů:

	php bin/console kaufino:process-coupons 

## Docker
- ``bin/helper start`` nastartuje kontejner včetně instalace vendoru
- ``bin/helper start --soft`` nastartuje kontejner bez instalace vendoru
- ``bin/helper restart --soft`` restartuje kontejner bez instalace vendoru
- ``bin/helper stop`` z<PERSON><PERSON><PERSON> kontejn<PERSON>
- ``bin/helper in`` spustí shell v kontejneru
### nastavení nginx reverse proxy:
```
    server {
        listen 80;
        server_name kaufino.dev;

        location / {
            proxy_pass http://127.0.0.1:8087;
        }
    }
```

Nastavení localhostu
----------------

V<PERSON> s<PERSON> `app/config` je třeba vytvořit soubor `local.neon` s přístupy do lokální databáze. Example je v `local.example.neon`

................
10
..........
11
...........

UPDATE kaufino_shops_shop set active_kaufino = 1 where active_leaflets = 1;
UPDATE kaufino_shops_shop set active_letado = 1 where active_leaflets = 1;
UPDATE kaufino_shops_shop set active_oferto = 1 where active_oferto = 1;
UPDATE kaufino_shops_shop set active_oferto_com = 1 where active_coupons = 1;
# composer require codeception/module-phpbrowser --dev --ignore-platform-reqs
composer require codeception/lib-innerbrowser --ignore-platform-reqs --with-all-dependencies
composer update --ignore-platform-reqs

vendor/bin/codecept g:cest acceptance First