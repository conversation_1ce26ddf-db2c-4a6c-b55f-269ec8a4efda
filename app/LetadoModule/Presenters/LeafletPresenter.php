<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletUserMilestoneResolver;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class LeafletPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

    /** @var LeafletUserMilestoneResolver @inject */
    public $leafletUserMilestoneResolver;

	public function renderDefault(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets($this->localization, true, null, Website::MODULE_LETADO);

		$this->template->leaflets = $leaflets;
	}

	public function actionLeaflet(Shop $shop, Leaflet $leaflet, $page = 1): void
	{
		if ($leaflet->getShop() != $shop) {
			$this->error('The leaflet is not owned by the shop.');
		}

		if ($leaflet->isDeleted() || $shop->isActiveLetado() === false) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

		if ($leaflet->isArchived() && $page > 1) {
			$this->redirect('this', ['page' => 1]);
		}

        if (!$leaflet->hasPageByNumber($page)) {
            $this->redirectPermanent("Leaflet:leaflet", ['shop' => $shop, 'leaflet' => $leaflet]);
        }

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		$this->template->shop = $shop;
		$this->template->leaflet = $leaflet;


		$this->template->getSimilarLeaflets = (function() use ($shop, $leaflet) {
			return $this->leafletFacade->findLeafletsByShop($shop, 5, true, true, $leaflet);
		});

		$this->template->getRecommendedLeaflets = (function() {
			return $this->leafletFacade->findLeaflets($this->localization, true, 5, Website::MODULE_LETADO);
		});

		$this->template->similarShops = $this->shopFacade->findLeafletShops($this->localization, true, null, $this->website->getModule());
		$this->template->currentPage = $page;

		$this->template->leafletDescription = $this->contentGenerator->generateLeafletDescription($leaflet);

        $cookieName = 'leaflet_depth_milestone_' . $leaflet->getId();
        $currentMilestoneCookie = $this->getHttpRequest()->getCookie($cookieName);
        $currentMilestone = $currentMilestoneCookie !== null ? (int) $currentMilestoneCookie : -1;

        $leafletUserMilestone = $this->leafletUserMilestoneResolver->resolveMilestone($leaflet, $page, $currentMilestone);

        if ($leafletUserMilestone['isNewMilestone'] === true) {
            $this->getHttpResponse()->setCookie($cookieName, (string) $leafletUserMilestone['depthPercent'], time() + 60 * 60 * 48);
        }

        $this->template->currentLeafletUrl = $this->link('//:Letado:Leaflet:leaflet', [
            'shop' => $shop,
            'leaflet' => $leaflet,
            'page' => $page,
        ]);

        $this->template->countOfPages = $leaflet->getCountOfPages();
        $this->template->currentMilestone = $leafletUserMilestone['depthPercent'];
        $this->template->isNewMilestone = $leafletUserMilestone['isNewMilestone'];
	}
}
