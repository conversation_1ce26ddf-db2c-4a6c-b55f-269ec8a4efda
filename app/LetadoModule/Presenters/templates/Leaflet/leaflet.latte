
{block title}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{_"$websiteType.leaflet.metaTitle", [brand => $leaflet->getShop()->getName(), validSince => $validSince, validTill => $validTill]}
{if $currentPage > 1} - {_"$websiteType.leaflet.metaTitlePageSuffix", [page => $currentPage]}{/if}
{/block}

{block description}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{_"$websiteType.leaflet.metaDesc", [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
{/block}

{block robots}{if $leaflet->isArchived()}noindex,follow{elseif $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block scripts}
    {include parent}    

    {dump $isNewMilestone}
    {dump $currentMilestone}

    {if $isNewMilestone}
        <script n:syntax="double">
            window.dataLayer.push({
                'event': 'leaflet_depth_viewed',
                'shop_id': {{$shop->getName()}},
                'current_page': {{$currentPage}},
                'total_pages': {{$countOfPages}}, 
                'depth_percent': {{$currentMilestone}}, 
                'page_location': {{$currentLeafletUrl}}                        
            });
        </script>
    {/if}
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

{var $cacheKey = 'leaflet-' . $leaflet->getId() . '-page-' . $currentPage . '-isOferito-' . ($isOferito ? 1 : 0)}

<div class="leaflet k-lf-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
                <div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {_"$websiteType.leaflet.leaflet", [brand => $leaflet->getShop()->getName()]}
                            <span class="leaflet__date">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate}</span>
                        </h1>

                        {capture $validTillDay}{$leaflet->getValidTill()|dayGenitive}{/capture}
                        {capture $leafletPageCount}{count($leaflet->getPages())}{/capture}

						<p class="page-header__text mw-600">
                            {_oferto.leaflet.metaDesc, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay, leafletPageCount => $leafletPageCount]|noescape}
                        </p>
					</div>                					
				</div>

                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>                        
                    <!-- Letado - Responsive - 1 -->
                    <ins class="adsbygoogle adslot-2"
                        style="display:block"
                        data-ad-client="ca-pub-4233432057183172"
                        data-ad-slot="2194597072"
                        data-ad-format="auto"
                        data-full-width-responsive="true"></ins>
                </div>

                <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                </script>                            

                {if $leaflet->isArchived() === false}
                    {if $channel === 'c1'}
                        {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                    {else}
                        {include 'newPaginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                    {/if}
                {/if}

                <div class="">
                    <!-- letado.com / mobile_rectangle1 -->
                    {include "../components/mobile_rectangle1.latte"}

                    <!-- letado.com / medium_rectangle1 -->
                    {include "../components/medium_rectangle1.latte"}

                    <!-- letado.com / medium_rectangle2 -->
                    {include "../components/medium_rectangle2.latte"}
                </div>

                <div>
                    <!-- letado.com / pr_native3 -->
                    {include "../components/pr_native3.latte"}
                </div>

                <div>
                    {cache $cacheKey . "-preview"}
                        <div class="leaflet-preview mb-5">
                            <picture>
                                <source data-srcset="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:870,null,'fit','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-870.png" data-src="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:870,null}" width="870" height="1190" alt="{$leaflet->getShop()->getName()}" class="lazyload">
                            </picture>
                        </div>
                    {/cache}
                </div>

                {if $leaflet->getPartnerLink()}
                    <div class="ta-center mt-3 mb-3">
                        <a href="{$leaflet->getPartnerLink()}" target="_blank" class="k-button">
                            {_"$websiteType.leaflet.goToShop"}
						</a>
                    </div>
                {/if}

                <div>
                    <!-- letado.com / pr_native1 -->
                    {include "../components/pr_native1.latte"}
                </div>

                {if $leaflet->isArchived() === false}
                    {if $channel === 'c1'}
                        {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                    {else}
                        {include 'newPaginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                    {/if}
                {/if}

                {if $channel !== 'e1' && $channel !== 'e2' && $channel !== 'e3'}
                    <div>
                        <!-- letado.com / mobile_rectangle2 -->
                        {include "../components/mobile_rectangle2.latte"}
                    </div>
                {/if}

                {capture $leafletBrandLink}
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                {capture $leafletPageCount}
                    {count($leaflet->getPages())}
                {/capture}

                <div class="px-3 px-lg-0">
                    <p class="color-grey fz-m lh-15 mb-3"><strong>{_"$websiteType.leaflet.smallTitle", [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc", [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}
                    </p>

                    <p class="color-grey fz-m lh-15 mb-3">{_"$websiteType.leaflet.longDesc1", [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill] |noescape}</p>
                    <p class="color-grey fz-m lh-15 mb-3">{_"$websiteType.leaflet.longDesc2"}</p>
                    <p class="color-grey fz-m lh-15 mb-3">{_"$websiteType.leaflet.longDesc3"}</p>
                    <p class="color-grey fz-m lh-15 mb-5">{_"$websiteType.leaflet.longDesc4", [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill] |noescape}</p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_"$websiteType.leaflet.backToLeaflets"}</a>
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_"$websiteType.leaflet.allBrandLeaflets", [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>
            </div>
        </div>

        {cache $cacheKey ."-similar"}
            <!-- {(new \DateTime)->format('Y-m-d H:i:s')} -->
        <div class="leaflet__sidebar" style="height: auto !important;" n:if="count($getSimilarLeaflets()) > 0">
            <div class="lf__box">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_"$websiteType.leaflet.similarLeaflets", [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">
                    <div n:foreach="$getSimilarLeaflets() as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source data-srcset="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$similarLeaflet->getName()}" class="img-responsive lazyload">
                            </picture>
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="leaflet__aside">
                {cache $cacheKey ."-recommended"}
                    <!-- {(new \DateTime)->format('Y-m-d H:i:s')} -->
                <div class="lf__box lf__box-lg-border">
                    <h3 class="lf__box-title px-3 px-lg-0">{_"$websiteType.leaflet.recommendedLeaflets"}</h3>
                    <div class="lf__box-wrapper">
                        {foreach $getRecommendedLeaflets() as $recommendedLeaflet}
                            {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                            <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                                <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                    <picture>
                                        <source data-srcset="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$recommendedLeaflet->getName()}" class="img-responsive lazyload">
                                    </picture>
                                </a>
                                <p class="fz-xxs fz-sm-xs mb-0">
                                    <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                    <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                                </p>
                            </div>
                        {/foreach}
                    </div>
                </div>
                {/cache}

                <div class="float-wrapper">
                    <!-- letado.com / halfpage1 -->
                    {include "../components/halfpage1.latte"}
                </div>
            </div>

            <div class="float-wrapper">
                <!-- letado.com / halfpage2 -->
                {include "../components/halfpage2.latte"}
            </div>
        </div>
        {/cache}
    </div>

	<div class="float-wrapper__stop"></div>
</div>


