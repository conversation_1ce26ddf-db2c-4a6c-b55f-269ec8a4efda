{block title}{$tag->getName()}{/block}
{block description}{_"$websiteType.tag.text", [tag => $tag->getName()]}{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {$tag->getName()}
                        </h1>
						<p class="page-header__text">
                            {_"$websiteType.tag.text", [tag => $tag->getName()]}
                        </p>
					</div>					
				</div>                

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">                        
                        {foreach $leaflets as $leaflet}
                            <div class="k-leaflets__item mb-5">                
                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                                    <picture>
                                        <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                                    </picture>                                                            
                                </a>
                                <div class="k-leaflets__title mt-0 mb-0">
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                                        {$leaflet->getName()}
                                    </a>
                                </div>                
                                <p class="k-leaflets__date mt-0 mb-0">{($leaflet->getValidSince())|localDate} - {($leaflet->getValidSince())|localDate:'long'}</p>
                            </div>            
                        {/foreach}                        
                    </div>
                {else}
                    <div class="alert alert-info mx-3">{_"$websiteType.tag.noLeaflets"}</div>
                {/if}

                <div n:if="count($shops) > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.tag.otherShops"}</h2>
                    <div class="k-shop">    
                        {foreach $shops as $shop}
                            <a n:href="Shop:shop $shop" class="k-shop__item">
                                <span class="k-shop__image-wrapper">
                                    <picture>
                                        <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                                    </picture>
                                </span>
                                <small class="k-shop__title">{$shop->getName()}</small>
                            </a>
                        {/foreach}
                    </div>
                </div>                
                
            </div>        		            
        </div>
        
    </div>    

	<div class="float-wrapper__stop"></div>	
</div>
