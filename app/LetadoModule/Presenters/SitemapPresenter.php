<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Sitemap\Item;
use <PERSON><PERSON><PERSON>\Model\Sitemap\SitemapFeed;
use <PERSON><PERSON><PERSON>\Model\Sitemap\SitemapGenerator;

final class SitemapPresenter extends BasePresenter
{
    public const TYPE_SHOPS = 'shops';
    public const TYPE_LEAFLETS = 'leaflets';
    public const TYPE_OFFERS = 'offers';
    public const TYPE_TAGS = 'tags';
    public const TYPE_CITIES = 'cities';
    public const TYPE_OTHER = 'other';

    public SitemapGenerator $sitemapGenerator;

    public function __construct(SitemapGenerator $sitemapGenerator)
    {
        $this->sitemapGenerator = $sitemapGenerator;
    }

    public function renderType(string $type): void
    {
        $this->disableCachedResponse = true;

        switch ($type) {
            case self::TYPE_SHOPS:
                $feed = $this->sitemapGenerator->generateShopsFeed($this->website);
                break;
            case self::TYPE_LEAFLETS:
                $feed = $this->sitemapGenerator->generateLeafletsFeed($this->website);
                break;
            case self::TYPE_TAGS:
                $feed = $this->sitemapGenerator->generateTagsFeed($this->website);
                break;
            case self::TYPE_CITIES:
                $feed = $this->sitemapGenerator->generateCityFeed($this->website);
                $feed->addItem(new Item($this->link('//Cities:cities'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                break;
            case self::TYPE_OTHER:
                $feed = new SitemapFeed();
                $feed->addItem(new Item($this->link('//Homepage:default'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Shops:shops'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Leaflets:leaflets'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Offers:offers'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
                $feed->addItem(new Item($this->link('//Static:aboutUs'), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));

                if ($this->localization->hasArticles()) {
                    $feed->addItem(new Item($this->link('//Articles:articles'), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));
                }

                break;
            default:
                $this->error('Invalid type.');
        }

        $this->template->feed = $feed;
    }

    public function renderSitemap(): void
    {
        $this->disableCachedResponse = true;
    }
}
