<!DOCTYPE html>
<html lang="{$localization->getLocale()}">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">
    <link href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght@160..700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="{$basePath}/js/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="{$basePath}/css2/output.css">
    <title>Title</title>
</head>
<body>
<header class="flex items-center border border-b-light-4 h-[44px] md:h-[80px]">
    <div class="container px-[15px]">
        <div class="justify-between flex items-center">
            <div class="flex items-center gap-[56px]">
                <a class="flex-shrink-0" n:href="Homepage:default">
                    <img class="w-[87px] h-[14px] md:w-full md:h-full" src="{$basePath}/newDesign/logo.svg" alt="logo">
                </a>
                <div class="hidden lg:flex gap-[35px]">
                    <a class="hover:text-primary" href="{link Shops:shops}">{_kaufino.navbar.shops}</a>
                    <a class="hover:text-primary" href="{link Leaflets:leaflets}">{_kaufino.navbar.leaflets}</a>
                    <a class="hover:text-primary" href="{link Offers:offers}">{_kaufino.navbar.offers}</a>
                </div>
            </div>

            <div class="flex justify-end items-center gap-[14px] lg:gap-[28px]">
                <div class="w-[183px] lg:w-[380px] max-w-full relative">
                    <input
                        id="search-input-shops"
                        required type="text"
                        data-search-url="{link Ajax:search}"
                        placeholder="{_kaufino.navbar.search.placeholder}"
                        class="text-xs md:text-sm leading-[21px] md:leading-[24.5px] rounded-[20px] pl-[18px] pr-[50px] pt-[6px] md:pt-[7px] pb-[7px] md:pb-2 border w-full border-[#E5E5E5] focus:border-[#BC2026] focus:outline-none z-50 relative"
                    >
                    <div class="absolute right-0 top-0 bottom-0 flex items-center z-50 text-primary">
                        <button
                            type="submit"
                            class="js-search-submit"
                            data-search-url="{link Search:search, q => 'q'}"
                        >
                            <svg class="w-[34px] h-[34px] md:w-[40px] h-[40px]" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none" class="cursor-pointer">
                                <rect width="40" height="40" rx="20" fill="currentColor"/>
                                <path d="M14 19.25C14 20.6424 14.5531 21.9777 15.5377 22.9623C16.5223 23.9469 17.8576 24.5 19.25 24.5C20.6424 24.5 21.9777 23.9469 22.9623 22.9623C23.9469 21.9777 24.5 20.6424 24.5 19.25C24.5 17.8576 23.9469 16.5223 22.9623 15.5377C21.9777 14.5531 20.6424 14 19.25 14C17.8576 14 16.5223 14.5531 15.5377 15.5377C14.5531 16.5223 14 17.8576 14 19.25Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M26.7499 26.7499L22.9619 22.9619" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>

                    <div id="overlay-search-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40"></div>
                    <div id="search-modal" class="absolute top-full mt-1 hidden bg-white rounded-lg shadow-lg w-[280px] md:w-[380px] right-0 rounded-xl z-50">
                        <div class="flex flex-col items-center justify-between p-1.5">
                            <div class="flex items-center gap-2 p-1.5 md:p-2.5 hover:cursor-pointer hover:bg-light-6 w-full rounded-lg">
                                <img class="w-[29px] h-[29px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
                                <div>Lidl</div>
                            </div>

                            <div class="flex items-center gap-2 p-1.5 md:p-2.5 hover:cursor-pointer hover:bg-light-6 w-full rounded-lg">
                                <img class="w-[29px] h-[29px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
                                <div>Lidl</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="hidden lg:block relative flex-shrink-0">
                    <div class="flex flex-shrink-0 items-center gap-2.5 cursor-pointer" onclick="toggleModal()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                            <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Vyber město
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="7" viewBox="0 0 12 7" fill="none">
                            <path d="M1 1.00008L6.00015 6L11 1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>

                    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" onclick="hideModal()"></div>

                    <div id="select-city-modal" class="absolute top-full mt-6 hidden bg-white rounded-lg shadow-lg w-[309px] right-0 rounded-xl rounded-tr-[3px] z-[60]">
                        <div class="flex items-center justify-between py-4 pl-5 pr-[23px]">
                            <div class="flex items-center gap-[9px]">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                    <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <input id="select-city-input" type="text" placeholder="Vyber město" class="text-sm leading-[24.5px] outline-none">
                            </div>
                            <a class="text-primary text-sm leading-[24.5px] font-light underline" href=#">Potvrdit</a>
                        </div>
                        <div class="h-px bg-light-4 w-full"></div>

                        <div class="flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                <path d="M12.5531 1.08598L1.62897 5.77063C1.44746 5.84781 1.29183 5.97536 1.18047 6.1382C1.0691 6.30105 1.00665 6.49239 1.0005 6.68962C0.994352 6.88683 1.04476 7.08169 1.14576 7.25117C1.24677 7.42064 1.39415 7.55765 1.57049 7.646L5.4234 9.57338L7.34986 13.4292C7.43822 13.6054 7.57499 13.7526 7.74413 13.8537C7.91328 13.9548 8.10776 14.0054 8.30474 13.9995C8.50161 13.9937 8.69274 13.9318 8.85571 13.821C9.01858 13.7102 9.14647 13.555 9.22433 13.374L13.9154 2.44457C13.9976 2.25405 14.0205 2.04321 13.9814 1.83943C13.9423 1.63566 13.8428 1.44837 13.696 1.30188C13.549 1.15539 13.3615 1.05647 13.1577 1.01797C12.9539 0.979466 12.7433 1.00316 12.5531 1.08598Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Aktuální poloha
                        </div>

                        <div class="result-item flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer hover:mx-1 hover:rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="city-name">České Budějovice</span>
                        </div>


                        <div class="result-item flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer hover:mx-1 hover:rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="city-name">Český Těšín</span>
                        </div>

                        <div class="result-item flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer hover:mx-1 hover:rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="city-name">Český Krumlov</span>
                        </div>
                    </div>
                </div>

                <div class="lg:hidden hover:cursor-pointer" id="menuButton">
                    <svg id="menuIcon" xmlns="http://www.w3.org/2000/svg" width="16" height="14" viewBox="0 0 16 14" fill="none">
                        <rect width="16" height="2" rx="1" fill="#080B10"/>
                        <rect width="13" height="2" rx="1" transform="matrix(1 0 0 -1 3 8)" fill="#080B10"/>
                        <rect width="8" height="2" rx="1" transform="matrix(1 0 0 -1 8 14)" fill="#080B10"/>
                    </svg>
                    <svg id="closeIcon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none" class="hidden">
                        <path d="M0.242145 0.242149C0.565005 -0.0807163 1.08846 -0.0807163 1.41132 0.242149L13.7576 12.5887C14.0805 12.9115 14.0805 13.435 13.7576 13.7579C13.4348 14.0807 12.9113 14.0807 12.5884 13.7579L0.242146 1.41135C-0.0807146 1.08848 -0.0807155 0.565014 0.242145 0.242149Z" fill="#080B10"/>
                        <path d="M13.7579 0.242149C13.435 -0.0807163 12.9115 -0.0807163 12.5887 0.242149L0.242372 12.5887C-0.0804883 12.9115 -0.0804892 13.435 0.242371 13.7579C0.565231 14.0807 1.08869 14.0807 1.41155 13.7579L13.7579 1.41135C14.0807 1.08848 14.0807 0.565014 13.7579 0.242149Z" fill="#080B10"/>
                    </svg>
                </div>

                <div class="fixed inset-0 top-[44px] md:top-[80px] bg-black bg-opacity-50 hidden z-20" id="sidebar-overlay"></div>

                <div class="fixed top-[44px] md:top-[80px] right-0 h-full w-[249px] bg-white shadow-md transform translate-x-full transition-transform duration-300 z-30" id="sidebar">
                    <div class="p-1.5">
                        <div class="py-1.5 pl-[9px] bg-light-6 text-[10px] leading-[21px] text-light-1 uppercase font-medium rounded-md mb-[11px]">všeobecné</div>

                        <div class="mb-2.5">
                            <div class="flex items-center justify-between text-sm leading-[24.5px] py-1.5 px-[9px] mb-3 hover:bg-light-6 hover:rounded-md hover:cursor-pointer">
                                Obchody
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
                                    <path d="M1 6.49405L12.9164 6.50178M8.58692 12L14 6.49397L8.59864 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="flex items-center justify-between text-sm leading-[24.5px] py-1.5 px-[9px] hover:bg-light-6 hover:rounded-md hover:cursor-pointer">
                                Akční letáky
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
                                    <path d="M1 6.49405L12.9164 6.50178M8.58692 12L14 6.49397L8.59864 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        </div>
                        <div class="py-1.5 pl-[9px] bg-light-6 text-[10px] leading-[21px] text-light-1 uppercase font-medium rounded-md mb-[11px]">obchody</div>
                        {foreach range(1, 11) as $i}
                            <div class="flex items-center justify-between text-sm leading-[24.5px] py-1.5 px-[9px] mb-3 hover:bg-light-6 hover:rounded-md hover:cursor-pointer">
                                Penny
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
                                    <path d="M1 6.49405L12.9164 6.50178M8.58692 12L14 6.49397L8.59864 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        {/foreach}
                    </div>
                </div>

            </div>
        </div>
    </div>
</header>

{include content}



<div class="bg-[#210B0B]">
    <div class="container">
        <div class="pt-[42px] md:pt-[70px]">
            <a class="flex-shrink-0" href="Homepage:default">
                <img class="w-[156px] h-[27px] md:w-[232px] md:h-[40px]" src="{$basePath}/newDesign/logo.svg" alt="logo">
            </a>

            <div class="w-full h-px bg-white/25 mt-[42px] mb-[15px] md:my-[42px]"></div>

            <div class="grid grid-cols-1 md:grid-cols-4">
                <div class="mb-4 border-b border-white/25 md:border-none">
                    <div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
                        Obchody
                        <svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
                            <path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 pb-[71px] md:pb-0">
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                    </ul>
                </div>
                <div class="mb-4 border-b border-white/25 md:border-none">
                    <div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
                        O Kaufino
                        <svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
                            <path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 mb-[45px] pb-[71px] md:pb-0">
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                        <li>Letáky</li>
                    </ul>
                </div>
                <div class="mb-4 border-b border-white/25 md:border-none">
                    <div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
                        Obchody
                        <svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
                            <path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 pb-[71px] md:pb-0">
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                        <li>Penny</li>
                    </ul>
                </div>
                <div class="mb-4 border-b border-white/25 md:border-none">
                    <div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
                        O Kafino
                        <svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
                            <path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 mb-[45px]">
                        <li>Letáky</li>
                        <li>Obchody</li>
                        <li>Města</li>
                        <li>Magazín</li>
                        <li>O nás</li>
                        <li>Cookies</li>
                    </ul>
                    <div class="hidden md:block md:text-[26px] md:leading-[39px] text-white mb-6">
                        Další země
                    </div>
                    <div id="dropdown" class="hidden md:block relative inline-block">
                        <button id="dropdownToggle" class="flex items-center gap-1.5 bg-transparent text-white py-[13px] pl-3 pr-[15px] border border-gray-600 rounded-lg text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                <path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
                                <path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
                                <path d="M9.50042 18.9999C13.5851 18.9999 17.0672 16.4218 18.4095 12.8042H0.591309C1.93359 16.4218 5.41575 18.9999 9.50042 18.9999Z" fill="#D80027"/>
                                <path d="M2.45654 5.36963V10.1087C2.45654 12.8045 5.97826 13.6305 5.97826 13.6305C5.97826 13.6305 9.49994 12.8045 9.49994 10.1087V5.36963H2.45654Z" fill="white"/>
                                <path d="M3.28223 5.36963V10.1087C3.28223 10.4252 3.35251 10.7241 3.49175 11.0043H8.46392C8.60315 10.7241 8.67344 10.4252 8.67344 10.1087V5.36963H3.28223Z" fill="#D80027"/>
                                <path d="M7.63001 8.67404H6.39093V7.84795H7.21702V7.02186H6.39093V6.1958H5.56484V7.02186H4.73879V7.84795H5.56484V8.67404H4.32568V9.50013H5.56484V10.3262H6.39093V9.50013H7.63001V8.67404Z" fill="white"/>
                                <path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
                            </svg>
                            <span>Slovakia</span>
                            <svg id="dropdownArrow" class="ml-[38px] transition-transform duration-300 ease-out" xmlns="http://www.w3.org/2000/svg" width="12" height="7" viewBox="0 0 12 7" fill="none">
                                <path d="M1 1.00008L6.00015 6L11 1" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <div id="dropdownMenu" class="hidden absolute bg-[#210B0B] border border-gray-600 rounded-lg mt-2 min-w-[165px] z-10 py-2">
                            <div class="flex gap-1.5 items-center py-2 px-4 text-white cursor-pointer hover:bg-gray-700 text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                    <path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
                                    <path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
                                    <path d="M9.50042 18.9999C13.5851 18.9999 17.0672 16.4218 18.4095 12.8042H0.591309C1.93359 16.4218 5.41575 18.9999 9.50042 18.9999Z" fill="#D80027"/>
                                    <path d="M2.45654 5.36963V10.1087C2.45654 12.8045 5.97826 13.6305 5.97826 13.6305C5.97826 13.6305 9.49994 12.8045 9.49994 10.1087V5.36963H2.45654Z" fill="white"/>
                                    <path d="M3.28223 5.36963V10.1087C3.28223 10.4252 3.35251 10.7241 3.49175 11.0043H8.46392C8.60315 10.7241 8.67344 10.4252 8.67344 10.1087V5.36963H3.28223Z" fill="#D80027"/>
                                    <path d="M7.63001 8.67404H6.39093V7.84795H7.21702V7.02186H6.39093V6.1958H5.56484V7.02186H4.73879V7.84795H5.56484V8.67404H4.32568V9.50013H5.56484V10.3262H6.39093V9.50013H7.63001V8.67404Z" fill="white"/>
                                    <path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
                                </svg>
                                <span>Slovakia</span>
                            </div>
                            <div class="flex gap-1.5 items-center py-2 px-4 text-white cursor-pointer hover:bg-gray-700 text-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                    <path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
                                    <path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
                                    <path d="M9.50042 18.9999C13.5851 18.9999 17.0672 16.4218 18.4095 12.8042H0.591309C1.93359 16.4218 5.41575 18.9999 9.50042 18.9999Z" fill="#D80027"/>
                                    <path d="M2.45654 5.36963V10.1087C2.45654 12.8045 5.97826 13.6305 5.97826 13.6305C5.97826 13.6305 9.49994 12.8045 9.49994 10.1087V5.36963H2.45654Z" fill="white"/>
                                    <path d="M3.28223 5.36963V10.1087C3.28223 10.4252 3.35251 10.7241 3.49175 11.0043H8.46392C8.60315 10.7241 8.67344 10.4252 8.67344 10.1087V5.36963H3.28223Z" fill="#D80027"/>
                                    <path d="M7.63001 8.67404H6.39093V7.84795H7.21702V7.02186H6.39093V6.1958H5.56484V7.02186H4.73879V7.84795H5.56484V8.67404H4.32568V9.50013H5.56484V10.3262H6.39093V9.50013H7.63001V8.67404Z" fill="white"/>
                                    <path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
                                </svg>
                                <span>Slovakia</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="md:hidden">
                    <div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
                        <div class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                <path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
                                <path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>

                                <path d="M9.50017 18.9999C13.5848 18.9999 17.067 16.4218 18.4093 12.8042H0.591064C1.93335 16.4218 5.41551 18.9999 9.50017 18.9999Z" fill="#D80027"/>
                                <path d="M2.45679 5.36963V10.1087C2.45679 12.8045 5.9785 13.6305 5.9785 13.6305C5.9785 13.6305 9.50018 12.8045 9.50018 10.1087V5.36963H2.45679Z" fill="white"/>
                                <path d="M3.28247 5.36963V10.1087C3.28247 10.4252 3.35276 10.7241 3.49199 11.0043H8.46416C8.6034 10.7241 8.67368 10.4252 8.67368 10.1087V5.36963H3.28247Z" fill="#D80027"/>
                                <path d="M7.63026 8.67404H6.39118V7.84795H7.21727V7.02186H6.39118V6.1958H5.56508V7.02186H4.73903V7.84795H5.56508V8.67404H4.32593V9.50013H5.56508V10.3262H6.39118V9.50013H7.63026V8.67404Z" fill="white"/>
                                <path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
                            </svg>
                            Slovakia
                        </div>
                        <svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
                            <path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 mb-[45px] pb-[71px] md:pb-0">
                        <li class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                <path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
                                <path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
                                <path d="M9.50017 18.9999C13.5848 18.9999 17.067 16.4218 18.4093 12.8042H0.591064C1.93335 16.4218 5.41551 18.9999 9.50017 18.9999Z" fill="#D80027"/>
                                <path d="M2.45679 5.36963V10.1087C2.45679 12.8045 5.9785 13.6305 5.9785 13.6305C5.9785 13.6305 9.50018 12.8045 9.50018 10.1087V5.36963H2.45679Z" fill="white"/>
                                <path d="M3.28247 5.36963V10.1087C3.28247 10.4252 3.35276 10.7241 3.49199 11.0043H8.46416C8.6034 10.7241 8.67368 10.4252 8.67368 10.1087V5.36963H3.28247Z" fill="#D80027"/>
                                <path d="M7.63026 8.67404H6.39118V7.84795H7.21727V7.02186H6.39118V6.1958H5.56508V7.02186H4.73903V7.84795H5.56508V8.67404H4.32593V9.50013H5.56508V10.3262H6.39118V9.50013H7.63026V8.67404Z" fill="white"/>
                                <path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
                            </svg>
                            Slovakia
                        </li>
                    </ul>
                </div>
            </div>

            <div class="w-full h-px bg-white/25 mt-[2px] md:mt-[42px]"></div>

            <div class="text-center text-xs md:text-sm leading-[35px] font-light text-white/50 pt-[42px] md:pt-[38px]  pb-[52px] md:pb-[47px]">Copyright © 2024 Kaufino. <span class="font-medium text-white">Všechna práva vyhrazena.</span></div>
        </div>
    </div>
</div>

    <script src="{$basePath}/js/swiper/swiper-bundle.min.js"></script>
</body>
</html>

<script>
	function toggleModal() {
		var modal = document.getElementById('select-city-modal');
		var overlay = document.getElementById('overlay');
		if (modal.classList.contains('hidden')) {
			modal.classList.remove('hidden');
			overlay.classList.remove('hidden');
		} else {
			modal.classList.add('hidden');
			overlay.classList.add('hidden');
		}
	}

	function hideModal() {
		var modal = document.getElementById('select-city-modal');
		var overlay = document.getElementById('overlay');
        modal.classList.add('hidden');
        overlay.classList.add('hidden');
    }

    const menuButton = document.getElementById('menuButton');
    const overlay = document.getElementById('sidebar-overlay');
    const sidebar = document.getElementById('sidebar');
    const menuIcon = document.getElementById('menuIcon');
    const closeIcon = document.getElementById('closeIcon');

    function openSidebar() {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
        menuIcon.classList.add('hidden');
        closeIcon.classList.remove('hidden');
    }

    function closeSidebar() {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
        menuIcon.classList.remove('hidden');
        closeIcon.classList.add('hidden');
    }

    menuButton.addEventListener('click', () => {
        if (sidebar.classList.contains('translate-x-full')) {
            openSidebar();
        } else {
            closeSidebar();
        }
    });

		overlay.addEventListener('click', closeSidebar);

/*		// TABS
		function showTab(tabNumber) {
			var tabContents = document.querySelectorAll('.tab-content');
			tabContents.forEach(function(content) {
				content.classList.add('hidden');
				content.classList.remove('block');
			});

			var tabs = document.querySelectorAll('.tab-item');
			tabs.forEach(function(tab) {
				tab.classList.remove('tabs-active');
			});

			document.getElementById('tab-content-' + tabNumber).classList.add('block');
			document.getElementById('tab-content-' + tabNumber).classList.remove('hidden');

			tabs[tabNumber - 1].classList.add('tabs-active');
		}
		showTab(1);*/

		// ToggleInfo
		function toggleContent(element) {
			var content = element.querySelector('.content');
			var iconPlus = element.querySelector('.icon-plus');
			var iconMinus = element.querySelector('.icon-minus');

			content.classList.toggle('hidden');
			element.classList.toggle('active');

			iconPlus.classList.toggle('hidden');
			iconMinus.classList.toggle('hidden');
		}

		// SWIPER SHOPS
		const swiper = new Swiper('.swiper', {
			spaceBetween: 36,
			direction: 'horizontal',
			navigation: {
				nextEl: '.custom-next',
				prevEl: '.custom-prev',
			},
            breakpoints: {
				1024: {
                    slidesPerView: 7,
                },
                768: {
	                spaceBetween: 18,
                    slidesPerView: 5,
                },
                300: {
	                spaceBetween: 18,
                    slidesPerView: 3.5,
                },
            },
			on: {
				init: function () {
					updateButtonStates(this);
				},
				slideChange: function () {
					updateButtonStates(this);
				}
			}
		});

		function updateButtonStates(swiper) {
			const prevButton = document.querySelector('.custom-prev');
			const nextButton = document.querySelector('.custom-next');

			if (prevButton) {
				if (swiper.isBeginning) {
					prevButton.classList.add('swiper-button-disabled');
					toggleSvgVisibility(prevButton, true);
				} else {
					prevButton.classList.remove('swiper-button-disabled');
					toggleSvgVisibility(prevButton, false);
				}
			}

			if (nextButton) {
				if (swiper.isEnd) {
					nextButton.classList.add('swiper-button-disabled');
					toggleSvgVisibility(nextButton, true);
				} else {
					nextButton.classList.remove('swiper-button-disabled');
					toggleSvgVisibility(nextButton, false);
				}
			}
		}

		function toggleSvgVisibility(button, isDisabled) {
			const activeSvg = button.querySelector('.active-svg');
			const disabledSvg = button.querySelector('.disabled-svg');

			if (isDisabled) {
				activeSvg.classList.add('hidden');
				disabledSvg.classList.remove('hidden');
			} else {
				activeSvg.classList.remove('hidden');
				disabledSvg.classList.add('hidden');
			}
		}

		// Footer dropdown
		document.addEventListener('DOMContentLoaded', function() {
			const dropdownToggle = document.getElementById('dropdownToggle');
			const dropdownMenu = document.getElementById('dropdownMenu');
			const dropdownArrow = document.getElementById('dropdownArrow');

			dropdownToggle.addEventListener('click', function() {
				dropdownMenu.classList.toggle('hidden');
				dropdownArrow.classList.toggle('rotate-180');
			});

			// Close dropdown if clicking outside of it
			window.addEventListener('click', function(e) {
				if (!document.getElementById('dropdown').contains(e.target)) {
					dropdownMenu.classList.add('hidden');
					dropdownArrow.classList.remove('rotate-180');
				}
			});
		});

		document.addEventListener('DOMContentLoaded', function() {
			const footerSections = document.querySelectorAll('.mobile-footer');

			footerSections.forEach(footerSection => {
				footerSection.addEventListener('click', function() {
					const list = this.nextElementSibling;
					const svgs = this.querySelectorAll('.footer-arrow');

					if (window.innerWidth < 768) {
						list.classList.toggle('hidden');
						svgs.forEach(svg => {
							svg.classList.toggle('rotate-180');
						});
					}
				});
			});
		});


		// Search modal
		document.getElementById('search-input-shops').addEventListener('input', function() {
			var searchModal = document.getElementById('search-modal');
			var overlaySearch = document.getElementById('overlay-search-modal');
			if (this.value.length > 0) {
				searchModal.classList.remove('hidden');
				overlaySearch.classList.remove('hidden');
			} else {
				searchModal.classList.add('hidden');
				overlaySearch.classList.add('hidden');
			}
		});
		document.getElementById('overlay-search-modal').addEventListener('click', function() {
			var searchModal = document.getElementById('search-modal');
			var overlaySearch = document.getElementById('overlay-search-modal');
			var searchInput = document.getElementById('search-input-shops');

			searchModal.classList.add('hidden');
			overlaySearch.classList.add('hidden');
			searchInput.value = '';
		});
</script>

<script n:syntax=off>
    // Zvyraznenie hľadaného textu v zozname miest + regex
	function cleanString(str) {
		return str
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "")
			.replace(/[^a-z0-9]/gi, '');
	}
	function createDiacriticInsensitiveRegex(str) {
		const diacriticMap = {
			'a': '[aáä]', 'c': '[cč]', 'd': '[dď]', 'e': '[eéě]', 'i': '[ií]',
			'l': '[lĺľ]', 'n': '[nň]', 'o': '[oóô]', 'r': '[rŕř]', 's': '[sš]',
			't': '[tť]', 'u': '[uúů]', 'y': '[yý]', 'z': '[zž]'
		};
		return str.split('').map(char => diacriticMap[char.toLowerCase()] || char).join('');
	}
	document.getElementById("select-city-input").addEventListener('input', function() {
		const searchTerm = this.value.toLowerCase();
		const cleanSearchTerm = cleanString(searchTerm);
		const results = document.querySelectorAll('.result-item .city-name');

		results.forEach(result => {
			const cityName = result.textContent;
			const cleanCityName = cleanString(cityName.toLowerCase());

			if (cleanCityName.includes(cleanSearchTerm)) {
				const regex = new RegExp(createDiacriticInsensitiveRegex(searchTerm), 'gi');
				const highlightedName = cityName.replace(regex, match => `<span style="color: #BC2026; font-weight: bold;">${match}</span>`);
				result.innerHTML = highlightedName;
				result.closest('.result-item').style.display = 'flex';
			} else {
				result.innerHTML = cityName;
				result.closest('.result-item').style.display = 'none';
			}
		});
	});
</script>
