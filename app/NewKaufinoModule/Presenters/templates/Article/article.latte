{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Article',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}{if $pageExtension && $pageExtension->getTitle()}{$pageExtension->getTitle()}{else}{$article->getName()}{/if}{/block}
{block description}{if $pageExtension && $pageExtension->getDescription()}{$pageExtension->getDescription()}{else}{$article->getShortDescription()}{/if}{/block}

{block image}{if $article->getImageUrl()}{$article->getImageUrl() |image:700,350}{/if}{/block}

{block scripts}
    {include parent}

    {var $link = '"' . $presenter->link('//this') . '"'}

    <!-- Article schema -->
	<script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Article",
        "headline": {$article->getName()},
        "url": {$link|noescape},
        "author": {
        	"@type": "Person",
        	"name": "Kaufino"
        },
        "datePublished": {$article->getPublishedAt()|localDate:'long'},
        "dateModified": {$article->getPublishedAt()|localDate:'long'},
        "publisher": {
        	"@type": "Organization",
        	"name": "Kaufino"
        },
        "image": {
        	"@type": "ImageObject",
        	"width": "700",
        	"height": "350",
        	"url": {if $article->getImageUrl()}{$article->getImageUrl() |image:700,350}{/if}
        }
    }
  </script>
{/block}

{block content}
	<div class="container mw-700">        
		<h1 class="k__title mt-5 mb-3 fw-700">{$article->getName()}</h1>	
		
        <div class="d-flex align-items-center mt-3 mb-3">
            <strong n:if="$author === null">Kaufino</strong>
            <a n:href="Articles:author $author->getSlug()" n:if="$author"><strong>{$author->getFullName()}</strong></a>
            <span class="mx-3">|</span>
            <span>{$article->getPublishedAt()|localDate:'long'}</span>
        </div>

        <div class="color-grey fz-m lh-15 mb-3">
            {$article->getShortDescription()}
        </div>
                                
        <img n:if="$article->getImageUrl()" src="{$article->getImageUrl() |image:700,350}" width="700" height="350" alt="{$article->getName()}" class="img-responsive mt-4 mb-4" loading="lazy">                            

        <div class="k-content">
            {cache md5($article->getContent()), expire => '20 minutes'}
                {$article->getContent()|content|noescape}
            {/cache}        
        </div>
    </div>

    <div class="container" n:if="$articles">
        <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">
            {_kaufino.articles.otherArticles}
        </h2>

        {include "../components/article-list.latte", articles => $articles}
    </div>

    {if $user->isLoggedIn() && count($leaflets) > 0}
        <div class="container">
            <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.leaflets.title}</h2>            
            <div class="k-leaflets__wrapper">
                {foreach $leaflets as $leaflet}
                    {include '../components/leaflet.latte', leaflet => $leaflet}
                {/foreach}
            </div>            
        </div>
    {/if}
{/block}
