{block title}{_kaufino.tag.metaTitle, [brand => $tag->getName()]}{/block}
{block description}{_kaufino.tag.text, [brand => $tag->getName()]}{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">
        <div class="leaflet__content">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {_kaufino.tag.title, [brand => $tag->getName()]}
                        </h1>
						<p class="page-header__text ml-0">
                            {_kaufino.tag.text, [brand => $tag->getName()]}
                        </p>
					</div>
				</div>

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">
                        {foreach $leaflets as $leaflet}
                            {include '../components/leaflet.latte', leaflet => $leaflet}
                        {/foreach}
                    </div>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}

                <div n:if="count($shops) > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.tag.otherShops}</h2>
                    <div class="k-shop">
                        {foreach $shops as $shop}
                            {include '../components/shop-logo.latte', shop => $shop}
                        {/foreach}
                    </div>
                </div>

                <div class="k-content">
                    {cache md5($tag->getDescription()), expire => '20 minutes'}
                            {$tag->getDescription()|content|noescape}
                    {/cache}
                </div>
            </div>
        </div>

    </div>

	<div class="float-wrapper__stop"></div>
</div>
