{block head}
    {include parent}
    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block scripts}
    {include parent}
{/block}

{block description}{_kaufino.shops.metaDescription}{/block}
{block title}{_kaufino.shops.metaTitle}{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {var $localizedUrl = $getLocalizedUrl($otherWebsite, 'shops')}
        <link rel="alternate" n:attr="hreflang: $otherWebsite !== $website ? $otherWebsite->getLocalization()->getFullLocale('-') : 'x-default'" href={$localizedUrl} />
    {/foreach}
{/block}

{block content}
<div class="container">
    <div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-[45px]">
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
    </div>

    <div class="swiper k-hp-swiper mt-[27px] mb-[22px] md:mt-0 md:mb-[50px]" style="mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0) 101%)" >
        <div class="swiper-wrapper">
            {for $i = 1; $i <= 20; $i++}
            <a title="Akční letáky Billa" class="swiper-slide" href="/cz/billa" style="width: 94.2px; margin-right: 16px;" role="group" aria-label="6 / 18">
                <picture class="flex justify-center items-center rounded-full w-[64px] h-[64px] md:w-[90px] md:h-[90px] shadow-md transition-shadow duration-200 ease-in-out border-2 border-primary overflow-hidden">
                    <source srcset="
                                https://n.klmcdn.com/zoh4eiLi/IMG/7200/VeIV62vZbj_gqWb_fgWDu_uO1jnU2aFA4V-dNg59VmU/resize:fit:80:70:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.webp 1x,
                                https://n.klmcdn.com/zoh4eiLi/IMG/7200/vVpF0ei85Zi0U9rxYprjnXy2xIJQwNlNZAfhUncGa3U/resize:fit:160:140:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.webp 2x
                            " type="image/webp">
                    <img
                            src="/images/placeholder-80x70.png"
                            srcset="https://n.klmcdn.com/zoh4eiLi/IMG/7200/T6OoNWEunzq05fp__4bF6R5pn196MC3J5u7JqmIDS7s/resize:fit:80:70:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.png 1x,
                                https://n.klmcdn.com/zoh4eiLi/IMG/7200/VvR89-IAYXwqgrvzVHLWDBfaojuHf-J-L-X18iaJNII/resize:fit:160:140:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9iaWxsYS05NzIucG5n.png 2x"
                            data-sizes="auto"
                            width="80"
                            height="70"
                            alt="Billa"
                            class="max-h-[40px] max-w-[40px] md:max-w-[50px]"
                            loading="lazy"
                    >
                </picture>

            </a>
            {/for}
        </div>
    </div>

    <div class="mb-6">
        <h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">{_kaufino.shops.title}</h1>
        <div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>
            Obchody
        </div>
        <p class="text-sm font-light leading-[22px] text-[#646C7C]">{_kaufino.shops.text}</p>
    </div>

    <div class="grid grid-cols-3 md:grid-cols-5 gap-x-[7px] gap-y-[13px] md:gap-x-3 md:gap-y-5 mb-10">
        {for $i = 1; $i <= 20; $i++}
            <div class="bg-light-6 pt-2 pb-[9px] md:pb-[17px] px-2 rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
                <div class="flex justify-center items-center bg-white px-2 py-10 md:py-20 mb-[7px] md:mb-[17px] rounded-lg">
                    <img src="https://www.tipli.cz/upload/images/shops-shop-logo/788597.svg" alt="">
                </div>
                <div class="md:px-2">
                    <div class="hidden md:inline text-xs font-light px-2 py-1 border border-light-3 rounded">Obchod</div>
                    <div class="text-sm md:text-base uppercase leading-7 md:mt-2.5">pepco</div>
                </div>
            </div>
        {/for}
    </div>

    <div class="k-shop mb-6">
        {foreach $storeShops as $shop}
            {include '../components/shop-logo.latte', shop => $shop}
        {/foreach}
    </div>

    {if $onlineShops}
        <div class="mb-6">
            <h2 class="k__title ta-center">{_kaufino.shops.otherShops.title}</h2>
        </div>

        <div class="k-shop">
            {foreach $onlineShops as $shop}
                {include '../components/shop-logo.latte', shop => $shop}
            {/foreach}
        </div>
    {/if}
</div>

<script type="module">
    import Swiper from 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.mjs'

    var swipperElm = document.querySelector(".k-hp-swiper");

    if (swipperElm) {
        const swiper = new Swiper(swipperElm, {
            slidesPerView: 4.5,
            spaceBetween: 12,

            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
                disabledClass: "swiper-button-disabled",
            },
            breakpoints: {
                1050: {
                    slidesPerView: 10.5,
                    sliedesPerGroup: 5,
                    spaceBetween: 16,
                },
                675: {
                    slidesPerView: 6,
                },
                850: {
                    slidesPerView: 8,
                },
                420: {
                    slidesPerView: 5.3,
                },
            },
        });
    }
</script>
