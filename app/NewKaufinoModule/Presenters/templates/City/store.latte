{block head}
    {include parent}

    <!-- Leaflet -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'CityStore',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.city.store.title, [brand => $shop->getName(), address => $store->getFullStreet(), city => $city->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.city.store.description, [brand => $shop->getName(), address => $store->getFullStreet(), city => $city->getName()]}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container mt-4">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a>  |
            <a n:href="City:city $city" class="link">{$city->getName()}</a> |
            <a n:href="City:shop $city, $shop" class="link">{$shop->getName()}</a>  |
            <span class="color-grey">{$store->getStreet()}</span>
        </p>
    </div>
{/block}

{define cities}
    <div n:if="count($cities) > 0" class="nearest-store-branch">
        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.stores, [brand => $shop->getName()]}</h2>

        <p class="k-tag k-tag--4 mb-5">
            {foreach $cities as $cityItem}
                <span class="k-tag__inner {$iterator->counter > 4 ? 'hidden' : ''}">
                    <a n:href="City:shop $cityItem, $shop" class="k-tag__item">{$shop->getName()} {$cityItem->getName()}</a>
                </span>
            {/foreach}
        </p>

        <p n:if="count($cities) > 3" class="d-flex mt-3 mb-5">
            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
        </p>
    </div>
{/define}

{block content}
{capture $otherStores |spaceless|trim}
    {foreach $nearestStores as $nearesStore}
        <a n:href="City:store $nearesStore->getCity(), $shop, $nearesStore">{_'kaufino.city.store.store', [fullAddress => $nearesStore->getFullAddress()] |replace: ' ', '&nbsp'|noescape}</a>

        {if $iterator->counter === 1}
            {if count($nearestStores) > 1}
                {_'kaufino.city.store.or'}
            {else}
                {_'kaufino.city.store.others'}
            {/if}
        {/if}

        {breakIf $iterator->counter > 1}
    {/foreach}
{/capture}

<div class="container">
    <div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-10">
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
    </div>

    <div class="flex items-center gap-[14px] text-sm py-6 md:hidden">
        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
            <path d="M14 6.49405L2.0836 6.50178M6.41308 12L0.999999 6.49397L6.40136 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Zpět na všechny poboočky
    </div>

    <div class="flex flex-row items-center md:items-start gap-[15px] mb-[34px]">
        <img class="w-[60px] h-[60px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

        <div>
            <div class="text-[24px] font-medium leading-[34px]">Lidl letáky <br> <span class="md:hidden">Českomoravská 930</span></div>
            <div class="hidden md:flex gap-[9px] items-center font-light text-sm text-[#646C7C]">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                    <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                    <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                </svg>
                Obchody
                <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                    <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                </svg>
                Hypermarkety a supermarkety
                <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                    <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                </svg>
                Lidl letáky
            </div>
        </div>
    </div>

    <div class="w-full mb-[38px]">
        <iframe class="w-full rounded-xl max-h-[279px]"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d41716.64084449754!2d16.55961998042657!3d49.19505696287829!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x471295d6ccf03d57%3A0x468363fa122d25fc!2sLidl!5e0!3m2!1ssk!2ssk&zoomControl=0&scaleControl=0&mapTypeControl=0&streetViewControl=0&fullscreenControl=0&disableDefaultUI=1"
                height="450"
                style="border:0;"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade">
        </iframe>
    </div>

    <div class="flex flex-col md:flex-row gap-[34px] md:gap-[60px] items-center">
        <div class="w-full max-w-[549px] md:border rounded-xl md:p-[30px] border-[#F0F0F0]">
            <div class="text-lg md:text-[26px] font-medium leading-[31.5px] md:leading-[39px] mb-2">Otevírací doba prodejny</div>
            <div class="flex gap-2 mb-[33px]">
                <div class="bg-green text-xs text-white flex items-center h-[22px] px-2 rounded">Aktuálne otevreno</div>
                <div class="text-xs text-[#080B10] border border-light-3 font-light flex items-center h-[22px] px-2 rounded">Zavírá za 8 hodin</div>
            </div>

            <div>
                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Pondeli <span class="ml-2.5 text-xs md:hidden border border-light-3 px-2 py-1 rounded">Dnes</span></div>
                    <div class="font-light">Otevřeno od <span class="font-medium">8:00</span> do <span class="font-medium">22:00</span></div>
                </div>

                <div class="h-px my-3 w-full bg-black/10"></div>

                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Úterý</div>
                    <div class="font-light">Otevřeno od <span class="font-medium">8:00</span> do <span class="font-medium">22:00</span></div>
                </div>

                <div class="h-px my-3 w-full bg-black/10"></div>

                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Středa</div>
                    <div class="font-light">Otevřeno od <span class="font-medium">8:00</span> do <span class="font-medium">22:00</span></div>
                </div>

                <div class="h-px my-3 w-full bg-black/10"></div>

                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Čtvrtek</div>
                    <div class="font-light">Otevřeno od <span class="font-medium">8:00</span> do <span class="font-medium">22:00</span></div>
                </div>

                <div class="h-px my-3 w-full bg-black/10"></div>

                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Pátek</div>
                    <div class="font-light">Otevřeno od <span class="font-medium">8:00</span> do <span class="font-medium">22:00</span></div>
                </div>

                <div class="h-px my-3 w-full bg-black/10"></div>

                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Sobota</div>
                    <div class="font-light">Otevřeno od <span class="font-medium">8:00</span> do <span class="font-medium">22:00</span></div>
                </div>

                <div class="h-px my-3 w-full bg-black/10"></div>

                <div class="flex items-center justify-between">
                    <div class="font-medium leading-7">Neděle</div>
                    <div>Zavřeno</div>
                </div>
            </div>
        </div>

        <div class="w-full">
            <div class="md:mb-[49px] w-full">
                <div class="text-lg md:text-[22px] font-medium leading-[31.5px] md:leading-[39px] mb-[19px] md:mb-2">Detaily prodejny</div>

                <div class="flex flex-col gap-[18px]">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4 font-light">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Českomoravská 930, 19000 Praha
                        </div>

                        <div class="hidden md:flex items-center gap-[7px] font-medium underline cursor-pointer">
                            Navigovat
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                <path d="M3.83357 13.0434L12.2734 4.60352" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.0433 11.5089L13.0367 3.82971L5.38404 3.8232" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4 font-light">
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                <path d="M18 9.5H9.4623M18 9.5L14.8264 6.28889M18 9.5L14.8264 12.7111M1.5387 15.3556C1.91647 16.9422 3.27646 18 4.86311 18H5.54311C6.14755 18 6.67643 17.4711 6.67643 16.8667V13.9956C6.67643 13.3911 6.14755 12.8622 5.54311 12.8622C4.93867 12.8622 4.40978 12.3333 4.40978 11.7289V7.19556C4.40978 6.59111 4.93867 6.06222 5.54311 6.06222C6.14755 6.06222 6.67643 5.53333 6.67643 4.92889V2.13333C6.67643 1.52889 6.14755 1 5.54311 1H4.86311C3.27646 1 1.91647 2.13333 1.5387 3.64444C0.858708 6.96889 0.783153 11.9556 1.5387 15.3556Z" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            +420 210 023 400
                        </div>

                        <div class="hidden md:flex items-center gap-[7px] font-medium underline cursor-pointer">
                            Telefonovat
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                <path d="M3.83357 13.0434L12.2734 4.60352" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.0433 11.5089L13.0367 3.82971L5.38404 3.8232" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4 font-light">
                            <svg xmlns="http://www.w3.org/2000/svg" width="19" height="16" viewBox="0 0 19 16" fill="none">
                                <path d="M16.6923 1H2.30769C1.58547 1 1 1.58548 1 2.30769V13.4231C1 14.1453 1.58547 14.7308 2.30769 14.7308H16.6923C17.4145 14.7308 18 14.1453 18 13.4231V2.30769C18 1.58548 17.4145 1 16.6923 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 2.63489L8.66308 7.24351C8.89806 7.38146 9.1942 7.457 9.5 7.457C9.8058 7.457 10.1019 7.38146 10.3369 7.24351L18 2.63489" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <EMAIL>
                        </div>

                        <div class="hidden md:flex items-center gap-[7px] font-medium underline cursor-pointer">
                            Napsat e-mail
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                <path d="M3.83357 13.0434L12.2734 4.60352" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M13.0433 11.5089L13.0367 3.82971L5.38404 3.8232" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="hidden md:block relative w-full">
                <div class="text-[22px] font-medium leading-[39px] mb-2">Nejbližší prodejny</div>

                <div class="flex flex-col gap-3">
                    {for $i = 1; $i <= 6 ; $i++}
                    <div class="flex items-center ">
                        <svg class="mr-4" xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                            <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="underline"><span class="font-light">Lidl</span> Vinohradská 543</span> <span class="mx-1">|</span> <span class="text-primary leading-7 font-medium">2,3 km</span>
                    </div>
                    {/for}
                </div>

                <div class="absolute right-0 bottom-0 flex items-center gap-[7px] underline cursor-pointer">
                    Zobrazit více prodejen
                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                        <path d="M3.83357 13.0434L12.2734 4.60352" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.0433 11.5089L13.0367 3.82971L5.38404 3.8232" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="bg-light-6">
    <div class="container py-6 md:py-[44px] mt-10 md:mt-[74px]">
        <div class="mb-10 md:mb-[106px]">
            <div class="flex flex-col md:flex-row md:items-center md:gap-[15px] mb-[18px] md:mb-8">
                <img class="hidden md:block w-[47px] h-[47px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

                <div>
                    <div class="text-[20px] md:text-[26px] font-medium leading-[35px] md:leading-[39px]">Aktuální letáky z obchodu Lidl</div>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-5">
                <div class="col-span-2 p-1.5 md:p-2 bg-green-light rounded-xl">
                    <div class="flex mb-[13px] mb-[17px] relative" style="position: relative;">
                        <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                        <div class="w-full relative">
                            <img class="w-full border rounded-l-lg max-h-[270px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/kbfrh3M8jRHgFUfQSBaxI3CSf9fhrfrgMP6SDWPdgag/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU1LzI1NTYxMS8wenJhN3RnODA3Zjk1YjZ0cjZlNnYwOHYuanBn.webp" alt="">
                        </div>
                        <div class="w-full relative">
                            <img class="w-full border rounded-r-lg max-h-[270px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/dGMHy1WRxt5crdYsfdnhMC8XUsQ057p76w0KNfUhR1w/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQyNC81MTMzNjIxMzZmOTMxNTFkLmhybTE1c3ZucmYzMS5qcGc.webp" alt="">
                        </div>
                        <div class="flex gap-1 absolute bottom-1 right-1 z-10">
                            <button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-green py-[3px] px-[7px] md:py-1 md:px-2.5">Aktuální leták</button>
                            <button class="rounded-md flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] py-[3px] px-[7px] md:py-1 md:px-2.5 gap-2">
                                Otevřít
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center gap-[15px] pl-2 pb-[9px]">
                        <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                        <div>
                            <div class="text-sm md:text-lg font-medium">Lidl</div>
                            <div class="text-xs font-light">04.04. - 07.04.2024</div>
                        </div>
                    </div>
                </div>

                {for $i = 1; $i <= 3 ; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                    <div class="p-1.5 md:p-2 bg-orange-light rounded-xl h-full">
                        <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                            <div class="w-full relative">
                                <img class="rounded-lg w-full" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/kbfrh3M8jRHgFUfQSBaxI3CSf9fhrfrgMP6SDWPdgag/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU1LzI1NTYxMS8wenJhN3RnODA3Zjk1YjZ0cjZlNnYwOHYuanBn.webp" alt="letak">
                            </div>
                            <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                                <button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-orange py-1 px-2.5">Nový leták</button>
                                <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                    Otevřít
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                            <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                            <div class="leading-[21px]">
                                <div class="text-xs md:text-lg font-medium">Lidl</div>
                                <div class="text-xs font-light">04.04. - 07.04.2024</div>
                            </div>
                        </div>
                    </div>
                </div>
                {/for}
            </div>

            <div class="flex justify-center mt-[32px]">
                <button class="flex justify-center items-center text-white gap-[9px] py-[17px] w-full max-w-[325px] bg-primary rounded-xl hover:bg-primary-hover transition duration-200">
                    Všechny letáky
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                        <path d="M4.43611 13.9086L12.876 5.46875" stroke="white" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M13.6439 12.3741L13.6373 4.69494L5.98463 4.68844" stroke="white" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div>
            <div class="flex flex-col md:flex-row md:items-center md:gap-[15px] mb-8">
                <img class="hidden md:block w-[47px] h-[47px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

                <div>
                    <div class="text-[20px] md:text-[26px] font-medium leading-[35px] md:leading-[39px]">Akční zboží z letáků Lidl Praha</div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3">
                {for $i = 1; $i <= 5 ; $i++}
                    <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                        <div class="p-1.5 md:p-2 bg-white rounded-xl">
                            <div class="flex mb-[14px] md:mb-[17px] relative">
                                <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                                <div class="w-full">
                                    <img class="w-full cover max-h-[231px] rounded-lg" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/sq0HjsGeRgZ9MBEFAzuTmYnNp9rOoqw3eDosAFaSDuw/resize:fit:150:150:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvb2ZmZXJzL2ltYWdlLzY3N2VlNjcyY2VjOWEtODY2LmpwZw.webp" alt="letak">
                                </div>
                                <div class="absolute bottom-1 right-1 z-10">
                                    <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                        Otevřít
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
                                <div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
                                <div>
                                    <div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
                                    <div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
                                </div>
                            </div>
                        </div>
                    </div>
                {/for}
            </div>

            <div class="flex justify-center mt-[32px]">
                <button class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
                    Všechno zboží v akci
                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                        <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container py-12">
    <p class="font-light">Výhodné akce a slevy na rozmanitý sortiment pravidelně nabízí prodejna Lidl Tupolevova 722, kde lze při nákupu využít oblíbený Lidl leták. Ten si mohou zákazníci pohodlně prohlédnout online, stejně jako akce dostupné na pobočkách Lidl Kbelská 919 nebo Lidl Střížkovská 8.</p>

    <div class="my-3 text-[20px] leading-[35px] font-medium">Lidl leták Praha Tupolevova 722</div>

    <p class="font-light">Prodejna Lidl Tupolevova 722 zákazníkům nabízí nejen širokou nabídku zboží, ale také nízké ceny, o nichž pravidelně informuje Lidl leták. Pobočka Lidl Praha Tupolevova 722 je oblíbeným místem těch, kteří hledají cenově výhodné nabídky. Díky tomu, že je Lidl leták dostupný online, mají kupující aktuální slevy vždy při ruce. Pokud Lidl Tupolevova 722 nenabízí vše, co zákazník potřebuje, může využít také další obchody v okolí, jako jsou:

        Zjistěte, jaká je přesná adresa, kontakt na zákaznickou linku nebo otevírací doba oblíbených obchodů přehledně na jednom místě. Nechybí ani informace o tom, jaké pobočky se nachází ve vašem okolí a kde se dají využít další výhodné nabídky, o nichž informují akční letáky vybraných obchodů.</p>

    <div class="md:hidden w-full mt-10">
        <div class="text-[22px] font-medium leading-[39px] mb-2">Nejbližší prodejny</div>

        <div class="flex flex-col gap-3">
            {for $i = 1; $i <= 6 ; $i++}
            <div class="flex items-center ">
                <svg class="mr-4" xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                    <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="underline"><span class="font-light">Lidl</span> Vinohradská 543</span> <span class="mx-1">|</span> <span class="text-primary leading-7 font-medium">2,3 km</span>
            </div>
            {/for}
        </div>
    </div>
</div>

<div class="bg-light-6">
    <div class="container py-11">
        <div class="flex relative justify-between md:gap-[5px] leading-[35px] md:leading-[39px] text-[20px] md:text-[26px] font-medium mb-4 mb-[28px] md:mb-[37px]">
            <div class="flex items-center">
                Oblíbené obchody
            </div>

            <div class="hidden md:flex gap-[60px]">
                <div class="swiper-button-prev favorite-shops"></div>
                <div class="swiper-button-next favorite-shops"></div>
            </div>
        </div>

        <div class="swiper favorite-shops -mr-5">
            <div class="swiper-wrapper">
                {for $i = 1; $i <= 8; $i++}
                <div class="swiper-slide">
                    <div class="hidden md:block">
                        <svg width="141" height="141" viewBox="0 0 141 141" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M128.286 0H12C5.37258 0 0 5.37259 0 12V128.286C0 134.913 5.37259 140.286 12 140.286H128.286C134.913 140.286 140.286 134.913 140.286 128.286V12C140.286 5.37258 134.913 0 128.286 0Z" fill="#CD1414"/>
                            <path d="M131.52 77.3539C131.52 78.3552 131.223 79.3341 130.667 80.1667C130.111 80.9993 129.32 81.6482 128.395 82.0314C127.47 82.4146 126.452 82.5148 125.47 82.3195C124.488 82.1241 123.585 81.6419 122.877 80.9339C122.169 80.2258 121.687 79.3237 121.492 78.3416C121.296 77.3595 121.397 76.3415 121.78 75.4164C122.163 74.4913 122.812 73.7006 123.645 73.1443C124.477 72.5879 125.456 72.291 126.457 72.291C127.8 72.291 129.088 72.8244 130.037 73.7739C130.987 74.7234 131.52 76.0111 131.52 77.3539Z" fill="#FFD200"/>
                            <path d="M19.3011 69.6978H21.1817C23.3197 69.6978 24.6164 68.3764 24.849 66.7283C25.0469 65.308 24.0571 63.7589 22.0429 63.7589H20.2365L19.3011 69.6978ZM114.456 66.2681L118.742 58.2308H128.596L113.749 81.7685H103.677L108.443 75.0526L102.999 58.7208L99.2876 81.7586H91.4087L86.5686 70.8707L84.8166 81.7586H76.9328L80.7237 58.2209H88.8154L93.5071 68.7525L95.2047 58.2209H112.437L114.456 66.2681ZM77.6108 58.2308L73.8198 81.7685H65.9112L61.0711 70.8806L59.3191 81.7685H51.4353L55.2262 58.2308H63.3179L68.0047 68.7624L69.7022 58.2308H77.6108ZM24.3095 57.8745C29.7535 57.8745 33.4504 60.8439 33.4851 65.5752L34.6432 58.2259H52.4944L51.5343 64.1647H42.1311L41.6609 66.9857H50.1881L49.2379 72.8701H40.6711L40.1762 75.795H49.7773L48.8172 81.7338H30.8571L32.911 68.99C31.5302 73.1175 27.764 75.5475 22.8347 75.5475H18.3113L17.3215 81.7338H8.76953L12.6199 57.8398L24.3095 57.8745Z" fill="white"/>
                        </svg>
                    </div>

                    <div class="md:hidden">
                        <svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M75.9973 0H12C5.37258 0 0 5.37258 0 12V75.9973C0 82.6247 5.37258 87.9973 12 87.9973H75.9973C82.6247 87.9973 87.9973 82.6247 87.9973 75.9973V12C87.9973 5.37258 82.6247 0 75.9973 0Z" fill="#CD1414"/>
                            <path d="M82.4991 48.5215C82.4991 49.1496 82.3128 49.7636 81.9638 50.2859C81.6149 50.8081 81.1189 51.2152 80.5386 51.4556C79.9583 51.6959 79.3197 51.7588 78.7037 51.6363C78.0877 51.5137 77.5218 51.2113 77.0776 50.7671C76.6335 50.323 76.331 49.7571 76.2085 49.1411C76.0859 48.525 76.1488 47.8865 76.3892 47.3062C76.6296 46.7259 77.0366 46.2299 77.5589 45.8809C78.0811 45.532 78.6951 45.3457 79.3233 45.3457C80.1655 45.3457 80.9733 45.6803 81.5689 46.2759C82.1645 46.8715 82.4991 47.6792 82.4991 48.5215Z" fill="#FFD200"/>
                            <path d="M12.1071 43.7193H13.2868C14.6279 43.7193 15.4413 42.8904 15.5872 41.8566C15.7113 40.9657 15.0905 39.994 13.827 39.994H12.6939L12.1071 43.7193ZM71.7954 41.5679L74.4838 36.5264H80.6647L71.3515 51.2909H65.034L68.0236 47.0782L64.6087 36.8337L62.2804 51.2847H57.3382L54.3021 44.455L53.2032 51.2847H48.2579L50.6358 36.5202H55.7115L58.6545 43.1263L59.7193 36.5202H70.5288L71.7954 41.5679ZM48.6832 36.5264L46.3052 51.2909H41.3444L38.3083 44.4612L37.2093 51.2909H32.264L34.642 36.5264H39.7177L42.6575 43.1325L43.7223 36.5264H48.6832ZM15.2488 36.3029C18.6636 36.3029 20.9826 38.1655 21.0043 41.1333L21.7308 36.5233H32.9283L32.3261 40.2485H26.4277L26.1328 42.0181H31.4817L30.8857 45.7092H25.5119L25.2015 47.5439H31.224L30.6218 51.2692H19.3559L20.6442 43.2753C19.7781 45.8644 17.4157 47.3887 14.3237 47.3887H11.4863L10.8654 51.2692H5.50098L7.9162 36.2811L15.2488 36.3029Z" fill="white"/>
                        </svg>
                    </div>
                </div>
                {/for}
            </div>
        </div>

        <div class="h-px w-full my-10 bg-light-2"></div>

        <div class="text-[20px] md:text-[26px] font-medium leading-[35px] md:leading-[39px] mb-[26px] md:mb-10">Další letáky</div>

        <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            {for $i = 1; $i <= 11 ; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                <div class="p-1.5 md:p-2 bg-white rounded-xl">
                    <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                        <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                        <div class="w-full relative">
                            <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                        </div>
                        <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                            <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                Otevřít
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                        <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                        <div class="leading-[21px]">
                            <div class="text-xs md:text-lg font-medium">Lidl</div>
                            <div class="text-xs font-light">04.04. - 07.04.2024</div>
                        </div>
                    </div>
                </div>
            </div>
            {/for}
        </div>
    </div>
</div>

<div class="hidden md:block container py-10">
    <div class="flex items-center gap-4 mb-[32px]">
        <img class="w-[47px] h-[47px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
        <div class="text-[26px] font-medium leading-[39px]">Letáky Lidl najdete také v těchto zemích</div>
    </div>

    <div class="flex flex-wrap gap-1.5 md:gap-3">
        {foreach range(1, 17) as $i}
        <div class="transition-transform duration-200 transform hover:scale-[105%] cursor-pointer rounded-lg flex items-center gap-[5px] md:gap-[11px] p-1.5 md:p-3 text-xs md:text-sm leading-[21px] md:leading-[24.5px] font-light bg-light-6">
            <svg class="w-[15px] h-[15px] md:w-[33px] md:h-[33px]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path d="M10.1086 0.466494C9.29607 0.165029 8.41728 0 7.49987 0C6.58247 0 5.70368 0.165029 4.89119 0.466494L4.23901 7.5L4.89119 14.5335C5.70368 14.835 6.58247 15 7.49987 15C8.41728 15 9.29607 14.835 10.1086 14.5335L10.7607 7.5L10.1086 0.466494Z" fill="#FFDA44"/>
                <path d="M15.0002 7.50003C15.0002 4.27532 12.9649 1.52622 10.1089 0.466553V14.5336C12.9649 13.4738 15.0002 10.7248 15.0002 7.50003Z" fill="#D80027"/>
                <path d="M0 7.50003C0 10.7248 2.03531 13.4738 4.89132 14.5336V0.466553C2.03531 1.52622 0 4.27532 0 7.50003Z" fill="black"/>
            </svg>
            Belgium
        </div>
        {/foreach}
    </div>
</div>
<!-- KONIEC NOVEJ GRAFIKY -->

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">
        <div class="storeContent">
            <div class="storeContent__main">
                <div class="k-profile-header k-profile-header--sm-center my-0">
                    <a href="{link City:shop $city, $shop}" title="{_kaufino.city.shop.shopLeaflet, [brand => $shop->getName()]}" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:160,160,'fit','webp'} 2x
                                "
                                type="image/webp"
                            >
                            <img
                                src="{$basePath}/images/placeholder-80x70.png"
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80} 1x,
                                    {$shop->getLogoUrl() |image:160,160} 2x
                                "
                                width="80"
                                height="80"
                                alt="{$shop->getName()}"
                                class="k-profile-header__logo"
                            >
                        </picture>
                    </a>

                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.city.store.h1, [city => $city->getName(), brand => $shop->getName(), address => $store->getFullStreet()]}
                            {/if}
                        </h1>
                    </div>
                </div>

                <div class="storeInfo mt-5">
                    <div class="storeInfo__row storeInfo__storeHours closed">
                        <div class="storeInfo__container-hours" n:if="$openingHours = $store->getOpeningHours()">
                            <div class="storeInfo__icon color--green">
                                <span class="icon " data-svg="/assets/icons/time.svg?v=1700491612828"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0z"></path><path d="M16.3 3c.*******.5 1.3-.2.5-.8.7-1.3.5-1.1-.5-2.3-.8-3.5-.8-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8c0-1.7-.5-3.3-1.5-4.6-.3-.4-.2-1.1.2-1.4.4-.3 1.1-.2 1.4.2C21.3 7.9 22 9.9 22 12c0 5.5-4.5 10-10 10S2 17.5 2 12 6.5 2 12 2c1.5 0 3 .3 4.3 1zM13 12h2c.6 0 1 .4 1 1s-.4 1-1 1h-3c-.6 0-1-.4-1-1V8c0-.6.4-1 1-1s1 .4 1 1v4z"></path></svg></span>
                            </div>
                                <div class="storeInfo__hours">
                                    <div class="storeInfo__hours color--mid_grey" id="js-storeInfo">
                                        <ul class="storeHours">
                                            <li class="storeHours__item storeHours__header">
                                                <strong class="" n:if="$store->isOpen()">{_'kaufino.city.store.open'}</strong>
                                                <strong class="" n:if="$store->isOpen() === false">{_'kaufino.city.store.closed'}</strong>
                                            </li>
                                            {foreach $store->getDays() as $day => $dayAsNumber}
                                                <li class="storeHours__item {$dayAsNumber === (int) date('N') ? 'storeHours__item--active': 'storeHours__item--inactive'}">
                                                    <span class="storeHours__itemDay">{_app.day.$dayAsNumber.nominative |firstUpper}</span>
                                                    {if isset($openingHours[$day])}
                                                        <span class="storeHours__itemTime">
                                                            {foreach $openingHours[$day] as $hours}
                                                                {foreach $hours as $hour}{$hour}{sep} / {/sep}{/foreach}
                                                                {sep}, {/sep}
                                                            {/foreach}
                                                        </span>
                                                    {else}
                                                        {_'kaufino.city.store.closed' |lower}
                                                    {/if}
                                                </li>
                                            {/foreach}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        <div class="storeInfo__additional">
                            <div class="storeInfo__additional-address">
                                <a href="https://www.google.com/maps/search/{$shop->getName()}+{$store->getFullStreet()}+{$city->getName()}/@{$store->getLat()},{$store->getLng()},17z/?entry=ttu" target="_blank">
                                    <img src="{$basePath}/images/icons/place_black_24dp.svg" alt="address">
                                    <span>{$store->getAddress()}</span>
                                </a>
                            </div>
                            <div class="storeInfo__additional-phone" n:if="$store->getPhoneNumber()">
                                <a href="tel:{$store->getPhoneNumber()}">
                                    <img src="{$basePath}/images/icons/phone_black_24dp.svg" alt="phone">
                                    <span>{$store->getPhoneNumber()}</span>
                                </a>
                            </div>
                            <div class="storeInfo__additional-email" n:if="$store->getEmail()">
                                <a href="mailto:{$store->getEmail()}">
                                    <img src="{$basePath}/images/icons/email_black_24dp.svg" alt="email">
                                    <span>
                                        {$store->getEmail()}
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper k-leaflets__wrapper--3">
                        {foreach $leaflets as $leaflet}
                            {if false && $iterator->counter == 1}
                                <div class="k-leaflets__item mb-3">
                                    <!-- Vypis mesta - Responsive - 2 -->
                                    <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="2849716931" data-ad-format="square" data-full-width-responsive="true"></ins>

                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($leaflets) > 17" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.showMore.leaflets'} »</button>
                    </p>
                {/if}

                {if count($similarLeaflets) > 0}
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.leaflets}</h2>

                    <div class="k-leaflets__wrapper k-leaflets__wrapper--3">
                        {foreach $similarLeaflets as $leaflet}
                            {breakIf $iterator->counter > 6}
                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>
                {/if}

                {if $similarShops}
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.shops}</h2>
                    {* TODO - obchody v okoli *}
                    <p class="k-tag k-tag--4 mb-4">
                        {foreach $similarShops as $nearestShop}
                            {continueIf $nearestShop === $shop}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:shop $city, $nearestShop" class="k-tag__item">{$nearestShop->getName()} {$city->getName()}</a>
                            </span>
                        {/foreach}
                    </p>
                {/if}

                {include cities}

                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCity}
                    </h2>

                    <p class="k-tag k-tag--4 mb-4">
                        {foreach $nearestCities as $nearestCity}
                            <span class="k-tag__inner {$iterator->counter > 4 ? 'hidden'}">
                                <a n:href="City:city $nearestCity" class="k-tag__item">{$nearestCity->getName()}</a>
                            </span>
                        {/foreach}
                    </p>
                    <p n:if="count($nearestCities) > 3" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                        <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                    </p>
                </div>

                <div n:if="count($topOffers) > 0">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.shop.offers, ['brand' => $shop->getName(), 'city' => $city->getName()]}</h2>

                    <div class="k-offers k-offers--4">
                        {foreach $topOffers as $offer}
                            {breakIf $iterator->getCounter() > 4}
                            {include '../components/offer-item.latte', offer => $offer}
                        {/foreach}
                    </div>

                    <div class="d-flex mt-3 mb-5">
                        <a n:href="Offers:offers#offers" class="link ml-auto k-show-more-button">
                            {_'kaufino.showMore.offers'} »
                        </a>
                    </div>
                </div>

                <p class="k-profile-header__text ml-0">
                    {_kaufino.city.store.text, [brand => $shop->getName(), 'address' => $store->getFullStreet(), 'city' => $city->getName()]}

                    {if count($nearestStores) >= 1}
                        {_'kaufino.city.store.text2', [stores => $otherStores] |noescape}
                    {else}
                        {_'kaufino.city.store.text2WithoutStores'}
                    {/if}
                </p>

                <h2>{_kaufino.city.store.h2bottom, ['brand' => $shop->getName(), 'city' => $city->getName(), 'street' => $store->getFullStreet()]}</h2>

                <div class="k-content ml-0">
                    <p>{_kaufino.city.store.textBottom, ['street' => $store->getFullStreet(), brand => $shop->getName(), 'address' => $store->getFullStreet(), 'city' => $city->getName(), 'fullAddress' => $store->getFullAddress()]}</p>

                    <ul n:if="$similarShops">
                        {foreach $similarShops as $similarShop}
                            <li>
                                <a n:href="City:shop $city, $similarShop">{$similarShop->getName()} {$city->getName()}</a>
                            </li>

                            {breakIf $iterator->counter > 2}
                        {/foreach}
                    </ul>

                    <p>
                        {_kaufino.city.store.textBottom2}
                    </p>

                    {if $pageExtension && $pageExtension->getLongDescription()}
                        <div>{$pageExtension->getLongDescription()}</div>
                    {/if}
                </div>
            </div>

            <div class="storeContent__storeList">
                <h2 class="fz-l fw-regular mt-0 mb-3 px-3 px-lg-0">
                    {_'kaufino.city.store.h2', [brand => $shop->getName(), city => $city->getName()]}
                </h2>
                <div id="map" class="storeContent__map" style="width: 100%; height: 396px;"></div>

                {* link to google maps *}
                <a style="display:none;" class="storeContent__link" href="https://www.google.com/maps/dir/?api=1&destination={$store->getLat()},{$store->getLng()}" target="_blank">
                   Google maps
                </a>

                {* telefon: $store->getPhoneNumber() *}
                {* email: $store->getEmail() *}

                <script n:syntax="double">
                    function renderMap(at, address) {
                        const map = L.map('map').setView(at, 14);
                        let marker = L.marker(at)
                            .bindPopup(address)
                            .addTo(map);

                        const tiles = L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            maxZoom: 19,
                            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
                        }).addTo(map);
                    }
                </script>
                <script>
                    renderMap(
                        [{$store->getLat()}, {$store->getLng()}],
                        {$store->getAddress()}
                    )
                </script>

                <ul class="storeList">
                    {foreach $nearestStores as $nearesStore}
                        <li class="storeList__item">
                            <div class="storeItem">
                                {* TODO - odkaz na /lidl *}
                                <div class="storeItem__wrapper">
                                    <a n:href="Shop:shop $nearesStore->getShop()" class="storeItem__image">
                                        <img
                                            src="{$basePath}/images/placeholder-80x70.png"
                                            srcset="
                                                {$shop->getLogoUrl() |image:160,140} 1x,
                                                {$shop->getLogoUrl() |image:320,280} 2x
                                            "
                                            width="80"
                                            height="70"
                                            alt="{$shop->getName()}"
                                            class=""
                                        >
                                    </a>

                                    <div>
                                        <a n:href="City:store $nearesStore->getCity(), $shop, $nearesStore" class="storeItem__content">
                                            {_'kaufino.city.store.store', [fullAddress => $nearesStore->getFullAddress()]}
                                        </a>
                                        <div class="storeItem__distance">
                                            {$distance($nearesStore)} km
                                        </div>
                                    </div>
                                </div>

                                <a class="storeItem__icon color--primary" n:href="City:store $nearesStore->getCity(), $shop, $nearesStore">
                                    <span class="icon " data-svg="/assets/icons/arrow_right.svg?v=1700491612828"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0z"></path><path d="M8.8 7.7c-.4-.4-.4-1 0-1.4.4-.4 1.1-.4 1.5 0l4.6 4.3c.8.8.8 2 0 2.8l-4.6 4.3c-.4.4-1.1.4-1.5 0-.4-.4-.4-1 0-1.4l4.6-4.3-4.6-4.3z"></path></svg></span>
                                </a>
                            </div>
                        </li>
                    {/foreach}
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
    .swiper.favorite-shops,
    .swiper-button-prev.favorite-shops,
    .swiper-button-next.favorite-shops {
        position: relative;
        color: #292D32;
    }

    .swiper-button-prev.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }
    .swiper-button-next.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }


    /* HP OLD SWIPER */
    .k-hp-swiper .swiper-button-next,
    .k-hp-swiper .swiper-button-prev {
        border-radius: 50%;
        background-color: white;
        padding: 21px;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
        transition: box-shadow 0.2s ease;
        color: #bc2026;
    }

    .k-hp-swiper .swiper-button-next::after,
    .k-hp-swiper .swiper-button-prev::after {
        font-weight: bold;
        font-size: 18px;
    }

    .k-hp-swiper .swiper-button-next:hover,
    .k-hp-swiper .swiper-button-prev:hover {
        box-shadow: 0 1px 6px #bc2026;
    }

    .k-hp-swiper .swiper-button-prev.swiper-button-disabled,
    .k-hp-swiper .swiper-button-next.swiper-button-disabled {
        display: none;
    }

    @media only screen and (max-width: 500px) {
        .k-hp-swiper .swiper-button-next,
        .k-hp-swiper .swiper-button-prev {
            display: none;
        }
    }
</style>

<script type="module">
    import Swiper from 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.mjs'

    const FavoriteSwiper = new Swiper('.swiper.favorite-shops', {
        direction: 'horizontal',
        slidesPerView: 3.5,
        breakpoints: {
            1024: {
                slidesPerView: 7,
                spaceBetween: 36,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 36,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.favorite-shops',
            prevEl: '.swiper-button-prev.favorite-shops',
        },
    });
</script>

