{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block head}
{include parent}
<script n:syntax="double">
    window.dataLayer.push({
        'content_group' : 'Shop',
        'country' : {{$localization->getRegion()}}
    });
</script>
{/block}

{block title}{if $pageExtension && $pageExtension->getTitle()}{$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}{else}{$metaTitle}{/if}{/block}
{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block breadcrumb}
<div class="k-breadcrumb__container mt-4">
    <p class="k-breadcrumb">
        <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
        <a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag() !== null">{$shop->getTag()->getName()}</a>
    </p>
</div>
{/block}

{block scripts}
{include parent}

<script n:if="$faqContentBlocks" type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {foreach $faqContentBlocks as $faq} {
                "@type": "Question",
                "name": {$faq->getHeading()},
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": {strip_tags($faq->getContent())}
                }
            }{sep},{/sep}
            {/foreach}
        ]
    }
</script>
{/block}
{block content}

<div class="container">
    <div class="flex flex-col lg:flex-row lg:items-center justify-between pt-[34px] mb-11">
        <div class="flex flex-row items-center md:items-start gap-[15px] mb-3 lg:mb-0">
            <img class="w-[60px] h-[60px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

            <div>
                <div class="text-[24px] font-medium leading-[34px]">Lidl letáky</div>
                <div class="hidden md:flex gap-[9px] items-center font-light text-sm text-[#646C7C]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                        <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>
                    Obchody
                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>
                    Hypermarkety a supermarkety
                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>
                    Lidl letáky
                </div>
            </div>
        </div>

        <div class="flex items-center gap-5 text-sm leading-[24.5px] text-[#646C7C] font-light py-3 pl-[21px] pr-[33px] bg-light-6 rounded-lg transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.56252 14.0001C6.56252 9.58103 9.60595 5.39453 14 5.39453C18.3941 5.39453 21.4375 9.58103 21.4375 14.0001C21.4375 16.4743 21.8327 17.9988 22.1922 18.8743C22.3721 19.3122 22.5443 19.5913 22.6575 19.7488C22.7142 19.8277 22.7566 19.877 22.7785 19.9009C22.7851 19.9081 22.7899 19.9131 22.7927 19.9159C22.8111 19.9328 22.8289 19.9504 22.8458 19.9688C22.9017 20.0291 22.9476 20.0956 22.9834 20.1662C23.0414 20.2801 23.0752 20.4084 23.078 20.5444C23.0787 20.5808 23.0772 20.6174 23.0735 20.6537C23.0698 20.6892 23.0639 20.7245 23.0559 20.7594C23.0248 20.8949 22.9608 21.025 22.8631 21.1372C22.7582 21.2576 22.6277 21.3424 22.4867 21.3906C22.3906 21.4234 22.2896 21.4392 22.1887 21.4375H5.81165C5.76516 21.4383 5.71868 21.4354 5.67267 21.4288C5.61541 21.4207 5.55994 21.407 5.5068 21.3883C5.36578 21.3389 5.23554 21.2527 5.13151 21.1309C4.98555 20.96 4.91689 20.7491 4.92216 20.5408C4.92547 20.4049 4.95976 20.2767 5.01822 20.163C5.05542 20.0905 5.10329 20.0223 5.16163 19.9608C5.17799 19.9436 5.19505 19.927 5.21276 19.9111C5.21541 19.9084 5.21977 19.9039 5.2257 19.8975C5.24696 19.8746 5.2888 19.8267 5.345 19.7492C5.45709 19.5947 5.62872 19.3191 5.80819 18.8836C6.16705 18.0126 6.56252 16.4887 6.56252 14.0001ZM20.5734 19.539C20.5943 19.5899 20.6152 19.6394 20.6362 19.6875H7.36812C7.38752 19.6429 7.4069 19.5972 7.42622 19.5503C7.88767 18.4303 8.31252 16.673 8.31252 14.0001C8.31252 10.2162 10.8735 7.14453 14 7.14453C17.1266 7.14453 19.6875 10.2162 19.6875 14.0001C19.6875 16.6601 20.1126 18.4168 20.5734 19.539ZM13.2344 23.8438C13.2344 23.3605 12.8426 22.9688 12.3594 22.9688C11.8761 22.9688 11.4844 23.3605 11.4844 23.8438C11.4844 24.5109 11.7493 25.1508 12.2212 25.6227C12.693 26.0945 13.3329 26.3594 14 26.3594C14.6671 26.3594 15.307 26.0945 15.7789 25.6227C16.2507 25.1508 16.5156 24.5109 16.5156 23.8438C16.5156 23.3605 16.1239 22.9688 15.6406 22.9688C15.1574 22.9688 14.7656 23.3605 14.7656 23.8438C14.7656 24.0469 14.685 24.2417 14.5414 24.3852C14.3979 24.5287 14.2031 24.6094 14 24.6094C13.7969 24.6094 13.6021 24.5287 13.4586 24.3852C13.3151 24.2417 13.2344 24.0469 13.2344 23.8438Z" fill="#646C7C"/>
            </svg>
            <div>
                <span class="font-medium">Upozornit,</span> až vyjde nový leták
            </div>
        </div>
    </div>

    <div class="w-full">
        <div class="flex flex-row items-center gap-3 justify-between mb-5 md:mb-[35px] overflow-x-auto mr-[-20px] md:mr-0">
            <div onclick="showTab(1)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
                <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                    <path d="M9.50078 17V3.24709C9.50078 3.24709 7.64605 1.09329 1.39287 1.00641C1.34133 1.00588 1.29019 1.01609 1.24246 1.03642C1.19473 1.05676 1.15135 1.08684 1.11485 1.12488C1.04125 1.20268 0.999987 1.3077 1.00001 1.41711V14.3494C0.999175 14.4559 1.03823 14.5585 1.10883 14.6352C1.17942 14.7119 1.27595 14.7568 1.37776 14.7601C7.64454 14.8446 9.50078 17 9.50078 17ZM9.50078 17C9.50078 17 11.357 14.8446 17.6222 14.7601C17.7241 14.7568 17.8206 14.7119 17.8912 14.6352C17.9618 14.5585 18.0008 14.4559 18 14.3494V1.4171C18 1.30768 17.9587 1.20266 17.8852 1.12487C17.8488 1.08701 17.8057 1.05705 17.7582 1.03671C17.7108 1.01638 17.6599 1.00607 17.6086 1.0064C16.9363 1.01588 13.0878 0.729878 9.68813 3.0993M7.23444 8.35548C5.94879 7.90348 4.61353 7.62349 3.26057 7.52224M7.23444 11.6687C5.94851 11.2185 4.61331 10.9398 3.26057 10.8394M11.7672 8.35548C13.0528 7.90348 14.3881 7.62349 15.7411 7.52224M11.7672 11.6687C13.0531 11.2185 14.3883 10.9398 15.7411 10.8394" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Letáky
            </div>

            <div onclick="showTab(2)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
                <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                    <path d="M16.7774 7.4V15.4C16.7774 16.2837 16.0525 17 15.1584 17H3.82503C2.93086 17 2.20599 16.2837 2.20599 15.4V7.4M3.0481 1C2.88731 1.00648 2.73211 1.06017 2.60235 1.15421C2.47257 1.24826 2.37411 1.37839 2.31953 1.528L1.09715 4.952C0.967617 5.32259 0.967617 5.72541 1.09715 6.096C1.22872 6.52073 1.49149 6.89412 1.84862 7.16383C2.20574 7.43354 2.63933 7.58606 3.08858 7.6C3.37992 7.58969 3.66635 7.52271 3.93149 7.40291C4.19662 7.28311 4.43525 7.11284 4.63374 6.90182C4.83224 6.69081 4.98669 6.44319 5.08826 6.17314C5.18984 5.9031 5.23655 5.6159 5.22572 5.328C5.20382 5.90812 5.41569 6.47314 5.81483 6.89908C6.21397 7.32502 6.76778 7.5771 7.35475 7.6C7.6461 7.58969 7.93251 7.52271 8.19771 7.40291C8.46283 7.28311 8.70148 7.11284 8.89997 6.90182C9.09839 6.69081 9.25285 6.44319 9.35444 6.17314C9.45604 5.9031 9.50275 5.6159 9.4919 5.328C9.48105 5.6159 9.52776 5.9031 9.62936 6.17314C9.73095 6.44319 9.88541 6.69081 10.0839 6.90182C10.2824 7.11284 10.521 7.28311 10.7862 7.40291C11.0513 7.52271 11.3377 7.58969 11.629 7.6C12.2132 7.57299 12.7629 7.31913 13.1586 6.89364C13.5543 6.46815 13.7639 5.90547 13.7419 5.328C13.7311 5.61721 13.7783 5.90567 13.881 6.17674C13.9835 6.44781 14.1394 6.69612 14.3396 6.90734C14.5398 7.11857 14.7804 7.28852 15.0473 7.40741C15.3143 7.52629 15.6025 7.59174 15.8952 7.6C16.3473 7.58937 16.7845 7.43837 17.1448 7.16842C17.5052 6.89846 17.7704 6.52326 17.9029 6.096C18.0324 5.72541 18.0324 5.32259 17.9029 4.952L16.6643 1.528C16.6097 1.37839 16.5113 1.24826 16.3815 1.15421C16.2517 1.06017 16.0965 1.00648 15.9357 1H3.0481Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Obchod
            </div>

            <div onclick="showTab(3)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
                <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                    <path d="M2.02263 13.3117C3.50804 14.8389 5.53916 15.7136 7.66941 15.7437C9.79964 15.7737 11.8546 14.9565 13.3825 13.4718C14.8671 11.9438 15.6842 9.88875 15.654 7.75851C15.6239 5.62827 14.7489 3.59719 13.2216 2.11187M11.7669 7.79902H12.8983M10.0661 10.0648L11.199 11.1977M7.8003 11.7641V12.8955M1.34289 13.9884C1.22628 14.105 1.13562 14.245 1.07686 14.399C1.01809 14.5531 0.992539 14.7179 1.00188 14.8825C1.01122 15.0472 1.05524 15.208 1.13106 15.3544C1.20688 15.5009 1.31278 15.6296 1.44183 15.7323C3.40212 17.3072 5.87129 18.1096 8.38292 17.988C10.8945 17.8664 13.2746 16.8291 15.0735 15.0722C16.8296 13.2735 17.8663 10.8939 17.9879 8.38306C18.1095 5.87214 17.3076 3.4036 15.7336 1.44352C15.6312 1.31426 15.5026 1.20811 15.3562 1.13202C15.2099 1.05593 15.0492 1.01162 14.8845 1.002C14.7199 0.992369 14.5551 1.01764 14.4008 1.07616C14.2466 1.13468 14.1065 1.22512 13.9897 1.34156L1.34289 13.9884Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Pobočky
            </div>

            <div onclick="showTab(4)" class="px-3 md:px-0 tab-item flex items-center justify-center pb-[5px] gap-2.5 font-medium leading-7 w-full hover:cursor-pointer text-current hover:text-primary transition-colors duration-200 hover:border-b-2 hover:border-b-primary group">
                <svg class="text-current group-hover:text-primary transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                    <path d="M18 9.5H9.4623M18 9.5L14.8264 6.28889M18 9.5L14.8264 12.7111M1.5387 15.3556C1.91647 16.9422 3.27646 18 4.86311 18H5.54311C6.14755 18 6.67643 17.4711 6.67643 16.8667V13.9956C6.67643 13.3911 6.14755 12.8622 5.54311 12.8622C4.93867 12.8622 4.40978 12.3333 4.40978 11.7289V7.19556C4.40978 6.59111 4.93867 6.06222 5.54311 6.06222C6.14755 6.06222 6.67643 5.53333 6.67643 4.92889V2.13333C6.67643 1.52889 6.14755 1 5.54311 1H4.86311C3.27646 1 1.91647 2.13333 1.5387 3.64444C0.858708 6.96889 0.783153 11.9556 1.5387 15.3556Z" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Kontakt
            </div>
        </div>

        <div id="tab-content-1" class="tab-content hidden">
            <div class="font-light leading-7 mb-[33px] text-grey-description max-w-[714px]">
                Aktuální Lidl leták na tento týden k prohlédnutí online. Letáky Lidl vychází pravidelně <span class="font-medium">každé pondělí.</span> Další Lidl leták na příští týden u nás naleznete již v pátek.
            </div>

            <div
                class="flex flex-col lg:flex-row items-center gap-4 lg:gap-8 p-2 rounded-xl border border-[#EEE] mb-10"
                style="rgba(30, 168, 101, 0.02)"
            >
                <div class="flex justify-center lg:justify-normal relative w-full lg:w-[544px]">
                    <div class="absolute inset-0"></div>
                    <div>
                        <img class="md:min-w-[272px] border rounded-l-lg h-auto" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/kbfrh3M8jRHgFUfQSBaxI3CSf9fhrfrgMP6SDWPdgag/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU1LzI1NTYxMS8wenJhN3RnODA3Zjk1YjZ0cjZlNnYwOHYuanBn.webp" alt="">
                    </div>
                    <div>
                        <img class="md:min-w-[272px] border rounded-r-lg h-auto" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/dGMHy1WRxt5crdYsfdnhMC8XUsQ057p76w0KNfUhR1w/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQyNC81MTMzNjIxMzZmOTMxNTFkLmhybTE1c3ZucmYzMS5qcGc.webp" alt="">
                    </div>
                </div>

                <div class="flex flex-col justify-between px-5 pb-5 lg:px-0">
                    <div class="mb-5 md:mb-10">
                        <div class="flex items-center gap-[9px] mb-[17px]">
                            <img class="hidden md:block w-[36px] h-[36px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="letak">

                            <div class="flex flex-col">
                                <div class="text-lg">Lidl letáky</div>
                                <div class="text-xs font-light">04.04. - 07.04.2024</div>
                            </div>
                        </div>

                        <div class="flex gap-2 mb-[23px]">
                            <div class="text-sm text-white font-medium leading-[24.5px] py-1 px-2.5 bg-primary rounded-md">Aktuálni leták</div>
                            <div class="text-sm font-medium leading-[24.5px] py-1 px-2.5 border border-[#DEDEDE] rounded-md">Platný ještě 4 dny</div>
                        </div>

                        <div class="text-sm md:text-base text-grey-description font-light leading-7">
                            Lidl leták od 14. 11. nabízí slevy na potraviny, sezónní zboží a vybavení do domácnosti. Najdete zde také výhodné nabídky na kuchyňské spotřebiče a zimní doplňky.
                        </div>
                    </div>

                    <div class="mt-auto">
                        <button class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full lg:max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
                            Otevřít leták
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                                <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3 mb-8">
                {for $i = 1; $i <= 10; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                    <div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
                        <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                            <div class="w-full relative">
                                <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                            </div>
                            <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                                <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                    Otevřít
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                            <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                            <div class="leading-[21px]">
                                <div class="text-xs md:text-lg font-medium">Lidl</div>
                                <div class="text-xs font-light">04.04. - 07.04.2024</div>
                            </div>
                        </div>
                    </div>
                </div>
                {/for}
            </div>

            <div class="text-center text-sm leading-[24.5px] underline mb-10">Načíst další letáky</div>
        </div>

        <div id="tab-content-2" class="tab-content hidden pb-10">
            <div class="font-light leading-7 text-grey-description max-w-[714px] mb-10">
                Aktuální Lidl leták na tento týden k prohlédnutí online. Letáky Lidl vychází pravidelně každé pondělí. Další Lidl leták na příští týden u nás naleznete již v pátek.
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                {for $i = 1; $i <= 15; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                    <div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
                        <div class="flex mb-[14px] md:mb-[17px] relative">
                            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                            <div class="w-full">
                                <img class="w-full cover max-h-[231px] rounded-lg" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/sq0HjsGeRgZ9MBEFAzuTmYnNp9rOoqw3eDosAFaSDuw/resize:fit:150:150:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvb2ZmZXJzL2ltYWdlLzY3N2VlNjcyY2VjOWEtODY2LmpwZw.webp" alt="letak">
                            </div>
                            <div class="absolute bottom-1 right-1 z-10">
                                <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                    Otevřít
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
                            <div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
                            <div>
                                <div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
                                <div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
                            </div>
                        </div>
                    </div>
                </div>
                {/for}
            </div>
        </div>
        <div id="tab-content-3" class="tab-content hidden">
            <div class="text-[20px] md:text-[26px] font-medium mb-[17px]">
                Pobočky a prodejny Lidl <span class="text-primary">Praha</span> - Přehled a informace
            </div>
            <div class="text-sm flex items-center gap-[14px] mb-6 md:mb-[39px]">
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
                    <path d="M14 6.49405L2.0836 6.50178M6.41308 12L0.999999 6.49397L6.40136 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Zpět na všechny pobočky
            </div>

            <div class="w-full mb-[46px]">
                <iframe class="w-full rounded-xl" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d41716.64084449754!2d16.55961998042657!3d49.19505696287829!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x471295d6ccf03d57%3A0x468363fa122d25fc!2sLidl!5e0!3m2!1ssk!2ssk!4v1737479460340!5m2!1ssk!2ssk" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>

            <div class="flex justify-between gap-[42px]">
                <div class="w-full ">
                    <div class="flex flex-col md:flex-row md:items-center md:gap-[15px] mb-8">
                        <img class="hidden md:block w-[47px] h-[47px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

                        <div>
                            <div class="text-[24px] font-medium leading-[34px]">Lidl pobočky nejbližší vám</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-[13px] mb-8">
                        {for $i = 1; $i <= 8; $i++}
                            <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer flex items-center justify-between bg-light-6 pb-[15px] pt-[14px] px-[18px] rounded-xl">
                                <div class="flex items-center gap-4 font-light">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                        <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <div>
                                        Lidl <span class="font-medium">Praha 3, Olgy Havlové</span>
                                    </div>
                                </div>

                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                    <path d="M3.53562 11.3889L10.6067 4.31787" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M11.3139 10.6818L11.3078 3.60465L4.25505 3.59866" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                        {/for}
                    </div>

                    <div class="text-center text-sm leading-[24.5px] underline mb-10">Načíst další pobočky</div>

                    <div class="flex flex-col md:flex-row md:items-center md:gap-[15px] mb-8">
                        <img class="hidden md:block w-[47px] h-[47px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

                        <div>
                            <div class="text-[24px] font-medium leading-[34px]">Seznam všech Lidl poboček</div>
                        </div>
                    </div>

                    <div class="text-[22px] font-medium leading-[39px] mb-[14px]">Hlavní město Praha</div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-[13px] mb-[47px]">
                        {for $i = 1; $i <= 16; $i++}
                            <div class="flex items-center justify-between underline cursor-pointer">
                                <div class="flex items-center gap-4 font-light">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
                                        <path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <div>
                                        Lidl <span class="font-medium">Praha 3, Olgy Havlové</span>
                                    </div>
                                </div>
                            </div>
                        {/for}
                    </div>

                    <div class="text-center text-sm leading-[24.5px] underline mb-10">Načíst další pobočky</div>
                </div>

                <div class="hidden md:block min-w-[300px]">
                    <div class="text-[26px] font-medium leading-[39px]">Lidl Letáky</div>

                    <div class="mt-3 grid grid-cols-1 gap-[25px]" style="mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0) 101%);">
                        {for $i = 1; $i <= 2; $i++}
                        <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                            <div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
                                <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                                    <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                                    <div class="w-full relative">
                                        <img class="rounded-lg w-full h-[422.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                                    </div>
                                    <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                                        <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                            Otevřít
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="currentColor" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                                    <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                                    <div class="leading-[21px]">
                                        <div class="text-xs md:text-lg font-medium">Lidl</div>
                                        <div class="text-xs font-light">04.04. - 07.04.2024</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/for}
                    </div>

                    <div class="flex justify-center mt-[32px] mb-[41px]">
                        <button class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
                            Všechny letáky
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                                <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <div id="tab-content-4" class="tab-content hidden">
            <div class="flex flex-col md:flex-row gap-[42px] mb-20">
                <div class="w-full md:w-1/2">
                    <div class="text-[26px] leading-[39px] font-medium mb-[15px]">Obvyklá otevírací doba obchodů Lidl</div>

                    <div class="leading-7 font-light py-6 px-[30px] border border-bg-light-4 rounded-xl">
                        V různých pobočkách obchodu Lidl se otevírací doba liší. Obchody mají otevřeno ve všední dny obvykle od 7:00 do 22:00, o víkendech pak od 9:00 do 21 nebo 22 hodin.

                        <div class="font-medium leading-7 mt-3">
                            <div class="flex justify-between items-center border-b border-bg-light-4 py-3">
                                <div>Pondělí</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                            <div class="flex justify-between items-center border-b border-bg-light-4 py-3">
                                <div>Úterý</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                            <div class="flex justify-between items-center border-b border-bg-light-4 py-3">
                                <div>Středa</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                            <div class="flex justify-between items-center border-b border-bg-light-4 py-3">
                                <div>Čtvrtek</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                            <div class="flex justify-between items-center border-b border-bg-light-4 py-3">
                                <div>Pátek</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                            <div class="flex justify-between items-center border-b border-bg-light-4 py-3">
                                <div>Sobota</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                            <div class="flex justify-between items-center py-3">
                                <div>Neděle</div>
                                <div>Obvykle <span>7:00</span> nebo <span>8:00 - 22:00</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2">
                    <div class="text-[26px] leading-[39px] font-medium mb-[15px]">Nejbližší pobočky u vás</div>

                    <div class="flex flex-col gap-3">
                        {for $i = 1; $i <= 4; $i++}
                        <div class="bg-light-6 p-4 rounded-xl">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center gap-[13px]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.5 0C6.98044 0 4.56408 1.00089 2.78249 2.78249C1.00089 4.56408 0 6.98044 0 9.5C0 12.0196 1.00089 14.4359 2.78249 16.2175C4.56408 17.9991 6.98044 19 9.5 19C12.0196 19 14.4359 17.9991 16.2175 16.2175C17.9991 14.4359 19 12.0196 19 9.5C19 6.98044 17.9991 4.56408 16.2175 2.78249C14.4359 1.00089 12.0196 0 9.5 0ZM3.84315 3.84315C5.34344 2.34285 7.37827 1.5 9.5 1.5C11.6217 1.5 13.6566 2.34285 15.1569 3.84315C16.6571 5.34344 17.5 7.37827 17.5 9.5C17.5 11.6217 16.6571 13.6566 15.1569 15.1569C13.6566 16.6571 11.6217 17.5 9.5 17.5C7.37827 17.5 5.34344 16.6571 3.84315 15.1569C2.34285 13.6566 1.5 11.6217 1.5 9.5C1.5 7.37827 2.34285 5.34344 3.84315 3.84315ZM10.25 5.75C10.25 5.33579 9.91421 5 9.5 5C9.08579 5 8.75 5.33579 8.75 5.75V9.5C8.75 9.69891 8.82902 9.88968 8.96967 10.0303L11.4697 12.5303C11.7626 12.8232 12.2374 12.8232 12.5303 12.5303C12.8232 12.2374 12.8232 11.7626 12.5303 11.4697L10.25 9.18934V5.75Z" fill="black"/>
                                    </svg>
                                    <div>Lidl <span class="font-medium">Praha 2, Vinohradská 543</span></div>
                                </div>
                                <div class="flex items-center gap-1.5 text-sm leading-[24.5px]">
                                    Zobrazit
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                        <path d="M3.53562 11.3889L10.6067 4.31787" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M11.3139 10.6818L11.3078 3.60465L4.25505 3.59866" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="text-grey-description leading-7 font-light">Po - Pá 7:00 až 19:00. So 9:00 až 22:00. Ne 9:00 až 21:00</div>
                        </div>
                        {/for}
                    </div>

                    <div class="flex justify-center mt-[32px]">
                        <button class="flex justify-center items-center text-white gap-[9px] py-[17px] w-full max-w-[325px] bg-primary rounded-xl hover:bg-primary-hover transition duration-200">
                            Najít svou pobočku
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                                <path d="M4.43611 13.9086L12.876 5.46875" stroke="white" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M13.6439 12.3741L13.6373 4.69494L5.98463 4.68844" stroke="white" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="mb-[69px]">
                <div class="text-[26px] leading-[39px] font-medium mb-[15px]">Aktuální zprávy o otevírací době</div>

                <div class="flex flex-col gap-[51px]">
                    {for $i = 1; $i <= 2; $i++}
                        <div class="flex flex-col md:flex-row items-center gap-5 md:gap-10">
                            <div>
                                <img class="aspect-video object-cover rounded-xl" src="https://images.unsplash.com/photo-1587970541290-1ac860337508?q=80&w=1829&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="">
                            </div>
                            <div class="flex flex-col">
                                <div class="max-w-max text-xs font-light px-2 py-1 border border-light-3 rounded mb-1">Nakupování</div>
                                <div class="text-[22px] font-semibold leading-[39px] mb-4">Otevírací doba Lidl o svátcích 2024</div>
                                <div class="font-light leading-7 mb-[19px]">
                                    Pokud patříte mezi ty, kteří rádi využívají státní svátky k tomu, aby uskutečnili větší nákup a navštívili své oblíbené prodejny, podívejte se raději na to, jak mají v roce 2024 otevřené prodejny Lidl o státní svátky. Díky tomu zjistíte, kdy se můžete vydat na nákup a kdy raději zůstat doma s rodinou.
                                </div>
                                <div class="flex items-center gap-3 underline">
                                    Přečíst celý článek
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                                        <path d="M3.83601 13.0434L12.2759 4.60352" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M13.0438 11.5089L13.0372 3.82971L5.38453 3.8232" stroke="black" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    {/for}
                </div>
            </div>
    </div>
</div>
</div>

<div class="bg-light-6">
    <div class="container py-10">
        <div class="flex flex-col md:flex-row md:items-center md:gap-[15px] mb-8">
            <img class="hidden md:block w-[47px] h-[47px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

            <div>
                <div class="text-[24px] font-medium leading-[34px]">Akční zboží z obchodu Lidl</div>
            </div>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {for $i = 1; $i <= 5; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                    <div class="p-1.5 md:p-2 bg-white rounded-xl">
                        <div class="flex mb-[14px] md:mb-[17px] relative">
                            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                            <div class="w-full">
                                <img class="w-full cover max-h-[231px] rounded-lg" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/sq0HjsGeRgZ9MBEFAzuTmYnNp9rOoqw3eDosAFaSDuw/resize:fit:150:150:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvb2ZmZXJzL2ltYWdlLzY3N2VlNjcyY2VjOWEtODY2LmpwZw.webp" alt="letak">
                            </div>
                            <div class="absolute bottom-1 right-1 z-10">
                                <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                    Otevřít
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
                            <div class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">Vejce</div>
                            <div>
                                <div class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">Pštrosí Vejce</div>
                                <div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">99,90 Kč</div>
                            </div>
                        </div>
                    </div>
                </div>
            {/for}
        </div>

        <div class="flex justify-center mt-8">
            <button class="flex justify-center items-center text-primary gap-[9px] py-[17px] w-full max-w-[325px] border border-primary rounded-xl hover:bg-primary-light-hover transition duration-200">
                Další produkty
                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                    <path d="M3.8365 13.6033L12.2764 5.16345" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M13.0443 12.0688L13.0377 4.38964L5.38502 4.38314" stroke="currentColor" stroke-width="1.68907" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </button>
        </div>
    </div>
</div>

<div class="container py-10">
    <div class="text-[20px] font-medium leading-[35px] mb-3">Lidl leták</div>
    <div class="font-light leading-7 mb-10">
        Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností, jak během nákupů ušetřit, v případě eshopu se několikrát do roka koná Lidl shop mega výprodej, během kterého lze ušetřit například na doplňcích do domácnosti. Prolistovat si mohou zákazníci leták hned na čtyřech místech. Dostupný je akční leták Lidl zde, na Kaufino.com, na e-shopu společnosti nebo v kamenných prodejnách a mobilní aplikaci. Podobný sortiment jako Lidl leták aktuální nabízí také leták Tesco, kde je opět dostupné nejen potravinové, ale také nepotravinové zboží. Spousta kupujících také ráda využívá Lidl Polsko leták, dající se uplatnit v polských prodejnách. Mnohdy Lidl Polsko leták nabízí ještě výhodnější akce, než které zahrnuje český leták Lidl. Obsažené Lidl akce umožňují vyzkoušení novinek bez velké finanční náročnosti. Při plánování nákupů je ideální sledovat i Lidl leták příští týden, který společnost vydává několik dnů před koncem platnosti aktuálního letáku. V Lidl letáku na příští týden opět nechybí desítky slev na nejen potravinový sortiment. Atraktivní nabídka obvykle zahrnuje jak běžné značky, tak i vlastní produkty řetězce Lidl.
    </div>

    <div class="flex flex-col md:flex-row md:items-center md:gap-[15px] mb-6">
        <img class="hidden md:block w-[47px] h-[47px] rounded" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">

        <div>
            <div class="text-[26px] font-medium leading-[39px]">Lidl Informácie</div>
        </div>
    </div>

    <div class="font-light leading-7">
        Na nový leták Lidl se mohou kupující těšit každý týden, a to hned dvakrát. První Lidl leták CZ platí od pondělí do neděle a druhý od čtvrtka do neděle. Aktualizovaný je na Kaufinu nejen akční leták Lidl, ale i konkurenční letáky, například Billa leták, který zájemcům nabízí obdobné možnosti výhodných nákupů. Lidl leták však není jedinou možností, jak během nákupů ušetřit, v případě eshopu se několikrát do roka koná Lidl shop mega výprodej, během kterého lze ušetřit například na doplňcích do domácnosti. Prolistovat si mohou zákazníci leták hned na čtyřech místech. Dostupný je akční leták Lidl zde, na Kaufino.com, na e-shopu společnosti nebo v kamenných prodejnách a mobilní aplikaci. Podobný sortiment jako Lidl leták aktuální nabízí také leták Tesco, kde je opět dostupné nejen potravinové, ale také nepotravinové zboží. Spousta kupujících také ráda využívá Lidl Polsko leták, dající se uplatnit v polských prodejnách. Mnohdy Lidl Polsko leták nabízí ještě výhodnější akce, než které zahrnuje český leták Lidl. Obsažené Lidl akce umožňují vyzkoušení novinek bez velké finanční náročnosti. Při plánování nákupů je ideální sledovat i Lidl leták příští týden, který společnost vydává několik dnů před koncem platnosti aktuálního letáku. V Lidl letáku na příští týden opět nechybí desítky slev na nejen potravinový sortiment. Atraktivní nabídka obvykle zahrnuje jak běžné značky, tak i vlastní produkty řetězce Lidl.
    </div>
</div>

<div class="bg-light-6">
    <div class="container py-10">
        <div class="mb-[28px] md:mb-[37px]">
            <div class="flex relative justify-between md:gap-[5px] leading-[35px] md:leading-[39px] text-[20px] md:text-[26px] font-medium mb-4 ">
                <div class="flex items-center">
                    Oblíbené obchody
                </div>

                <div class="hidden md:flex gap-[60px]">
                    <div class="swiper-button-prev favorite-shops"></div>
                    <div class="swiper-button-next favorite-shops"></div>
                </div>
            </div>

            <div class="swiper favorite-shops -mr-5 md:mr-0">
                <div class="swiper-wrapper">
                    {for $i = 1; $i <= 8; $i++}
                    <div class="swiper-slide">
                        <div class="hidden md:block">
                            <svg width="141" height="141" viewBox="0 0 141 141" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M128.286 0H12C5.37258 0 0 5.37259 0 12V128.286C0 134.913 5.37259 140.286 12 140.286H128.286C134.913 140.286 140.286 134.913 140.286 128.286V12C140.286 5.37258 134.913 0 128.286 0Z" fill="#CD1414"/>
                                <path d="M131.52 77.3539C131.52 78.3552 131.223 79.3341 130.667 80.1667C130.111 80.9993 129.32 81.6482 128.395 82.0314C127.47 82.4146 126.452 82.5148 125.47 82.3195C124.488 82.1241 123.585 81.6419 122.877 80.9339C122.169 80.2258 121.687 79.3237 121.492 78.3416C121.296 77.3595 121.397 76.3415 121.78 75.4164C122.163 74.4913 122.812 73.7006 123.645 73.1443C124.477 72.5879 125.456 72.291 126.457 72.291C127.8 72.291 129.088 72.8244 130.037 73.7739C130.987 74.7234 131.52 76.0111 131.52 77.3539Z" fill="#FFD200"/>
                                <path d="M19.3011 69.6978H21.1817C23.3197 69.6978 24.6164 68.3764 24.849 66.7283C25.0469 65.308 24.0571 63.7589 22.0429 63.7589H20.2365L19.3011 69.6978ZM114.456 66.2681L118.742 58.2308H128.596L113.749 81.7685H103.677L108.443 75.0526L102.999 58.7208L99.2876 81.7586H91.4087L86.5686 70.8707L84.8166 81.7586H76.9328L80.7237 58.2209H88.8154L93.5071 68.7525L95.2047 58.2209H112.437L114.456 66.2681ZM77.6108 58.2308L73.8198 81.7685H65.9112L61.0711 70.8806L59.3191 81.7685H51.4353L55.2262 58.2308H63.3179L68.0047 68.7624L69.7022 58.2308H77.6108ZM24.3095 57.8745C29.7535 57.8745 33.4504 60.8439 33.4851 65.5752L34.6432 58.2259H52.4944L51.5343 64.1647H42.1311L41.6609 66.9857H50.1881L49.2379 72.8701H40.6711L40.1762 75.795H49.7773L48.8172 81.7338H30.8571L32.911 68.99C31.5302 73.1175 27.764 75.5475 22.8347 75.5475H18.3113L17.3215 81.7338H8.76953L12.6199 57.8398L24.3095 57.8745Z" fill="white"/>
                            </svg>
                        </div>

                        <div class="md:hidden">
                            <svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M75.9973 0H12C5.37258 0 0 5.37258 0 12V75.9973C0 82.6247 5.37258 87.9973 12 87.9973H75.9973C82.6247 87.9973 87.9973 82.6247 87.9973 75.9973V12C87.9973 5.37258 82.6247 0 75.9973 0Z" fill="#CD1414"/>
                                <path d="M82.4991 48.5215C82.4991 49.1496 82.3128 49.7636 81.9638 50.2859C81.6149 50.8081 81.1189 51.2152 80.5386 51.4556C79.9583 51.6959 79.3197 51.7588 78.7037 51.6363C78.0877 51.5137 77.5218 51.2113 77.0776 50.7671C76.6335 50.323 76.331 49.7571 76.2085 49.1411C76.0859 48.525 76.1488 47.8865 76.3892 47.3062C76.6296 46.7259 77.0366 46.2299 77.5589 45.8809C78.0811 45.532 78.6951 45.3457 79.3233 45.3457C80.1655 45.3457 80.9733 45.6803 81.5689 46.2759C82.1645 46.8715 82.4991 47.6792 82.4991 48.5215Z" fill="#FFD200"/>
                                <path d="M12.1071 43.7193H13.2868C14.6279 43.7193 15.4413 42.8904 15.5872 41.8566C15.7113 40.9657 15.0905 39.994 13.827 39.994H12.6939L12.1071 43.7193ZM71.7954 41.5679L74.4838 36.5264H80.6647L71.3515 51.2909H65.034L68.0236 47.0782L64.6087 36.8337L62.2804 51.2847H57.3382L54.3021 44.455L53.2032 51.2847H48.2579L50.6358 36.5202H55.7115L58.6545 43.1263L59.7193 36.5202H70.5288L71.7954 41.5679ZM48.6832 36.5264L46.3052 51.2909H41.3444L38.3083 44.4612L37.2093 51.2909H32.264L34.642 36.5264H39.7177L42.6575 43.1325L43.7223 36.5264H48.6832ZM15.2488 36.3029C18.6636 36.3029 20.9826 38.1655 21.0043 41.1333L21.7308 36.5233H32.9283L32.3261 40.2485H26.4277L26.1328 42.0181H31.4817L30.8857 45.7092H25.5119L25.2015 47.5439H31.224L30.6218 51.2692H19.3559L20.6442 43.2753C19.7781 45.8644 17.4157 47.3887 14.3237 47.3887H11.4863L10.8654 51.2692H5.50098L7.9162 36.2811L15.2488 36.3029Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                    {/for}
                </div>
            </div>
        </div>

        <div class="hidden md:block w-full h-px bg-light-2 my-[50px]"></div>

        <div class="flex relative justify-between md:gap-[5px] leading-[35px] md:leading-[39px] text-[20px] md:text-[26px] font-medium mb-4">
            <div class="flex items-center">
                Další letáky
            </div>
        </div>

        <div class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3">
            {for $i = 1; $i <= 11; $i++}
            <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                <div class="p-1.5 md:p-2 bg-white rounded-xl">
                    <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                        <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                        <div class="w-full relative">
                            <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                        </div>
                        <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                            <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                Otevřít
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                        <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                        <div class="leading-[21px]">
                            <div class="text-xs md:text-lg font-medium">Lidl</div>
                            <div class="text-xs font-light">04.04. - 07.04.2024</div>
                        </div>
                    </div>
                </div>
            </div>
            {/for}
        </div>
    </div>
</div>

<div class="hidden md:block">
    <div class="container py-10">
        <div class="flex items-center gap-4 mb-[32px]">
            <img class="w-[47px] h-[47px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
            <div class="text-[26px] font-medium leading-[39px]">Letáky Lidl najdete také v těchto zemích</div>
        </div>
        <div class="flex flex-wrap gap-1.5 md:gap-3">
            {foreach range(1, 17) as $i}
            <div class="transition-transform duration-200 transform hover:scale-[105%] cursor-pointer rounded-lg flex items-center gap-[5px] md:gap-[11px] p-1.5 md:p-3 text-xs md:text-sm leading-[21px] md:leading-[24.5px] font-light bg-light-6">
                <svg class="w-[15px] h-[15px] md:w-[33px] md:h-[33px]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path d="M10.1086 0.466494C9.29607 0.165029 8.41728 0 7.49987 0C6.58247 0 5.70368 0.165029 4.89119 0.466494L4.23901 7.5L4.89119 14.5335C5.70368 14.835 6.58247 15 7.49987 15C8.41728 15 9.29607 14.835 10.1086 14.5335L10.7607 7.5L10.1086 0.466494Z" fill="#FFDA44"/>
                    <path d="M15.0002 7.50003C15.0002 4.27532 12.9649 1.52622 10.1089 0.466553V14.5336C12.9649 13.4738 15.0002 10.7248 15.0002 7.50003Z" fill="#D80027"/>
                    <path d="M0 7.50003C0 10.7248 2.03531 13.4738 4.89132 14.5336V0.466553C2.03531 1.52622 0 4.27532 0 7.50003Z" fill="black"/>
                </svg>
                Belgium
            </div>
            {/foreach}
        </div>
    </div>
</div>


<!--<div class="bg-light-6">
    <div class="container pt-[43px] pb-[51px]">
        <div class="hidden md:block">
            <div class="flex justify-between items-center">
                <div class="text-[26px] font-medium leading-[39px] mb-[41px]">Ďalší obchody</div>
                <div class="hidden md:flex gap-[60px]">
                    <div class="swiper-button-prev favorite-shops"></div>
                    <div class="swiper-button-next favorite-shops"></div>
                </div>
            </div>

            <div class="swiper favorite-shops">
                <div class="swiper-wrapper">
                    {for $i = 1; $i <= 8; $i++}
                    <div class="swiper-slide">
                        <div class="hidden md:block">
                            <svg width="141" height="141" viewBox="0 0 141 141" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M128.286 0H12C5.37258 0 0 5.37259 0 12V128.286C0 134.913 5.37259 140.286 12 140.286H128.286C134.913 140.286 140.286 134.913 140.286 128.286V12C140.286 5.37258 134.913 0 128.286 0Z" fill="#CD1414"/>
                                <path d="M131.52 77.3539C131.52 78.3552 131.223 79.3341 130.667 80.1667C130.111 80.9993 129.32 81.6482 128.395 82.0314C127.47 82.4146 126.452 82.5148 125.47 82.3195C124.488 82.1241 123.585 81.6419 122.877 80.9339C122.169 80.2258 121.687 79.3237 121.492 78.3416C121.296 77.3595 121.397 76.3415 121.78 75.4164C122.163 74.4913 122.812 73.7006 123.645 73.1443C124.477 72.5879 125.456 72.291 126.457 72.291C127.8 72.291 129.088 72.8244 130.037 73.7739C130.987 74.7234 131.52 76.0111 131.52 77.3539Z" fill="#FFD200"/>
                                <path d="M19.3011 69.6978H21.1817C23.3197 69.6978 24.6164 68.3764 24.849 66.7283C25.0469 65.308 24.0571 63.7589 22.0429 63.7589H20.2365L19.3011 69.6978ZM114.456 66.2681L118.742 58.2308H128.596L113.749 81.7685H103.677L108.443 75.0526L102.999 58.7208L99.2876 81.7586H91.4087L86.5686 70.8707L84.8166 81.7586H76.9328L80.7237 58.2209H88.8154L93.5071 68.7525L95.2047 58.2209H112.437L114.456 66.2681ZM77.6108 58.2308L73.8198 81.7685H65.9112L61.0711 70.8806L59.3191 81.7685H51.4353L55.2262 58.2308H63.3179L68.0047 68.7624L69.7022 58.2308H77.6108ZM24.3095 57.8745C29.7535 57.8745 33.4504 60.8439 33.4851 65.5752L34.6432 58.2259H52.4944L51.5343 64.1647H42.1311L41.6609 66.9857H50.1881L49.2379 72.8701H40.6711L40.1762 75.795H49.7773L48.8172 81.7338H30.8571L32.911 68.99C31.5302 73.1175 27.764 75.5475 22.8347 75.5475H18.3113L17.3215 81.7338H8.76953L12.6199 57.8398L24.3095 57.8745Z" fill="white"/>
                            </svg>
                        </div>

                        <div class="md:hidden">
                            <svg width="88" height="88" viewBox="0 0 88 88" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M75.9973 0H12C5.37258 0 0 5.37258 0 12V75.9973C0 82.6247 5.37258 87.9973 12 87.9973H75.9973C82.6247 87.9973 87.9973 82.6247 87.9973 75.9973V12C87.9973 5.37258 82.6247 0 75.9973 0Z" fill="#CD1414"/>
                                <path d="M82.4991 48.5215C82.4991 49.1496 82.3128 49.7636 81.9638 50.2859C81.6149 50.8081 81.1189 51.2152 80.5386 51.4556C79.9583 51.6959 79.3197 51.7588 78.7037 51.6363C78.0877 51.5137 77.5218 51.2113 77.0776 50.7671C76.6335 50.323 76.331 49.7571 76.2085 49.1411C76.0859 48.525 76.1488 47.8865 76.3892 47.3062C76.6296 46.7259 77.0366 46.2299 77.5589 45.8809C78.0811 45.532 78.6951 45.3457 79.3233 45.3457C80.1655 45.3457 80.9733 45.6803 81.5689 46.2759C82.1645 46.8715 82.4991 47.6792 82.4991 48.5215Z" fill="#FFD200"/>
                                <path d="M12.1071 43.7193H13.2868C14.6279 43.7193 15.4413 42.8904 15.5872 41.8566C15.7113 40.9657 15.0905 39.994 13.827 39.994H12.6939L12.1071 43.7193ZM71.7954 41.5679L74.4838 36.5264H80.6647L71.3515 51.2909H65.034L68.0236 47.0782L64.6087 36.8337L62.2804 51.2847H57.3382L54.3021 44.455L53.2032 51.2847H48.2579L50.6358 36.5202H55.7115L58.6545 43.1263L59.7193 36.5202H70.5288L71.7954 41.5679ZM48.6832 36.5264L46.3052 51.2909H41.3444L38.3083 44.4612L37.2093 51.2909H32.264L34.642 36.5264H39.7177L42.6575 43.1325L43.7223 36.5264H48.6832ZM15.2488 36.3029C18.6636 36.3029 20.9826 38.1655 21.0043 41.1333L21.7308 36.5233H32.9283L32.3261 40.2485H26.4277L26.1328 42.0181H31.4817L30.8857 45.7092H25.5119L25.2015 47.5439H31.224L30.6218 51.2692H19.3559L20.6442 43.2753C19.7781 45.8644 17.4157 47.3887 14.3237 47.3887H11.4863L10.8654 51.2692H5.50098L7.9162 36.2811L15.2488 36.3029Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                    {/for}
                </div>
            </div>
        </div>

        <div class="hidden md:block w-full h-px bg-light-2 my-10"></div>

        <div class="text-[26px] font-medium leading-[39px] mb-[26px] md:mb-[41px]">Ďalší letáky</div>

        <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            {for $i = 1; $i <= 11; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                    <div class="p-1.5 md:p-2 bg-white rounded-xl">
                        <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                            <div class="w-full relative">
                                <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                            </div>
                            <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                                <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                    Otevřít
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                            <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                            <div class="leading-[21px]">
                                <div class="text-xs md:text-lg font-medium">Lidl</div>
                                <div class="text-xs font-light">04.04. - 07.04.2024</div>
                            </div>
                        </div>
                    </div>
                </div>
            {/for}
        </div>
    </div>
</div>

<div class="hidden md:block">
    <div class="container pt-[77px] pb-[55px]">
        <div class="flex items-center gap-4 mb-[32px]">
            <img class="w-[47px] h-[47px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
            <div class="text-[24px] font-medium leading-[34px]">Letáky Lidl najdete také v těchto zemích</div>
        </div>
        <div class="flex flex-wrap gap-1.5 md:gap-3">
            {foreach range(1, 17) as $i}
            <div class="transition-transform duration-200 transform hover:scale-[105%] cursor-pointer rounded-lg flex items-center gap-[5px] md:gap-[11px] p-1.5 md:p-3 text-xs md:text-sm leading-[21px] md:leading-[24.5px] font-light bg-light-6">
                <svg class="w-[15px] h-[15px] md:w-[33px] md:h-[33px]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path d="M10.1086 0.466494C9.29607 0.165029 8.41728 0 7.49987 0C6.58247 0 5.70368 0.165029 4.89119 0.466494L4.23901 7.5L4.89119 14.5335C5.70368 14.835 6.58247 15 7.49987 15C8.41728 15 9.29607 14.835 10.1086 14.5335L10.7607 7.5L10.1086 0.466494Z" fill="#FFDA44"/>
                    <path d="M15.0002 7.50003C15.0002 4.27532 12.9649 1.52622 10.1089 0.466553V14.5336C12.9649 13.4738 15.0002 10.7248 15.0002 7.50003Z" fill="#D80027"/>
                    <path d="M0 7.50003C0 10.7248 2.03531 13.4738 4.89132 14.5336V0.466553C2.03531 1.52622 0 4.27532 0 7.50003Z" fill="black"/>
                </svg>
                Belgium
            </div>
            {/foreach}
        </div>
    </div>
</div>-->

<script type="module">
    import Swiper from 'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.mjs'

    const FavoriteSwiper = new Swiper('.swiper.favorite-shops', {
        direction: 'horizontal',
        slidesPerView: 3.5,
        breakpoints: {
            1024: {
                slidesPerView: 7,
                spaceBetween: 36,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 36,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.favorite-shops',
            prevEl: '.swiper-button-prev.favorite-shops',
        },
    });

    const LeafletSwiper = new Swiper('.swiper.leaflet', {
        direction: 'horizontal',
        slidesPerView: 3.2,
        spaceBetween: 12,
        breakpoints: {
            1024: {
                slidesPerView: 8.3,
                spaceBetween: 12,
            },
            768: {
                slidesPerView: 5,
                spaceBetween: 12,
            }
        },
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next.leaflet',
            prevEl: '.swiper-button-prev.leaflet',
        },
    });
</script>

<script>
    // TABS
    function showTab(tabNumber) {
        var tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(function(content) {
            content.classList.add('hidden');
            content.classList.remove('block');
        });

        var tabs = document.querySelectorAll('.tab-item');
        tabs.forEach(function(tab) {
            tab.classList.remove('tabs-active', 'text-primary', 'border-b-2', 'border-primary', 'pb-[3px]');
            tab.classList.add('border-b-2', 'border-light-4');
        });

        document.getElementById('tab-content-' + tabNumber).classList.add('block');
        document.getElementById('tab-content-' + tabNumber).classList.remove('hidden');

        let activeTab = tabs[tabNumber - 1];
        activeTab.classList.remove('border-light-4');
        activeTab.classList.add('tabs-active', 'text-primary', 'border-b-2', 'border-primary', 'pb-[3px]');
    }
    showTab(1);
</script>

<style>
    .swiper.favorite-shops,
    .swiper-button-prev.favorite-shops,
    .swiper-button-next.favorite-shops {
        position: relative;
        color: #292D32;
    }

    .swiper-button-prev.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }
    .swiper-button-next.favorite-shops.swiper-button-disabled::after {
        font-size: 30px;
    }

    /* LEAFLET SWIPER */
    .swiper-button-prev.leaflet,
    .swiper-button-next.leaflet {
        color: white;
        background: black;
        border-radius: 4px;
        width: 32px;
        height: 32px;
        top: 44%;
        right: -30px;
    }
    .swiper-button-prev.leaflet {
        left: -13px;
    }


    .swiper-button-prev.leaflet::after,
    .swiper-button-next.leaflet::after {
        font-size: 14px;
        font-weight: 700;
    }

    .swiper-button-prev.leaflet.swiper-button-disabled,
    .swiper-button-next.leaflet.swiper-button-disabled {
        display: none;
    }

    @media (max-width: 768px) {
        .swiper-button-prev.leaflet,
        .swiper-button-next.leaflet {
            display: none;
        }
    }
</style>
