
{block title}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
    {if $leaflet->isChecked()}
        {_kaufino.leaflet.metaTitle, [brand => $leaflet->getName(), validSince => $validSince]}
    {else}
        {_kaufino.leaflet.metaTitleUnChecked, [brand => $leaflet->getName()]}
    {/if}
{/block}

{block description}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
    {if $leaflet->isChecked()}
        {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
    {else}
        {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
    {/if}
{/block}

{block robots}{if $currentPage > 1 || $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container k-breadcrumb__container--leaflet mt-4">
        <p class="k-breadcrumb">            
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link">{$shop->getTag()->getName()}</a> |
            <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a> |

            {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
                {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
            {else}
                <span class="color-grey">{$leaflet->getName()}</span>
            {/if}
        </p>
    </div>
{/block}

{block head}
    <link
        rel="preload"
        as="image"
        href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null,'fit','webp'}"                
    />    

    {*}
    <link
        rel="preload"
        as="image"
        href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null}"        
    />
    *}
{/block}

{block scripts}
    {include parent}        
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

{capture $validSinceShort}{$leaflet->getValidSince()|localDate} {/capture}
{capture $validTillShort}{$leaflet->getValidTill()|localDate}{/capture}

{var $cacheKey = 'leaflet-2-' . $leaflet->getId() . '-page-' . $currentPage}

<div class="leaflet k-lf-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $leaflet->isChecked()}
                                {_kaufino.leaflet.title, [brand => $leaflet->getName(), validSince => $validSinceShort, validTill => $validTillShort]|noescape}
                            {else}
                                {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                            {/if}
                        </h1>                        

						<p class="page-header__text ml-0">
                            {if $leaflet->isChecked()}
                                {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
                            {else}
                                {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
                            {/if}
                        </p>
					</div>

					<div class="leaflet__detail-header-side">
						<a n:href="Shop:shop $leaflet->getShop()">
                            <picture>                        
                                <source 
                                    srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','webp'} 2x
                                    " 
                                    type="image/webp"
                                >                                                        
                                <img 
                                    src="{$basePath}/images/placeholder-80x70.png" 
                                    srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','png'} 2x
                                    " 
                                    width="80" 
                                    height="70" 
                                    alt="{$leaflet->getShop()->getName()}" 
                                    class="leaflet__detail-header-logo"
                                >
                            </picture>  					
						</a>
					</div>
				</div>                                

                {cache $cacheKey . "-preview"}
                    <div class="leaflet-preview mb-5">
                    {foreach $leaflet->getPages() as $page}
                        {breakIf $leaflet->isArchived() && $iterator->getCounter() > $leaflet->getCountOfArchivedPages() || $page->getImageUrl() === null}
                        {if $iterator->first}
                            <picture id="p-{$page->getPageNumber()}">
                                <source 
                                    srcset="
                                        {$page->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                        {$page->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                    "
                                    sizes="
                                        (min-width: 1200px) 645px, 
                                        100vw
                                    "
                                    type="image/webp"
                                >
                                <img 
                                    src="{$basePath}/images/placeholder-870.png"
                                    srcset="
                                        {$page->getImageUrl() |image:768,null} 768w,
                                        {$page->getImageUrl() |image:1740,null} 1740w
                                    "
                                    sizes="
                                        (min-width: 1200px) 645px, 
                                        100vw
                                    "                                 
                                    width="870" 
                                    height="1190" 
                                    alt="{$leaflet->getShop()->getName()}"                                     
                                >
                            </picture> 
                        {else}
                            <picture id="p-{$page->getPageNumber()}" data-expand="100" class="{if $iterator->last}leaflet-last-page{/if}">                            
                                <source 
                                    srcset="
                                        {$page->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                        {$page->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                    "                                 
                                    type="image/webp"
                                >
                                <img                             
                                    src="{$basePath}/images/placeholder-870.png"
                                    srcset="
                                        {$page->getImageUrl() |image:768,null} 768w,
                                        {$page->getImageUrl() |image:1740,null} 1740w
                                    "
                                    data-sizes="auto"                                    
                                    width="870"                                     
                                    height="1190" 
                                    alt="{$leaflet->getShop()->getName()}"                                 
                                    class=""
                                >
                            </picture>                         
                        {/if} 

                        {if $iterator->odd}
                            <!-- Detail letaku - Responsive - 1 -->
                            <ins 
                                class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="4173441893"
                                data-ad-format="auto"
                                data-full-width-responsive="true">
                            </ins>
                        {else}    
                            <!-- Detail letaku - Responsive - 3 -->
                            <ins 
                                class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="2477216848"
                                data-ad-format="auto"
                                data-full-width-responsive="true">
                            </ins>
                        {/if} 

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>                                                                                                           
                    {/foreach}
                </div>
                {/cache}

                <div>
                    <!-- Letaky - Detail letaku - Responsive - 2 -->
                    <ins class="adsbygoogle mrec-xs mrec-sm mrec-md leaderboard-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="4885879417" data-ad-format="auto" data-full-width-responsive="true"></ins>

                    <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>                                    

                <div n:if="count($offers) > 0 && isset($userLoggedIn)">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offers, [brand => $shop->getName()]}</h2>

                    <div class="k-offers">
                        {foreach $offers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            {include '../components/offer-item.latte', offer => $offer, hideShop => true}
                        {/foreach}
                    </div>
                </div>

                {capture $leafletBrandLink}                    
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                {capture $leafletPageCount}           
                    {count($leaflet->getPages())}
                {/capture}

                <div class="px-3 px-lg-0">
                    <p class="color-grey fz-m lh-15 mb-3"><strong>{_kaufino.leaflet.smallTitle, [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>
                    <p class="color-grey fz-m lh-15 mb-5">
                        {_'kaufino.leaflet.desc', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}                        
                    </p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_kaufino.leaflet.backToLeaflets}</a>                    
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_kaufino.leaflet.allBrandLeaflets, [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>

                <div>

                    <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_kaufino.leaflet.similarLeaflets, [brand => $leaflet->getShop()->getName()]}</h3>

                    <div class="k-leaflets__wrapper k-leaflets__wrapper--xs-mx">
                        {cache $cacheKey . "-similar-leaflets"}
                            {foreach $getSimilarLeaflets() as $similarLeaflet}
                                {include '../components/leaflet.latte', leaflet => $similarLeaflet, validBadgeShow => true}
                            {/foreach}
                        {/cache}
                    </div>

                    <h3 class="lf__box-title px-3 px-lg-0">{_kaufino.leaflet.recommendedLeaflets}</h3>
                    {cache $cacheKey . "-recommended-leaflets"}
                    <div class="k-leaflets__wrapper k-leaflets__wrapper--xs-mx">
                        {foreach $getRecommendedLeaflets() as $recommendedLeaflet}
                            {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                            {include '../components/leaflet.latte', leaflet => $recommendedLeaflet, validBadgeShow => true}
                        {/foreach}
                    </div>
                    {/cache}

                </div>
            
            </div>                                      
            
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;">                                    
            <div class="float-wrapper">
                <!-- Detail letaku - Sidebar - 2 -->
                <ins class="adsbygoogle mrec-xs mrec-sm skyscraper-md halfpage-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="5041874235" data-ad-format="auto" data-full-width-responsive="true"></ins>                

                <script>
                    (adsbygoogle = window.adsbygoogle || []).push({});
                </script>
            </div>            
        </div>	
    </div>	

	<div class="float-wrapper__stop"></div>	
</div>

