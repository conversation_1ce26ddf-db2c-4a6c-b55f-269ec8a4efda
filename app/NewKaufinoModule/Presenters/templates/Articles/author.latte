{block description}{_kaufino.articles.author.description, ['author' => $author->getFullName()]}{/block}
{block title}{_kaufino.articles.author.title, ['author' => $author->getFullName()]}{/block}

{block content}
    <div class="container">        
        <div class="author">
            <div class="author__container">
                <div class="author__content">
                    <img n:if="$author->getImageUrl()" class="author__avatar" src="{$author->getImageUrl()}" alt="avatar">
                    <div class="author__title">{$author->getFullName()}
                    <div class="author__articles-count">
                        {_kaufino.articles.author.articlesCount, ['count' => count($articles)]}
                    </div>
                    </div>
                </div>
            </div>        
        </div>    

        {include "../components/article-list.latte", articles => $articles}    
    </div>
{/block}
