{var $shop = $leaflet->getShop()}

<a class="lf__box-image-wrapper {if $leaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet">
    <picture n:if="$leaflet->getFirstPage()">
        <source 
            srcset="
                {$leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop','webp'} 60w,
                {$leaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'} 100w,
                {$leaflet->getFirstPage()->getImageUrl() |image:120,168,'exactTop','webp'} 120w,
                {$leaflet->getFirstPage()->getImageUrl() |image:200,280,'exactTop','webp'} 200w
            " 
            type="image/webp"
        >        
        <img 
            src="{$basePath}/images/placeholder-100x140.png" 
            srcset="
                {$leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop'} 60w,
                {$leaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'} 100w,
                {$leaflet->getFirstPage()->getImageUrl() |image:120,168,'exactTop'} 120w,
                {$leaflet->getFirstPage()->getImageUrl() |image:200,280,'exactTop'} 200w
            "
            data-sizes="auto"
            width="200" 
            height="280" 
            alt="{$leaflet->getName()}" 
            class="img-responsive"
            loading="lazy"
        >
    </picture>
</a>
<p class="fz-xxs fz-sm-xs mb-0">
    <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet">
        {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
            {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
        {else}
            {$leaflet->getName()}
        {/if}
    </a>

    <small>{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</small>
</p>