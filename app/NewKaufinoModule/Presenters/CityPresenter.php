<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Shops\StoreFacade;
use Ka<PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Tracy\Debugger;

final class CityPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

    /** @var OfferFacade @inject */
    public $offerFacade;

    /** @var StoreFacade @inject */
    public $storeFacade;

	public function actionCity(City $city): void
	{
		if (!$city->isActive() && !$this->user->isLoggedIn()) {
			$this->redirectPermanent("Homepage:default");
		}

        $shops = $this->shopFacade->findLeafletShopsByCity($city, 50, true, Website::MODULE_KAUFINO);

		$this->template->city = $city;
		$this->template->shops = $shops;
		$this->template->nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 36);

        $leafletsByCity = $this->leafletFacade->findLeafletsByCity($city, null, 20, true);
        $leafletsByShops = $this->leafletFacade->findLeafletsByShopsWithoutCities($shops, 20, true, $leafletsByCity);

        $leafletIds = array_map(static function (Leaflet $leaflet) {
            return $leaflet->isTop() ? $leaflet->getId() : null;
        }, array_merge($leafletsByCity, $leafletsByShops));

        $this->template->leaflets = $this->leafletFacade->findIn($leafletIds);

        $this->template->offers = $this->offerFacade->findOffersByShops($shops, 20, true, Offer::TYPE_LEAFLET);

        $this->template->cityStores = $this->geoFacade->findStoresByCity($city, 48);
	}

	public function actionShop(City $city, Shop $shop): void
	{
		if (!$shop->hasCity($city)) {
			$this->error('The shop is not located in the city.');
		}

		if ($city->isActive() === false && $this->user->isLoggedIn() === false) {
			$this->redirectPermanent("Homepage:default");
		}

		if (!$shop->isActiveLeaflets()) {
			$this->redirectPermanent("City:city", ['city' => $city]);
		}

        if ($city->isActiveBrandsKaufino() === false) {
            $this->redirect("City:city", ['city' => $city]);
        }

		if (!$shop->isStore()) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

        if ($shop->isActiveKaufino() === false) {
            $this->redirect("City:city", ['city' => $city]);
        }

		$this->template->city = $city;
		$this->template->shop = $shop;

        $this->template->leaflets = $this->leafletFacade->findLeafletsByCity($city, $shop, 20);

		$this->template->shops = $this->shopFacade->findLeafletShopsByCity($city, 50, true, Website::MODULE_KAUFINO);

        $this->template->topOffers = $this->offerFacade->findOffersByShop($shop, 15, true, Offer::TYPE_LEAFLET);

        $nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 50);

		$this->template->nearestCities = array_filter($nearestCities, static function ($city) use ($shop) {
			return $city->isActiveBrandsKaufino() && $shop->hasCity($city);
		});

        $this->template->cityStores = $this->geoFacade->findStoresByShopAndCity($shop, $city, 48);

        $similarLeaflets = null;
        if ($shop->getTag()) {
            $similarLeaflets = $this->leafletFacade->findLeafletsByTag($shop->getTag(), false, 30);
        }

        $this->template->similarLeaflets = array_filter($similarLeaflets ?? [], static function ($leaflet) use ($shop) {
            return $leaflet->getShop() !== $shop;
        });
    }

    public function renderStore(City $city, Shop $shop, ?Store $store = null): void
    {
        if ($store === null) {
            $this->redirect('City:shop', ['city' => $city, 'shop' => $shop]);
        }

        if ($store->getCity() !== $city) {
            $this->error('The store is not located in the city.');
        }

        if (!$city->isActive() && !$this->user->isLoggedIn()) {
            $this->redirectPermanent("Homepage:default");
        }

        if ($store->isActive() === false) {
            $this->redirectPermanent("City:shop", ['city' => $city, 'shop' => $shop]);
        }

        if ($shop->isActiveKaufino() === false) {
            $this->redirect('City:city', ['city' => $city]);
        }

        $this->template->leaflets = $this->leafletFacade->findLeafletsByCity($city, $store->getShop(), 20);

        $this->template->city = $city;
        $this->template->store = $store;
        $this->template->shop = $shop;

        if ($shop->getTag()) {
            $similarShops = $this->shopFacade->findLeafletShopsByTag($shop->getTag(), $shop->isStore(), 18, Website::MODULE_KAUFINO);
            $similarLeaflets = $this->leafletFacade->findLeafletsByTag($shop->getTag(), false, 60);
        } else {
            $similarShops = $this->shopFacade->findTopLeafletShops($shop->getLocalization(), $shop->isStore(), 18, $this->website->getModule(), !$shop->isStore());
        }

        $this->template->similarShops = array_filter($similarShops, static function ($shop) use ($city) {
            return $shop->hasCity($city);
        });

        $this->template->similarLeaflets = array_filter($similarLeaflets ?? [], static function ($leaflet) use ($shop) {
            return $leaflet->getShop() !== $shop;
        });

        $this->template->nearestStores = $this->storeFacade->findNearestStores($store, 10, Website::MODULE_KAUFINO);
        $this->template->nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 10, Website::MODULE_KAUFINO);
        // $this->template->nearestShops = $this->shopFacade->findNearestShopsByStore($store);

        $nearestCities = $this->geoFacade->findNearestCitiesByCity($city, 48);

        $this->template->topOffers = $this->offerFacade->findOffersByShop($shop, 15, true, Offer::TYPE_LEAFLET);

        $this->template->cities = array_filter($nearestCities, static function ($city) use ($shop) {
            return $shop->hasCity($city);
        });

        $this->template->nearestCities = array_slice($nearestCities, 0, 10);

        $this->template->distance = function(Store $otherStore) use ($store) {
            return $this->geoFacade->calculateDistance((float) $store->getLat(), (float) $store->getLng(), (float) $otherStore->getLat(), (float) $otherStore->getLng());
        };
    }

    public function renderTag(City $city, Tag $tag): void
    {
        if ($tag->getId() !== 1) {
            $this->redirect("Homepage:default");
        }

        $this->template->tag = $tag;
        $this->template->city = $city;

        $shops = $this->shopFacade->findLeafletShopsByCity($city, 100, true, Website::MODULE_KAUFINO);

        $leaflets = $this->leafletFacade->findLeafletsByTag($tag, false, 60);

        if (count($leaflets) === 0) {
            $leaflets = $this->leafletFacade->findLeafletsByShops($shops, 60);
        }

        $this->template->leaflets = $leaflets;
        $this->template->shops = $shops;
    }
}
