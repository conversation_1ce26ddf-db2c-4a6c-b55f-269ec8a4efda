<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Tracy\Debugger;

final class LeafletPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	public function renderDefault(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets($this->localization, false, null, $this->website->getModule());

		$this->template->leaflets = $leaflets;
	}

	public function actionLeaflet(Shop $shop, Leaflet $leaflet, $page = 1): void
	{
		if ($leaflet->getShop() != $shop) {
			$this->error('The leaflet is not owned by the shop.');
		}

		if ($leaflet->isDeleted() || $shop->isActiveKaufino() === false) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

		if (!$leaflet->hasPageByNumber($page)) {
			$this->redirectPermanent("Leaflet:leaflet", ['shop' => $shop, 'leaflet' => $leaflet]);
		}

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		$this->template->shop = $shop;
		$this->template->leaflet = $leaflet;

		$this->template->getSimilarLeaflets = (function() use ($shop, $leaflet) {
			return $this->leafletFacade->findLeafletsByShop($shop, 5, true, true, $leaflet);
		});

		$this->template->offers = $this->offerFacade->findOffersByLeaflet($leaflet, 20, true, Offer::TYPE_LEAFLET);

		$this->template->getRecommendedLeaflets = (function() use ($leaflet) {
			if ($leaflet->isNewsletter()) {
				return $this->leafletFacade->findNewsletters($this->localization, false, 5);
			}

			return $this->leafletFacade->findLeaflets($this->localization, false, 5, $this->website->getModule());
		});

		$this->template->similarShops = $this->shopFacade->findLeafletShops($this->localization, false, null, $this->website->getModule());

		$this->template->currentPage = $page;

		$this->template->leafletDescription = $this->contentGenerator->generateLeafletDescription($leaflet);

		if ($leaflet->isNewsletter() && $shop->useLeafletTemplateForNewsletters() === false) {
			$this->setView('leafletNewsletter');
		} else {
			$variant = $this->getGoogleOptimizeVariant();

			if ($variant === 0) {
				$this->setView('leaflet');
			} elseif ($variant === 1) {
				$this->setView('leaflet');
			} elseif ($variant === 2) {
				$this->setView('leaflet');
			} else {
				$this->setView('leaflet');

				Debugger::log('neočekávaná GoogleOptimize varianta: ' . $variant, 'unexpected-google-optimize-variant');
			}
		}
	}
}
