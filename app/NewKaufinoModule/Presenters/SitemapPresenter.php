<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Sitemap\Item;
use <PERSON><PERSON><PERSON>\Model\Sitemap\SitemapGenerator;

final class SitemapPresenter extends BasePresenter
{
	/**
	 * @var SitemapGenerator @inject
	 */
	public $sitemapGenerator;

	public function __construct(SitemapGenerator $sitemapGenerator)
	{
		$this->sitemapGenerator = $sitemapGenerator;
	}

	public function renderSitemap(): void
	{
		$this->disableCachedResponse = true;

		$feed =  $this->sitemapGenerator->generateFeed($this->website, $this->getName());
		$feed->addItem(new Item($this->link('//Homepage:default'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
		$feed->addItem(new Item($this->link('//Shops:shops'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
		$feed->addItem(new Item($this->link('//Leaflets:leaflets'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
		$feed->addItem(new Item($this->link('//Offers:offers'), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
		$feed->addItem(new Item($this->link('//Static:aboutUs'), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));

		$this->template->feed = $feed;
	}
}
