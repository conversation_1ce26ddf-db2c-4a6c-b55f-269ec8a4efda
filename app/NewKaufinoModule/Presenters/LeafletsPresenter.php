<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class LeafletsPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

	public function actionLeaflets(): void
	{
		$this->responseCacheTags[] = 'leaflets';

		$this->template->leaflets = $this->leafletFacade->findLeaflets($this->localization, false, 100, Website::MODULE_KAUFINO);
		$this->template->leafletsTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_SHOPS, 11);
		$this->template->cities = $this->geoFacade->findCities($this->localization, 20);
	}
}
