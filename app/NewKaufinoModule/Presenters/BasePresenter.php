<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Content\ContentGenerator;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\GoogleOptimize\GoogleOptimize;
use <PERSON><PERSON><PERSON>\Model\GoogleOptimize\GoogleOptimizeExperiment;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoGenerator;
use Ka<PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use Ka<PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Router\RouterFactory;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;
use Tracy\Debugger;

abstract class BasePresenter extends \Kaufino\Presenters\BasePresenter
{
	/** @var EntityManager @autowire */
	public $entityManager;

	/** @var SeoGenerator @inject */
	public $seoGenerator;

	/** @var ContentGenerator @inject */
	public $contentGenerator;

	/** @var ITranslator @autowire */
	public $translator;

	/** @var string @persistent */
	public $region;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var SeoFacade @inject */
	public $seoFacade;

	/** @var GoogleOptimize @inject */
	public $googleOptimize;

	/** @var GoogleOptimizeExperiment|null */
	protected $googleOptimizeExperiment;

    /** @var GeoFacade @inject */
    public $geoFacade;

    public function __construct()
    {
        parent::__construct();

        $this->disableCachedResponse = true;
    }

	protected function startup()
	{
		parent::startup();

        if ($this->getUser()->isLoggedIn() === false) {
            $this->error('Not found');
        }

		if ($this->getParameter('region') === 'no') {
			$this->redirect('this', ['region' => 'no']);
		}

		$region = $this->region ? $this->region : $this->getParameter('region');

		if (!$region) {
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->localization) {
			# Debugger::log('Region does not exist.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->website->isActive() && !$this->getUser()->isLoggedIn()) {
			# Debugger::log('Website is not active.');
			$this->redirect('this', ['region' => null]);
		}

		if ($this->getParameter('cookie') && $this->getParameter('cookie') === 'wVPkTDuR8QSQXKsU') {
			$this->getHttpResponse()->setCookie('d2s0KZA1rp9pwsRI9n0l', 'Rj1Z53FM17fL6nskc5NG', new \DateTime('+ 1 month'));

			$this->redirect('this');
		}

		if ($this->getUser()->isLoggedIn()) {
			$this->template->userLoggedIn = true; // only for starting session purpose
		}

		$pageExtensionSlug = Strings::substring($this->getHttpRequest()->getUrl()->getPath(), 4);
		$this->template->pageExtension = $this->seoFacade->findPageExtensionBySlug($this->website, $pageExtensionSlug);
		$this->template->pageExtensionSlug = $pageExtensionSlug;

		$this->template->allPageCacheAllowed = false;
		$this->template->canonicalUrl = $this->seoGenerator->generateCanonicalUrl($this->getHttpRequest()->getUrl()->getAbsoluteUrl());
		$this->template->translator = $this->translator;
		$this->template->headerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, false, 10, $this->website->getModule());
		};
		$this->template->footerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, false, 10, $this->website->getModule());
		};


		$this->template->footerOffersTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 11);
		};
		$this->template->footerWebsites = function () {
			return $this->websiteFacade->findActiveWebsites($this->website->getModule());
		};

		$this->template->getLocalizedUrl = function(Website $website, ?string $path = null) {
			return RouterFactory::getLocalizedUrl($website, $path);
		};

        $this->template->cities = $this->geoFacade->findCities($this->localization, 36);

		$this->initGoogleOptimize();
	}

	protected function initGoogleOptimize() {
		$this->googleOptimizeExperiment = new GoogleOptimizeExperiment('UsGY6n_FQF6PF45uD1Cq6w', 3);

		$group = $this->googleOptimize->getVariant('group', 3);
		$this->googleOptimizeExperiment->setVariant($group % $this->googleOptimizeExperiment->getCountOfVariants());

		$this->template->googleOptimizeExperiment = $this->googleOptimizeExperiment;

		Debugger::barDump('experimentId: ' . $this->googleOptimizeExperiment->getExperimentId() . ';group: ' . $group . ';variant: ' . $this->googleOptimizeExperiment->getVariant());
	}

	protected function getGoogleOptimizeVariant() {
		if (!$this->googleOptimizeExperiment) {
			return null;
		}

		return $this->googleOptimizeExperiment->getVariant();
	}
}
