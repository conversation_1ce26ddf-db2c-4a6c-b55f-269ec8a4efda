<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewKaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON>\Debugger;

final class SearchPresenter extends BasePresenter
{
	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function actionSearch(?string $q = null): void
	{
        $this->disableCachedResponse = true;

        if ($q === null) {
            $this->redirect('Homepage:default');
        }

		$this->template->query = $q;
		$this->template->shops = $this->shopFacade->findLeafletShopsByFulltext($this->localization, $q, false, 10, Website::MODULE_KAUFINO);
		$this->template->leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($q, $this->localization);

        Debugger::log('Search query: ' . $q, 'search');
	}
}
