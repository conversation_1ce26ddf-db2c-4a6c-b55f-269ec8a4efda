{block title}
    {_oferto.shop.stores.title, ['shopName' => $shop->getName()]}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_"$websiteType.navbar.leaflets"}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag() !== null">{$shop->getTag()->getName()}</a> |
            <a n:href="shop $shop" class="link">{$shop->getName()}</a> |
            <span class="color-grey">{_oferto.shop.stores.title, ['shopName' => $shop->getName()]}</span>
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">
        <div class="leaflet__content">
            <div class="w100">                
                <div class="k-profile-header k-profile-header--sm-center">                    
                    <a n:href="Shop:shop $shop" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:160,140}" width="160" height="140" alt="{$shop->getName()}" class="lazyload k-profile-header__logo">
                        </picture>							
                    </a>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {_oferto.shop.stores.title, ['shopName' => $shop->getName()]}
                        </h1>                                                                    
                    </div>                                       
                </div>       

                {if $localization->isHungarian()}
                    <div class="k-tabs k-content">
                        <div class="k-tabs__buttons">
                            <a n:href="shop, $shop" class="k-tabs__tabLink">                                
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {$shop->getName()}
                            </a>
                            <a n:href="offers, $shop" class="k-tabs__tabLink">                                
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {_oferto.shop.offers.title, ['shopName' => $shop->getName()]}
                            </a>
                            <a n:href="stores, $shop" class="k-tabs__tabLink active">                                
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {_oferto.shop.stores.title, ['shopName' => $shop->getName()]}
                            </a>                            
                        </div>                        
                    </div>                    
                {/if} 

                <div n:if="count($stores) > 0">
                    <div class="k-offers">
                        <div class="k-offers">
                            {foreach $stores as $store}
                                <span class="k-tag__inner {$iterator->counter > 6 ? 'hidden'}">
                                <a n:href="City:store $store->getCity(), $store->getShop(), $store" class="k-tag__item">{$store->getShop()->getName()} {$store->getStreet()}</a>
                            </span>
                            {/foreach}
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="float-wrapper__stop"></div>
</div>
