<div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
    <div class="p-1.5 md:p-2 bg-orange-light rounded-xl h-full">
        <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
            <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="w-full relative">
                <picture n:if="$leaflet->getFirstPage()">
                    <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                    <img src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="rounded-lg w-full" loading="lazy">
                </picture>                
            </a>
            <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                <button class="rounded-md text-xs md:text-sm font-medium leading-[24.5px] text-white bg-orange py-1 px-2.5">Nový leták</button>
                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                    Otevřít
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </a>
            </div>
        </div>
        <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
            <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
            <div class="leading-[21px]">
                <div class="text-xs md:text-lg font-medium line-clamp-1">
                    {if $leaflet->isChecked() === false}
                        {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                    {else}
                        {$leaflet->getName()}
                    {/if}
                </div>
                <div class="text-xs font-light" n:if="$leaflet->isChecked()">
                    {$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}
                </div>
            </div>
        </div>
    </div>
</div>