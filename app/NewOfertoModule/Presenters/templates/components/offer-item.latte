{var $offerLeaflet = $offer->getLeafletPage()->getLeaflet()}
<div class="relative transition-transform duration-300 transform hover:scale-[102%] cursor-pointer {if $offer->isExpired()}expired{/if} {isset($cssClass) ? $cssClass}">
    {if $user->isLoggedIn()}
        <a n:href=":Admin:Offer:archive, $offer->getId(), $presenter->link('this')" style="position: absolute; top: 10px; right: 10px; background-color: #c30404; color: white; border-radius: 50%; padding: 3px 5px;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" style="width: 20px; height: 20px">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
            </svg>
        </a>
    {/if}

    <div class="p-1.5 md:p-2 bg-white rounded-xl">
        <div class="flex mb-[14px] md:mb-[17px] relative">
            <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet, page => $offer->getLeafletPage()->getPageNumber()}" class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></a>
            <div class="w-full">
                <picture>
                    <source 
                        srcset="
                            {$offer->getImageUrl()|image:150,150,'fit','webp'} 1x,
                            {$offer->getImageUrl()|image:300,300,'fit','webp'} 2x                            
                        " 
                        type="image/webp"
                    >            
                    <img
                        loading="lazy" 
                        src="{$offer->getImageUrl()|image:150,150,'fit'}"                             
                        srcset="
                            {$offer->getImageUrl()|image:150,150,'fit'} 1x,
                            {$offer->getImageUrl()|image:300,300,'fit'} 2x
                        "
                        data-sizes="auto"
                        width="150" 
                        height="150" 
                        alt="{$offer->getName()}" 
                        class="w-full cover max-h-[231px] rounded-lg"                    
                    >
                </picture>                  
            </div>
            <div class="absolute bottom-1 right-1 z-10">
                <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet, page => $offer->getLeafletPage()->getPageNumber()}" class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                    Otevřít
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </a>
            </div>
        </div>
        <div class="pl-1 md:pl-2 pb-[2px] md:pb-1.5">
            {if !isset($hideTags)}
                {foreach $offer->getTags() as $tag}
                    <a n:href="Tag:tag $tag" class="mb-2 md:mb-0 inline-flex text-xs font-light pt-[3px] pb-1 px-2 border border-light-3 rounded">
                        {$tag->getName()}                                            
                    </a>
                    {breakIf $iterator->getCounter() > 1}
                {/foreach}
            {/if}

            {if false && !isset($hideShop)}
                <a n:href="Shop:shop $offerLeaflet->getShop()">
                    {$offer->getShop()->getName()}
                </a>
            {/if}  

            <div>
                <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$offer->getLeafletPage()->getPageNumber()}" class="text-sm md:text-base font-light leading-[24.5px] md:leading-7">{$offer->getName()}</a>
                <div class="md:text-lg font-semibold leading-7 md:leading-[31.5px] text-primary">
                    <small n:if="$offer->getCommonPrice() > 0">{$offer->getCommonPrice()|price:$offer->getLocalization()}</small>
                    <strong>{$offer->getCurrentPrice()|price:$offer->getLocalization()}</strong>

                    {if $offer->isFutureOffer()}
                        {var $color = $offer->getValidSinceDays() <= 2 ? 'green' : ''}

                        <span class="k-offers__tag {$color |noescape}">
                            {if $offer->getValidSinceDays() <= 6}
                                {_kaufino.offer.offerItem.validSinceDays, [count => $offer->getValidSinceDays()]}
                            {else}
                                {_kaufino.offer.offerItem.validSinceWeek}
                            {/if}
                        </span>
                    {else}
                    {var $color = $offer->getValidTillDays() === 0 ? 'red' : ($offer->getValidTillDays() <= 2 ? 'orange' : '')}
                        <span class="k-offers__tag {$color |noescape}">
                            {if $offer->getValidTillDays() <= 0}
                                {_kaufino.offer.offerItem.validTillToday}
                            {elseif $offer->getValidTillDays() <= 6}
                                {_kaufino.offer.offerItem.validTillDays, [count => $offer->getValidTillDays()]}
                            {else}
                                {_kaufino.offer.offerItem.validTillWeek}
                            {/if}
                        </span>
                    {/if}                    
                </div>
                {*$offer->getDescription()*}
            </div>
        </div>
    </div>
</div>
            