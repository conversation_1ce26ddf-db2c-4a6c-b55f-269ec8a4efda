{block scripts}
    {include parent}

    <script type="application/ld+json" n:if="count($leaflets)">
        {
            "@context": "https://schema.org",
            "@type": "OfferCatalog",
            "name": {_"$websiteType.homepage.h1"},
            "url": {$presenter->link('//this')},
            "itemListElement": [
                {foreach $leaflets as $key => $leaflet}
                    {
                        "@type": "SaleEvent",
                        "name": {$leaflet->getName()},
                        "url": {$presenter->link('//Leaflet:leaflet', $leaflet->getShop(), $leaflet)},
                        "startDate": {$leaflet->getValidSince()|date:'Y-m-d'},
                        "endDate": {$leaflet->getValidTill()|date:'Y-m-d'},
                        "location": {
                            "@type": "Place",
                            "name": {$leaflet->getShop()->getName()},
                            "url": {$presenter->link('//Shop:shop', $leaflet->getShop())}
                        }
                    }{sep},{/sep}
                {/foreach}
            ]
        }
    </script>
{/block}

{block description}{_"$websiteType.leaflets.text"}{/block}

{block title}{_"$websiteType.leaflets.title"}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">{if $localization->isCzech()}{_"$websiteType.leaflets.h1"}{else}{_"$websiteType.leaflets.title"}{/if}</h1>
        <div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
            <a n:href="Homepage:default">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                    <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
            
            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>

            <span>{_"$websiteType.navbar.leaflets"}</span>
        </div>
        <p class="text-sm font-light leading-[22px] text-[#646C7C]">{_"$websiteType.leaflets.text"}</p>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-5">
        {foreach $leaflets as $leaflet}
            {include '../components/leaflet.latte', leaflet => $leaflet}          
        {/foreach}
    </div>
</div>
