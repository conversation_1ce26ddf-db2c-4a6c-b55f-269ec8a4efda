{block scripts}
    {include parent}
{/block}

{block description}{_"$websiteType.shops.metaDescription"}{/block}
{block title}{_"$websiteType.shops.metaTitle"}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">{if $localization->isCzech()}{_"$websiteType.shops.h1"}{else}{_"$websiteType.shops.title"}{/if}</h1>
        <div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
            <a n:href="Homepage:default">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                    <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
            
            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>
            
            <span>{_"$websiteType.navbar.shops"}</span>
        </div>
        <p class="text-sm font-light leading-[22px] text-[#646C7C]">{_"$websiteType.shops.text"}</p>
    </div>
    
    <div class="grid grid-cols-3 md:grid-cols-5 gap-x-[7px] gap-y-[13px] md:gap-x-3 md:gap-y-5 mb-10">    
        {foreach $shops as $shop}
            <a n:href="Shop:shop $shop" class="bg-light-6 pt-2 pb-[9px] md:pb-[17px] px-2 rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
                <div class="flex justify-center items-center bg-white px-2 py-10 md:py-20 mb-[7px] md:mb-[17px] rounded-lg">
                    <picture>
                        <source srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" loading="lazy">
                    </picture>
                </div>

                <div class="md:px-2">
                    <div class="hidden md:inline text-xs font-light px-2 py-1 border border-light-3 rounded">Obchod</div>
                    <div class="text-sm md:text-base uppercase leading-7 md:mt-2.5">{$shop->getName()}</div>
                </div>            

                {if $user->isLoggedIn()}
                    {var $countOfCurrentAndFutureLeaflets = count($shop->getCurrentAndFutureLeaflets())}
                    {var $countOfCurrentAndFutureNewsletter = count($shop->getCurrentAndFutureNewsletter())}
                    
                    {if !$countOfCurrentAndFutureLeaflets && !$countOfCurrentAndFutureNewsletter}
                        <span class="absolute top-0 right-0 bg-primary p-2 rounded-lg">0</span>
                    {else}
                        <span class="absolute top-0 right-0 bg-green p-2 rounded-lg">{$countOfCurrentAndFutureLeaflets} / {$countOfCurrentAndFutureNewsletter}</span>
                    {/if}
                {/if}
            </a>
        {/foreach}
    </div>
</div>
