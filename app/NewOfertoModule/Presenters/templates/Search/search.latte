{block scripts}
    {include parent}
{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 n:block="title" class="k__title ta-center mt-5 mb-0">{_"$websiteType.search.title", [query => $query]}</h1>
    </div>

    {if count($shops) > 0}
        <div class="k-shop">    
            {foreach $shops as $shop}
                <a n:href="Shop:shop $shop" class="k-shop__item">
                    <span class="k-shop__image-wrapper">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                        </picture>                        
                    </span>
                    <small class="k-shop__title">{$shop->getName()}</small>
                </a>
            {/foreach}
        </div>
    {else}
        {*<div class="alert alert-info mx-3">{_"$websiteType.search.noResults"}</div>*}
    {/if}

    {if count($leafletPages) > 0}
        <div class="k-leaflets__wrapper k-leaflets__wrapper--4">
            {foreach $leafletPages as $leafletPage}
                {var $leaflet = $leafletPage->getLeaflet()}
                <div class="k-leaflets__item mb-5">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet, $leafletPage->getPageNumber()" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                        <picture>
                            <source data-srcset="{$leafletPage->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                        </picture>
                    </a>
                    <div class="k-leaflets__title mt-0 mb-0">
                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet, $leafletPage->getPageNumber()" class="color-black">
                            {$leaflet->getName()}
                        </a>
                    </div>
                    <p class="k-leaflets__date mt-0 mb-0">{($leaflet->getValidSince())|localDate} - {($leaflet->getValidTill())|localDate:'long'}</p>
                </div>
            {/foreach}
        </div>
    {else}
        <div class="alert alert-info mx-3">{_"$websiteType.tag.noLeaflets"}</div>
    {/if}
</div>
