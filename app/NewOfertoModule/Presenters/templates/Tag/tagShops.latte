{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.tag.metaTitle, [tag => $tag->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.tag.text, [tag => $tag->getName()]}
    {/if}
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_kaufino.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$tag->getName()},
                "item": {link //Tag:tag $tag}
        }
  ]
}
    </script>
{/block}

{block content}

<div class="container">
    <div class="mt-6 mb-6">
        <h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">
            {if $pageExtension && $pageExtension->getHeading()}
                {$pageExtension->getHeading()}
            {else}
                {_kaufino.tag.title, [tag => $tag->getName()]}
            {/if}
        </h1>
        <div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
            <a n:href="Homepage:default">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                    <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
            
            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>

            <span>{$tag->getName()}</span>
        </div>
        <p class="text-sm font-light leading-[22px] text-[#646C7C]">
            {if $pageExtension && $pageExtension->getShortDescription()}
                {$pageExtension->getShortDescription()}
            {else}
                {_kaufino.tag.text, [tag => $tag->getName()]}
            {/if}
        </p>
    </div>

    <div n:if="count($shops) > 0" class="">
        <div class="k-shop">
            {foreach $shops as $shop}
                <a n:href="Shop:shop $shop" class="k-shop__item">
                    <span class="k-shop__image-wrapper">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                        </picture>
                    </span>
                    <small class="k-shop__title">{$shop->getName()}</small>
                </a>
            {/foreach}
        </div>

        <p n:if="count($shops) > 11" class="d-flex">
            <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
        </p>
    </div>

    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">
        {_kaufino.tag.titleWithTag, [tag => $tag->getName()]}
    </h2>

    {if count($leaflets) > 0}
        <div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-5">                        
            {foreach $leaflets as $leaflet}                            
                {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
            {/foreach}                        
        </div>

        <p n:if="count($leaflets) > 17" class="d-flex">
            <button class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.showMore.leaflets'} »</button>
        </p>    
    {/if}

    <div n:if="count($cities)">
        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
            {_kaufino.tag.citiesWithTag, [tag => $tag->getName()]}
        </h2>

        <div class="k-tag mb-5">
            {foreach $cities as $city}
                <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                    <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                </span>
            {/foreach}

            <p n:if="count($cities) > 11" class="d-flex w100">
                <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
            </p>
        </div>
    </div>                

    <div class="k-content">
        {cache md5($tag->getDescription()), expire => '20 minutes'}
                {$tag->getDescription()|content|noescape}
        {/cache}
    </div>          
</div>
