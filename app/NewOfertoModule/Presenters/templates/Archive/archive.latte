{block scripts}
    {include parent}
{/block}

{block robots}noindex, follow{/block}

{block title}{_"$websiteType.leaflets.expiredMetaTitle", [brand: $shop->getName()]}{/block}
{block description}{_kaufino.leaflets.expiredText}{/block}

{block content}
<div class="container">
    <div class="flex flex-col lg:flex-row lg:items-center justify-between pt-[34px] mb-11">
        <div class="flex flex-row items-center md:items-start gap-[15px] mb-3 lg:mb-0">
            <picture>
                <source srcset="{$shop->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                <img src="{$shop->getLogoUrl() |image:160,140}" width="160" height="140" alt="{$shop->getName()}" class="w-[60px] h-[60px] rounded">
            </picture>            

            <div>
                <h1 class="text-[24px] font-medium leading-[34px]">
                    {_"$websiteType.leaflets.expiredTitle"}
                </h1>
                <div class="hidden md:flex gap-[9px] items-center font-light text-sm text-[#646C7C]">
                    <a n:href="Homepage:default">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                            <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                    
                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <a n:href="Shops:shops">                        
                        {_"$websiteType.navbar.shops"}
                    </a>

                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <a n:if="$shop->getTag() !== null" n:href="Tag:tag $shop->getTag()">                        
                        {$shop->getTag()->getName()}
                    </a>                    

                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <a n:href="Shop:shop $shop">{$shop->getName()}</a>

                    <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                        <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
                    </svg>

                    <span>Archív</span>
                </div>
                <p class="font-light leading-7 mb-[33px] text-grey-description max-w-[714px]">{_"$websiteType.leaflets.expiredText"}</p>
            </div>
        </div>

        <div class="flex items-center gap-5 text-sm leading-[24.5px] text-[#646C7C] font-light py-3 pl-[21px] pr-[33px] bg-light-6 rounded-lg transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.56252 14.0001C6.56252 9.58103 9.60595 5.39453 14 5.39453C18.3941 5.39453 21.4375 9.58103 21.4375 14.0001C21.4375 16.4743 21.8327 17.9988 22.1922 18.8743C22.3721 19.3122 22.5443 19.5913 22.6575 19.7488C22.7142 19.8277 22.7566 19.877 22.7785 19.9009C22.7851 19.9081 22.7899 19.9131 22.7927 19.9159C22.8111 19.9328 22.8289 19.9504 22.8458 19.9688C22.9017 20.0291 22.9476 20.0956 22.9834 20.1662C23.0414 20.2801 23.0752 20.4084 23.078 20.5444C23.0787 20.5808 23.0772 20.6174 23.0735 20.6537C23.0698 20.6892 23.0639 20.7245 23.0559 20.7594C23.0248 20.8949 22.9608 21.025 22.8631 21.1372C22.7582 21.2576 22.6277 21.3424 22.4867 21.3906C22.3906 21.4234 22.2896 21.4392 22.1887 21.4375H5.81165C5.76516 21.4383 5.71868 21.4354 5.67267 21.4288C5.61541 21.4207 5.55994 21.407 5.5068 21.3883C5.36578 21.3389 5.23554 21.2527 5.13151 21.1309C4.98555 20.96 4.91689 20.7491 4.92216 20.5408C4.92547 20.4049 4.95976 20.2767 5.01822 20.163C5.05542 20.0905 5.10329 20.0223 5.16163 19.9608C5.17799 19.9436 5.19505 19.927 5.21276 19.9111C5.21541 19.9084 5.21977 19.9039 5.2257 19.8975C5.24696 19.8746 5.2888 19.8267 5.345 19.7492C5.45709 19.5947 5.62872 19.3191 5.80819 18.8836C6.16705 18.0126 6.56252 16.4887 6.56252 14.0001ZM20.5734 19.539C20.5943 19.5899 20.6152 19.6394 20.6362 19.6875H7.36812C7.38752 19.6429 7.4069 19.5972 7.42622 19.5503C7.88767 18.4303 8.31252 16.673 8.31252 14.0001C8.31252 10.2162 10.8735 7.14453 14 7.14453C17.1266 7.14453 19.6875 10.2162 19.6875 14.0001C19.6875 16.6601 20.1126 18.4168 20.5734 19.539ZM13.2344 23.8438C13.2344 23.3605 12.8426 22.9688 12.3594 22.9688C11.8761 22.9688 11.4844 23.3605 11.4844 23.8438C11.4844 24.5109 11.7493 25.1508 12.2212 25.6227C12.693 26.0945 13.3329 26.3594 14 26.3594C14.6671 26.3594 15.307 26.0945 15.7789 25.6227C16.2507 25.1508 16.5156 24.5109 16.5156 23.8438C16.5156 23.3605 16.1239 22.9688 15.6406 22.9688C15.1574 22.9688 14.7656 23.3605 14.7656 23.8438C14.7656 24.0469 14.685 24.2417 14.5414 24.3852C14.3979 24.5287 14.2031 24.6094 14 24.6094C13.7969 24.6094 13.6021 24.5287 13.4586 24.3852C13.3151 24.2417 13.2344 24.0469 13.2344 23.8438Z" fill="#646C7C"/>
            </svg>
            <div>
                <span class="font-medium">Upozornit,</span> až vyjde nový leták
            </div>
        </div>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-5 gap-3 mb-5">
        {foreach $leaflets as $leaflet}
            {include '../components/leaflet.latte', leaflet => $leaflet, validBadgeShow => true}
        {/foreach}
    </div>
</div>
