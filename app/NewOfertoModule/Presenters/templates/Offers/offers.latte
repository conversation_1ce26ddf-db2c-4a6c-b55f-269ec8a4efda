{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "ItemList",
            "itemListElement": [
              {foreach $offers as $offer}
                  {var $offerLeaflet = $offer->getLeafletPage()->getLeaflet()}
                {
                    "@type": "Product",
                    "name": {$offer->getName()},
                    "url": {link //Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet, page => $offer->getLeafletPage()->getPageNumber()},
                    "image": {$offer->getImageUrl()|image:300,300,'fit','webp'},
                    "offers": {
                        "@type": "Offer",
                        "priceCurrency": {_oferto.currency},
                        "price": {$offer->getCurrentPrice()},
                        "availability": "https://schema.org/InStock",
                        "seller": {
                            "@type": "Organization",
                            "name": {$offer->getShop()->getName()}
                        }
                    },
                    "priceValidUntil": {$offer->getvalidTill()|date:'Y-m-d'},
                    "priceSpecification": {
                      "@type": "UnitPriceSpecification",
                      "price": {$offer->getCurrentPrice()},
                      "priceCurrency": {_oferto.currency},
                      {if $offer->getCommonPrice() && $offer->getCommonPrice() > $offer->getCurrentPrice()}
                      "referencePrice": {$offer->getCommonPrice()},
                      {/if}
                      "unitText": {_oferto.currency}
                    }
                }{sep},{/sep}
            {/foreach}
            ]
        }
    </script>
{/block}

{block description}{_kaufino.offer.metaDescription}{/block}

{block content}
<div class="container">
    <div class="mt-6 mb-6">
        <h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">{_kaufino.offer.title}</h1>
        <div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
            <a n:href="Homepage:default">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                    <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
            
            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>

            <span>{_"$websiteType.navbar.offers"}</span>
        </div>
        <p class="text-sm font-light leading-[22px] text-[#646C7C]">{_kaufino.offer.text}</p>
    </div>


    <div n:if="count($offersTags) > 0" class="mb-6">                    
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {foreach $offersTags as $tag}
                <a n:href="Tag:tag $tag" class="flex items-center justify-between font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
                    {$tag->getName()}
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                        <path d="M3.53538 11.3891L10.6064 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>                
            {/foreach}
        </div>
    </div>

    <div n:if="count($offers) > 0">
        <h2 id="offers" class="k__title ta-center mt-4 mt-sm-5 mb-4">{_kaufino.offer.whatOnSale}</h2>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {foreach $offers as $offer}
                {continueIf !$offer->getLeafletPage()}
                {include '../components/offer-item.latte', offer => $offer}
            {/foreach}
        </div>
    </div>
</div>
