<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewOfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Articles\Entities\Article;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;

final class ArticlesPresenter extends BasePresenter
{
	/** @var ArticleFacade @inject */
	public $articleFacade;

	public function actionArticles(): void
	{
		$this->template->articles = $this->articleFacade->findArticles($this->website->getParentWebsite());
	}
}
