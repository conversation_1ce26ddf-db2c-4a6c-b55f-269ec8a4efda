<?php

namespace <PERSON><PERSON><PERSON>\NewOfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class ArchivePresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function renderArchive(Shop $shop)
	{
		$this->template->shop = $shop;
		$this->template->leaflets = $this->leafletFacade->findArchivedLeaflets($shop);
	}
}
