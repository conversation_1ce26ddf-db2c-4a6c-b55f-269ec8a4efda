{import 'components/form.latte'}

<!DOCTYPE html>
<html lang="{$localization->getLocale()}">
<head>
	<meta charset="utf-8">
	{capture $headTitle|spaceless}
		{ifset title}{include title|stripHtml}{/ifset}
	{/capture}

	<title>{if strlen($headTitle) > 55}{$headTitle}{else}{$headTitle} | MrOferto{/if}</title>
	<meta name="keywords" content="">
    <meta name="description" content="{ifset description}{include description|stripHtml|spaceless}{/ifset}">
	<meta name="author" content="MrOferto">
	{if $channel === null}
		<meta name="robots" content="{block #robots|stripHtml|trim}index,follow{/block}">
	{else}
		<meta name="robots" content="noindex,nofollow">
	{/if}
	<link rel="canonical" href="{$canonicalUrl}">

	<meta property="og:title" content="{ifset title}{include title|stripHtml|spaceless} | {/ifset}MrOferto" />
    <meta property="og:site_name" content="MrOferto"/>
    <meta property="og:url" content="{link //this}" />
    <meta property="og:description" content="{ifset description}{include description|stripHtml}{/ifset}" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="{ifset image}{include image |strip}{else}{$baseUrl}/images/1200x627_0G_oferto.jpg{/ifset}" />
    <meta property="fb:app_id" content="" />

	<meta name="twitter:card" content="summary" />

	<!-- Viewport for mobile devices -->    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">

	<link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/oferto/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/oferto/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/oferto/favicon-16x16.png">
	<link rel="icon" type="image/png" sizes="192x192"  href="{$basePath}/images/favicon/oferto/android-chrome-192x192.png">
	<link rel="icon" type="image/png" sizes="512x512"  href="{$basePath}/images/favicon/oferto/android-chrome-512x512.png">
{*	<link rel="manifest" href="{$basePath}/images/favicon/oferto/site.webmanifest">*}
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">

	<meta name="google-site-verification" content="7ypcHzpCB0upyEy8VKX_9akp-hXM0zMSokKA2MMVQtk" />
	<meta name="ahrefs-site-verification" content="5ec5be26cfe58d5e25899ff9921ea420afd8c7d87d68794943404438899a4f15">

	<link rel="stylesheet" href="/css/nice-select2.css">

	{ifset hreflang}{include hreflang}{/ifset}

	{if $localization->isCzech()}
		<meta name="facebook-domain-verification" content="i70fvp4k9svpfba39gf9a8lyrhrgm9" />		
		<meta name="seznam-wmt" content="77pAh8rrEpDTHwWEdBcK70rOPnbNTfip" />
	{/if}

	{if $localization->isSlovak()}
		<meta name="facebook-domain-verification" content="pxw1anm59hacu1p4c82sjv5ztoixfd" />
	{/if}

	{if $localization->isPolish()}
		<meta name="facebook-domain-verification" content="makvbqwyjp1r4iwoienhuuxcjuv5i1" />
	{/if}

	{if $localization->isRomanian()}
		<meta name="facebook-domain-verification" content="p7oq3v11nxw7xztntduhh0ezmgqvv7" />
	{/if}

	{if $localization->isHungarian()}
		<meta name="facebook-domain-verification" content="ji7c6hwij8i2ykatryxje9wbxnez2u" />
	{/if}

	{if $localization->isCroatian()}
		<meta name="facebook-domain-verification" content="jmyf4xp49c9h66py6rh5kglrjqq1xx" />
	{/if}

	{if $localization->isItaly()}
		<meta name="facebook-domain-verification" content="c4a20j56dyt4je8d3ktxc753au2ghz" />
	{/if}

	{if $localization->isCanadian()}
		<meta name="facebook-domain-verification" content="33vkclncg7zoiwxhwsq3zuopk2nt8y" />
	{/if}

	{if $localization->isNetherlandian()}
		<meta name="facebook-domain-verification" content="uzhd9ocu2cd6lxrco2dmcjn05hve54" />
	{/if}

	{if $localization->isDenmarkian()}
		<meta name="facebook-domain-verification" content="m32fiiw0rc6znj5uupj2ll082xl4yw" />
	{/if}

	{if $localization->isGermany()}
		<meta name="facebook-domain-verification" content="wtza7igp782w7k2mexj7k1icis45ek" />
	{/if}

	{if $localization->isBelgian()}
		<meta name="facebook-domain-verification" content="b4ojcsmuvak26533ltrtbbbozv3yyy" />
	{/if}

	{if $localization->isGreecian()}
		<meta name="facebook-domain-verification" content="mouit0enbes04ouumjp0leechm9chy" />
	{/if}

	{if $localization->isJar()}
		<meta name="facebook-domain-verification" content="mwqtufcs1t0ls6k4xogcetkuowv5rs" />
	{/if}

	{if $localization->isFrancian()}
		<meta name="facebook-domain-verification" content="xj9igj0w15rkrglz8hss9lcsahgqke" />
	{/if}

	{if $localization->isFinlandian()}
		<meta name="facebook-domain-verification" content="uh9gal4zufpeb75rjyn3msiabjq1x6" />
	{/if}

	{if $localization->isSwedian()}
		<meta name="facebook-domain-verification" content="zx9e96npc977iyjau2tlj9q8rifr02" />
	{/if}
	
	{if $localization->isNorwaian()	}
		<meta name="facebook-domain-verification" content="et6ubent4a3emk5jk1o8gxiot8pfzw" />
	{/if}

	{if $localization->isAustrian()}
		<meta name="facebook-domain-verification" content="dms0w979bks3mv2spfnz763mofro8t" />
	{/if}

	{if $localization->isSwitzerlandian()}
		<meta name="facebook-domain-verification" content="k6azdscy8trjrwhqac6g8zozn36mjd" />
	{/if}
	
	{if $localization->isPortuguesian()}
		<meta name="facebook-domain-verification" content="2bpqp094h5onmcug1fmh0qge4jlamu" />
	{/if}
	
	{if $localization->isSpaian()}
		<meta name="facebook-domain-verification" content="o72bpgkdnos8r4rqvg1ce3cehfm5ki" />
	{/if}

	{if $localization->isGreatbritian()}
		<meta name="facebook-domain-verification" content="kszxgvcing4ha4rjqsy9i85yrkg45b" />
	{/if}

	{if $localization->isSlovenian()}
		<meta name="facebook-domain-verification" content="0xl0eg45wyigm30w1qdyl9qh24jauc" />
	{/if}

	{if $localization->isLatvian()}
		<meta name="facebook-domain-verification" content="1pzfc3r0sgvil1kfkpqoxph96fdrcn" />
	{/if}

	{if $localization->isSerbian()}
		<meta name="facebook-domain-verification" content="tb9u6fzxfa4mtqfhnhv8avip6fbk3b" />
	{/if}

	{if $localization->isBulgarian()}
		<meta name="facebook-domain-verification" content="9izowtgq0izdb6mp0e18vna4g0ujtn" />
	{/if}

	{if $localization->isLithuanian()}
	{/if}

	{if $localization->isEstonian()}
		<meta name="facebook-domain-verification" content="e16cbvkwmasquqr6gn6qzroj3ufp8p" />
	{/if}

	{if $localization->isMoldavian()}
		<meta name="facebook-domain-verification" content="7b95isuugw0w49vn6ejdhu3mgkxibq" />
	{/if}	

	{var $version = 0.72}
	<link rel="stylesheet" href="{$basePath}/css/main.oferto.css?v={$version}">

	{block head}{/block}

	<script>
		dataLayer = [{
		}];
	</script>
	<script n:if="isset($googleOptimizeExperiment)">
		dataLayer.push({
			'expId': {$googleOptimizeExperiment->getExperimentId()},
			'expVar': {$googleOptimizeExperiment->getVariant()}
		});
	</script>	

	{if $localization->isCzech()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WC5TC26');</script>
		<!-- End Google Tag Manager -->	
	{/if}

	{if $localization->isSlovak()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KZH5WLL');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isPolish()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WGK8HS5');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isRomanian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KH9SVTS');</script>
		<!-- End Google Tag Manager -->		
	{/if}

	{if $localization->isHungarian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-TMWCLVZ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isCroatian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-M62XL7C');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isItaly()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-P34SLSN');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isCanadian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-T45TR7K');</script>
		<!-- End Google Tag Manager -->

	{/if}

	{if $localization->isNetherlandian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5D7XSD4');</script>
		<!-- End Google Tag Manager -->	
	{/if}	

	{if $localization->isDenmarkian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-TJHXR5R');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isGermany()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NSPZVGJ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isBelgian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-W28LQH9');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isGreecian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5F78CPS');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isJar()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NB6GBBR');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isFrancian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-MN3J8GX');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isFinlandian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-N46B9J5');</script>
		<!-- End Google Tag Manager -->

	{/if}

	{if $localization->isSwedian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WVTSWR7');</script>
		<!-- End Google Tag Manager -->
	{/if}
	
	{if $localization->isNorwaian()	}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WX79TQ2');</script>
		<!-- End Google Tag Manager -->

	{/if}

	{if $localization->isAustrian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-MC85Q98');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isSwitzerlandian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-P8HLMSR');</script>
		<!-- End Google Tag Manager -->
	{/if}
	
	{if $localization->isPortuguesian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-T6P59VF');</script>
		<!-- End Google Tag Manager -->
	{/if}
	
	{if $localization->isSpaian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KK47MKV');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isGreatbritian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WG6SPPQ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isSlovenian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WW5CSBX');</script>
		<!-- End Google Tag Manager -->

	{/if}	

	{if $localization->isLatvian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-M5KPSRQ');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isSerbian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NNXHJJJ');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isBulgarian()}
		<!-- Google Tag Manager -->
		<script  n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NTTLTCL');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isLithuanian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5KJMDBT');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isEstonian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5HT2ZGD');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isMoldavian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5H9LNN3S');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isTurkian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-W2Z54G36');</script>
		<!-- End Google Tag Manager -->

	{/if}
	
	{if $localization->isAustralian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-52GM7JFW');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isBrazilian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-T8TZZTVV');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isUnitedStatesAmerican()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5SZRBDXJ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if !$isTrafficPaid}
		{*<script type="text/javascript" charset="UTF-8" src="//cdn.cookie-script.com/s/e3a52935254eec71b8a7cc9aba4a3426.js"></script>*}
	{/if}

	{if $channel == 'c1' && $localization->isCzech()}
		<!--Google GPT/ADM code -->
		<script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
		<script type="text/javascript">
			window.googletag = window.googletag || { cmd: [] };
			window.googletag.cmd.push(function () {
				window.googletag.pubads().enableSingleRequest();
			window.googletag.pubads().disableInitialLoad();
			});
		</script>

		<!--Site config -->
		<script type="text/javascript" async="async" src="https://protagcdn.com/s/mroferto.cz/site.js"></script>
		<script type="text/javascript">
			window.protag = window.protag || { cmd: [] };
			window.protag.config = { s:'mroferto.cz', childADM: '***********', l: 'FbM3ys2m' };
			window.protag.cmd.push(function () {
				window.protag.pageInit();
			});
		</script>
	{elseif $channel == 'c1' && $localization->isPolish()}		
		<script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
		<script type="text/javascript">
			window.googletag = window.googletag || { cmd: [] };
			window.googletag.cmd.push(function () {
				window.googletag.pubads().enableSingleRequest();
			});
		</script>

		<!--Site config -->
		<script type="text/javascript" async="async" src="https://protagcdn.com/s/mroferto.pl/site.js"></script>
		<script type="text/javascript">
			window.protag = window.protag || { cmd: [] };
			window.protag.config = { s:'mroferto.pl', childADM: '***********', l: 'FbM3ys2m' };
			window.protag.cmd.push(function () {
				window.protag.pageInit();
			});
		</script>
	{/if}
	
	{if $localization->isLatvian() || $localization->isSerbian() || $localization->isBulgarian() || $localization->isFrancian() || $localization->isSpaian() || $localization->isBrazilian() || $localization->isUnitedStatesAmerican()}
		{* Kaufino adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
	{elseif $localization->isLithuanian() || $localization->isEstonian() || $localization->isCyprian()}
		{* AdSalva adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6277661924921144" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
	{elseif $localization->isMoldavian() || $localization->isTurkian()}
		{* Stario adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4363508753950122" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
	{else}	
		{* Oferto adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3454721603118795" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>															
	{/if}	

</head>

<body>	
	{if $localization->isCzech()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WC5TC26"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->	
	{/if}

	{if $localization->isSlovak()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KZH5WLL"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isPolish()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WGK8HS5"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isRomanian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KH9SVTS"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isHungarian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TMWCLVZ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isCroatian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M62XL7C"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isItaly()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P34SLSN"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isCanadian()}	
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T45TR7K"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->	
	{/if}

	{if $localization->isNetherlandian()}	
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5D7XSD4"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isDenmarkian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TJHXR5R"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isGermany()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NSPZVGJ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isBelgian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W28LQH9"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isGreecian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5F78CPS"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isJar()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NB6GBBR"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isFrancian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MN3J8GX"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isFinlandian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N46B9J5"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isSwedian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WVTSWR7"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}
	
	{if $localization->isNorwaian()	}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WX79TQ2"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isAustrian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MC85Q98"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isSwitzerlandian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P8HLMSR"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}
	
	{if $localization->isPortuguesian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T6P59VF"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}
	
	{if $localization->isSpaian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KK47MKV"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isGreatbritian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WG6SPPQ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isSlovenian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WW5CSBX"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) --> 
	{/if}	

	{if $localization->isLatvian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M5KPSRQ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isSerbian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNXHJJJ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isBulgarian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NTTLTCL"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isLithuanian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5KJMDBT"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isEstonian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5HT2ZGD"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isMoldavian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5H9LNN3S"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isTurkian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W2Z54G36"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isAustralian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-52GM7JFW"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isBrazilian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T8TZZTVV"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isUnitedStatesAmerican()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5SZRBDXJ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	<div class="k-header k-header--city-picker">
		<div class="container container--flex">
			<a n:href="Homepage:default" class="k-header__logo-link">
				{if $websiteType === 'oferto'}
					{if $localization->isItaly()}
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1603.8133 345.6336" n:syntax="off"><defs><style>.g{fill:url(#e);}.h{fill:#2bb673;}.i{opacity:.53;}</style><linearGradient id="e" x1="0" y1="172.8168" x2="298.1663" y2="172.8168" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><g id="b"><g><path d="M370.9398,286.2329h-19.1896V78.4332c0-10.4255,8.4516-18.8771,18.8771-18.8771h12.0123l79.8758,169.7342,79.8743-169.7342h12.0123c10.4255,0,18.8771,8.4516,18.8771,18.8771v207.7997h-19.1881c-10.4255,0-18.8771-8.4516-18.8771-18.8771v-111.6996l-60.8417,130.5767h-23.7135l-60.8417-130.5767v111.6996c0,10.4255-8.4516,18.8771-18.8771,18.8771Z"/><path d="M721.5371,256.4358c-21.7389-21.7359-32.6054-49.6101-32.6054-83.6196,0-34.1115,10.8665-61.9857,32.6054-83.6196,21.7359-21.7359,49.6087-32.6039,83.6182-32.6039s61.8822,10.868,83.6211,32.6039c21.7359,21.6339,32.6054,49.5081,32.6054,83.6196,0,34.0095-10.8694,61.8837-32.6054,83.6196-21.7389,21.7389-49.6116,32.6054-83.6211,32.6054s-61.8822-10.8665-83.6182-32.6054Zm137.2855-144.618c-14.5616-13.5181-32.4487-20.2801-53.6673-20.2801s-39.1058,6.762-53.6644,20.2801c-16.3293,15.0818-24.4939,35.4136-24.4939,60.9984s8.1646,45.8663,24.4939,60.8432c14.5586,13.626,32.4487,20.4367,53.6644,20.4367s39.1058-6.8108,53.6673-20.4367c16.3263-14.9769,24.4939-35.2584,24.4939-60.8432s-8.1676-45.9165-24.4939-60.9984Z"/><path d="M937.9559,154.564v-44.3055c0-9.7757,1.8209-18.4606,5.4628-26.0533,4.6794-9.5673,11.8006-16.6929,21.3723-21.3723,7.8011-3.9523,16.6929-5.9284,26.6755-5.9284,13.3112,0,26.6785,3.2251,40.0931,9.6722l-16.5362,34.1647c-8.008-3.8458-15.8593-5.7717-23.5569-5.7717-4.1592,0-7.4877,.6237-9.9826,1.8712-1.7707,.8336-3.0713,2.1343-3.902,3.9005-1.0405,2.2895-1.5578,5.4598-1.5578,9.517v10.9197h27.1429c10.4255,0,18.8771,8.4516,18.8771,18.8771v14.5087h-46.02v112.7919c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1911V154.564Z"/><path d="M1072.1676,213.5345c1.2475,10.6078,5.1997,19.6563,11.8568,27.1441,6.1338,6.9704,13.7782,11.7548,22.9302,14.3532,9.152,2.6013,18.2005,2.5496,27.1455-.1567,9.5688-2.8068,17.6802-8.1632,24.3373-16.0677h44.9292c-11.44,19.2425-24.9078,32.7103-40.4035,40.4049-12.8973,6.5521-27.1455,9.8289-42.7476,9.8289-26.105,0-47.5305-8.4248-64.2737-25.2729-16.8496-16.7446-25.2714-38.2219-25.2714-64.4304,0-26.105,8.4218-47.5291,25.2714-64.2752,16.7431-16.8481,38.1687-25.2729,64.2737-25.2729,26.2114,0,47.6843,8.4248,64.4304,25.2729,16.8496,16.7461,25.2743,38.1702,25.2743,64.2752v14.1965h-137.7525Zm95.4749-29.3301c-2.1845-13.3112-8.2179-23.6589-18.097-31.0446-9.0485-6.6541-19.5011-9.8792-31.3579-9.6722-11.8568,.3119-22.0493,3.9005-30.5775,10.7645-9.2554,7.4877-14.4049,17.4718-15.4425,29.9523h95.4749Z"/><path d="M1326.0257,146.7629v-14.352c0-10.4255,8.4516-18.8771,18.8771-18.8771h9.3592v-32.4487l19.0883-.1009c10.4644-.0553,18.9769,8.4123,18.9769,18.8768v13.6728h26.1859v14.352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-7.3088v120.5929c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1881V146.7629h-28.2363Z"/><path d="M1226.7383,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1877c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0003,7.6162-19.7256,14.275-34.1453,43.635-34.304v27.1254c-.0001,9.4839-6.8211,17.8192-16.2135,19.1335-21.3797,2.9917-27.4216,16.2288-27.4216,55.0946v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M589.2932,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1876c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0004,7.6162-19.7256,14.275-34.1453,43.635-34.304l-.0002,27.1255c0,9.4839-6.821,17.8192-16.2134,19.1335-21.3796,2.9918-27.4215,16.2288-27.4215,55.0945v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M1566.8913,262.7671c-2.6614,3.3276-6.3205,6.3205-13.3072,11.3103-14.9677,10.311-28.9377,14.9677-44.903,14.9677-46.9015,0-81.4935-38.2511-81.4935-90.1424,0-50.2258,36.2558-88.4786,83.4888-88.4786,21.2881,0,43.9054,10.9772,56.2149,26.9425v-2.4111c0-10.4255,8.4516-18.8771,18.8771-18.8771h18.0449v148.4352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-18.0449v-20.6236Zm-49.8944-10.311c27.9401,0,48.8968-22.6189,48.8968-52.5542,0-30.6032-20.6253-52.5558-48.8968-52.5558-27.9401,0-49.563,23.2834-49.563,53.2204,0,29.2708,21.9543,51.8897,49.563,51.8897Z"/></g></g><g id="d"><path class="g" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g><g id="f" class="i"><path class="h" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g></svg>
					{else}
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 530.3963 114.1832" n:syntax="off"><defs><style>.a{fill:url(#a);}.b{opacity:0.53;}.c{fill:#2bb673;}</style><linearGradient id="a" x1="13.0185" y1="67.1923" x2="111.5204" y2="67.1923" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><path d="M135.5618,104.66h-6.3394V36.0118a6.2362,6.2362,0,0,1,6.2362-6.2362h3.9683l26.3877,56.0733,26.3872-56.0733H196.17a6.2362,6.2362,0,0,1,6.2362,6.2362V104.66h-6.3389a6.2363,6.2363,0,0,1-6.2363-6.2362V61.5232l-20.1,43.1372h-7.834l-20.1-43.1372v36.901A6.2362,6.2362,0,0,1,135.5618,104.66Z" transform="translate(-13.0185 -10.1007)"/><path d="M251.3848,94.8166q-10.7724-10.7709-10.7714-27.6245,0-16.9035,10.7714-27.6245,10.771-10.7709,27.6241-10.771t27.625,10.771q10.7709,10.7205,10.7714,27.6245,0,16.8531-10.7714,27.6245t-27.625,10.7715Q262.1558,105.5881,251.3848,94.8166Zm45.3536-47.7758a26.8076,26.8076,0,0,0-35.4581,0q-8.0918,7.4736-8.0918,20.1513t8.0918,20.1a26.6535,26.6535,0,0,0,35.4581,0q8.09-7.4215,8.0918-20.1Q304.83,54.514,296.7384,47.0408Z" transform="translate(-13.0185 -10.1007)"/><path d="M322.8808,61.1623V46.5256a19.6663,19.6663,0,0,1,1.8047-8.6069,14.9751,14.9751,0,0,1,7.06-7.0606A19.2313,19.2313,0,0,1,340.5585,28.9a30.3644,30.3644,0,0,1,13.2451,3.1953l-5.4629,11.2866a17.8937,17.8937,0,0,0-7.7822-1.9068,7.4282,7.4282,0,0,0-3.2978.6182,2.6134,2.6134,0,0,0-1.2891,1.2886,7.7685,7.7685,0,0,0-.5147,3.144V50.133h8.9669a6.2363,6.2363,0,0,1,6.2363,6.2363v4.793H335.4569V98.4242a6.2362,6.2362,0,0,1-6.2362,6.2362h-6.34Z" transform="translate(-13.0185 -10.1007)"/><path d="M367.2188,80.6438a15.9479,15.9479,0,0,0,3.917,8.9673,16.0561,16.0561,0,0,0,16.543,4.69,16.3474,16.3474,0,0,0,8.04-5.3081h14.8428q-5.6689,9.5353-13.3477,13.3482a30.7246,30.7246,0,0,1-14.122,3.247q-12.936,0-21.2334-8.3491-8.35-8.2976-8.3487-21.2851,0-12.9361,8.3487-21.2339,8.2968-8.349,21.2334-8.3492,12.9888,0,21.2851,8.3492,8.35,8.2983,8.35,21.2339v4.69Zm31.541-9.6895a15.35,15.35,0,0,0-5.9785-10.2558,16.513,16.513,0,0,0-10.3593-3.1953A16.327,16.327,0,0,0,372.32,61.0593a13.6289,13.6289,0,0,0-5.1016,9.895Z" transform="translate(-13.0185 -10.1007)"/><path d="M451.0832,58.5852V53.8439a6.2361,6.2361,0,0,1,6.2362-6.2362h3.0919v-10.72l6.306-.0333a6.2362,6.2362,0,0,1,6.2692,6.2361v4.517h8.6507V52.349a6.2362,6.2362,0,0,1-6.2362,6.2362h-2.4145v39.839A6.2363,6.2363,0,0,1,466.75,104.66h-6.339V58.5852Z" transform="translate(-13.0185 -10.1007)"/><path d="M513.987,46.68q12.8832,0,21.1816,8.2979,8.2457,8.2462,8.2461,21.13,0,12.9369-8.2461,21.1822-8.2983,8.2983-21.1816,8.2978-12.8848,0-21.1826-8.2978-8.2456-8.2449-8.2461-21.1822,0-12.8832,8.2461-21.13Q501.1027,46.68,513.987,46.68ZM502.3913,89.56a16.3447,16.3447,0,0,0,11.5957,4.4321A16.0587,16.0587,0,0,0,525.5827,89.56q5.2573-4.9988,5.2568-13.4517,0-8.3994-5.2568-13.4a16.353,16.353,0,0,0-11.5957-4.4321,16.06,16.06,0,0,0-11.5957,4.4321q-5.2574,5-5.2578,13.4Q497.1335,84.5618,502.3913,89.56Z" transform="translate(-13.0185 -10.1007)"/><path d="M418.2827,63.0977c0-5.6656-.0128-11.69-.11-16.4588h6.6692a6.2353,6.2353,0,0,1,6.2337,6.0469c.055,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4153-11.3326l0,8.9611a6.3352,6.3352,0,0,1-5.3562,6.3209c-7.063.9884-9.059,5.3614-9.059,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6465Z" transform="translate(-13.0185 -10.1007)"/><path d="M207.6968,63.0977c0-5.6656-.0127-11.69-.11-16.4588h6.6692a6.2351,6.2351,0,0,1,6.2336,6.0469c.0551,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4152-11.3326V55.27a6.3353,6.3353,0,0,1-5.3563,6.3209c-7.0629.9884-9.0589,5.3614-9.0589,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6466Z" transform="translate(-13.0185 -10.1007)"/><path class="a" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/><g class="b"><path class="c" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/></g></svg>
					{/if}
				{else}
					{$website->getName()}
				{/if}
			</a>

			<div class="k-header__search k-header__search--city-picker">
				<form class="">
					<input type="text" class="k-header__search-input js-search-input" data-search-url="{link Ajax:search}" placeholder="{_"$websiteType.navbar.search.placeholder"}">
					<input type="submit" class="k-header__search-submit js-search-submit" data-search-url="{link Search:search, q => 'q'}" value="{_"$websiteType.navbar.search.submit"}">

					<div class="k-header__search-wrapper">					
					</div>
				</form>

				<form n:name="cityPickerControl-form" n:if="$localization->hasGeolocation()" class="order-first order-sm-1">
					<select n:name="cityId" style="display: none"></select>

					{* <input n:name="submit" value="{_kaufino.cityPicker.submit}" class="k-header__search-submit"> *}
				</form>
			</div>

			<button class="k-header__menu-icon">
				<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" class="svg-inline--fa fa-bars fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg>
			</button>			

			<div class="k-header__nav">				
				<a href="{link Shops:shops}" class="k-header__nav-item">{_"$websiteType.navbar.shops"}</a>
				<a href="{link Leaflets:leaflets}" class="k-header__nav-item">{_"$websiteType.navbar.leaflets"}</a>

				{if ($localization->isCzech() || $localization->isHungarian()) && $websiteType === 'oferto'}
					<a href="{link Offers:offers}" class="k-header__nav-item">{_"$websiteType.navbar.offers"}</a>
				{/if}

				<span class="k-header__nav-separator">|</span>
				
				{foreach $headerShops() as $headerShop}
					<a n:if="$iterator->counter < 3" n:href="Shop:shop $headerShop" class="k-header__nav-item color-grey">{$headerShop->getName()}</a>

					{if $iterator->counter == 2}
					<div class="k-header__nav-dropdown-wrapper">
						<a href="{link Shops:shops}" class="k-header__nav-item k-header__nav-item--more">{_"$websiteType.navbar.moreShops"} »</a>

						<div class="k-header__nav-dropdown">
					{/if}
						<a n:if="$iterator->counter > 3" n:href="Shop:shop $headerShop" class="k-header__nav-dropdown-item">{$headerShop->getName()}</a>
					{if $iterator->last}
						</div>
					</div>
					{/if}
				{/foreach}
			</div>			
		</div>
		
	</div>
	<div n:foreach="$flashes as $flash" n:class="alert, 'alert-' . $flash->type">{$flash->message}</div>				

	{block breadcrumb}{/block}

	{include content}

	{if in_array($presenterName, ['Oferto:Shop', 'Oferto:Tag', 'Oferto:Article']) && $user->isLoggedIn()}
		<div class="k-page-extension">			
			<span n:class="$pageExtension && $pageExtension->getTitle() ? k-page-extension__tag--green ,k-page-extension__tag">MT</span>
			<span n:class="$pageExtension && $pageExtension->getDescription() ? k-page-extension__tag--green ,k-page-extension__tag">MD</span>			
			<span n:class="$pageExtension && $pageExtension->getHeading() ? k-page-extension__tag--green ,k-page-extension__tag">H1</span>			
			<span n:class="$pageExtension && $pageExtension->getKeywords() ? k-page-extension__tag--green ,k-page-extension__tag">KW</span>			
			<span n:class="$pageExtension && $pageExtension->getShortDescription() ? k-page-extension__tag--green ,k-page-extension__tag">SD</span>
			<span n:class="$pageExtension && $pageExtension->getLongDescription() ? k-page-extension__tag--green ,k-page-extension__tag">LD</span>

			{if $presenterName == 'Oferto:Shop'}
				<span n:class="$contentBlocksAllowed ? k-page-extension__tag--green ,k-page-extension__tag">CB</span>
				<a n:href=":Admin:Shop:shop $shop->getId()" class="k-page-extension__btn" target="_blank">Edit shop</a>
			{/if}

			{if $presenterName == 'Oferto:Tag'}
				<a n:href=":Admin:Tag:tag $tag->getId()" class="k-page-extension__btn" target="_blank">Edit tag</a>
			{/if}

			{if $presenterName == 'Oferto:Article'}
				<a n:href=":Admin:Article:article $article->getId()" class="k-page-extension__btn" target="_blank">Edit article</a>
			{/if}

			{if $pageExtension}
				<a n:href=":Admin:Seo:pageExtension $pageExtension->getId()" class="k-page-extension__btn" target="_blank">Edit page extension</a>	

				{var $shopKeywords = $pageExtension->getKeywords()}							
				<span class="k-alternative-name js-alternative-name" data-alternative-name="{$shopKeywords}"></span>				
			{else}
				<a n:href=":Admin:Seo:pageExtension id => null, websiteId => $website->getId(), slug => $pageExtensionSlug" class="k-page-extension__btn" target="_blank">Edit page extension</a>
			{/if}

			<a n:href=":Admin:Translations:Dictionary:dictionary dictionary => 'oferto', localizationId => $localization->getId()" class="k-page-extension__btn" target="_blank">Translations</a>
		</div>
	{/if}		

	<footer class="k-footer mt-5">		
		<div class="container">
			<div class="k-footer__wrapper">
				<div class="k-footer__column k-footer__column--first">
					<a n:href="Homepage:default" class="d-block mb-4">
						{if $localization->isItaly()}
							<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1603.8133 345.6336" n:syntax="off"><defs><style>.g{fill:url(#e);}.h{fill:#2bb673;}.i{opacity:.53;}</style><linearGradient id="e" x1="0" y1="172.8168" x2="298.1663" y2="172.8168" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><g id="b"><g><path d="M370.9398,286.2329h-19.1896V78.4332c0-10.4255,8.4516-18.8771,18.8771-18.8771h12.0123l79.8758,169.7342,79.8743-169.7342h12.0123c10.4255,0,18.8771,8.4516,18.8771,18.8771v207.7997h-19.1881c-10.4255,0-18.8771-8.4516-18.8771-18.8771v-111.6996l-60.8417,130.5767h-23.7135l-60.8417-130.5767v111.6996c0,10.4255-8.4516,18.8771-18.8771,18.8771Z"/><path d="M721.5371,256.4358c-21.7389-21.7359-32.6054-49.6101-32.6054-83.6196,0-34.1115,10.8665-61.9857,32.6054-83.6196,21.7359-21.7359,49.6087-32.6039,83.6182-32.6039s61.8822,10.868,83.6211,32.6039c21.7359,21.6339,32.6054,49.5081,32.6054,83.6196,0,34.0095-10.8694,61.8837-32.6054,83.6196-21.7389,21.7389-49.6116,32.6054-83.6211,32.6054s-61.8822-10.8665-83.6182-32.6054Zm137.2855-144.618c-14.5616-13.5181-32.4487-20.2801-53.6673-20.2801s-39.1058,6.762-53.6644,20.2801c-16.3293,15.0818-24.4939,35.4136-24.4939,60.9984s8.1646,45.8663,24.4939,60.8432c14.5586,13.626,32.4487,20.4367,53.6644,20.4367s39.1058-6.8108,53.6673-20.4367c16.3263-14.9769,24.4939-35.2584,24.4939-60.8432s-8.1676-45.9165-24.4939-60.9984Z"/><path d="M937.9559,154.564v-44.3055c0-9.7757,1.8209-18.4606,5.4628-26.0533,4.6794-9.5673,11.8006-16.6929,21.3723-21.3723,7.8011-3.9523,16.6929-5.9284,26.6755-5.9284,13.3112,0,26.6785,3.2251,40.0931,9.6722l-16.5362,34.1647c-8.008-3.8458-15.8593-5.7717-23.5569-5.7717-4.1592,0-7.4877,.6237-9.9826,1.8712-1.7707,.8336-3.0713,2.1343-3.902,3.9005-1.0405,2.2895-1.5578,5.4598-1.5578,9.517v10.9197h27.1429c10.4255,0,18.8771,8.4516,18.8771,18.8771v14.5087h-46.02v112.7919c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1911V154.564Z"/><path d="M1072.1676,213.5345c1.2475,10.6078,5.1997,19.6563,11.8568,27.1441,6.1338,6.9704,13.7782,11.7548,22.9302,14.3532,9.152,2.6013,18.2005,2.5496,27.1455-.1567,9.5688-2.8068,17.6802-8.1632,24.3373-16.0677h44.9292c-11.44,19.2425-24.9078,32.7103-40.4035,40.4049-12.8973,6.5521-27.1455,9.8289-42.7476,9.8289-26.105,0-47.5305-8.4248-64.2737-25.2729-16.8496-16.7446-25.2714-38.2219-25.2714-64.4304,0-26.105,8.4218-47.5291,25.2714-64.2752,16.7431-16.8481,38.1687-25.2729,64.2737-25.2729,26.2114,0,47.6843,8.4248,64.4304,25.2729,16.8496,16.7461,25.2743,38.1702,25.2743,64.2752v14.1965h-137.7525Zm95.4749-29.3301c-2.1845-13.3112-8.2179-23.6589-18.097-31.0446-9.0485-6.6541-19.5011-9.8792-31.3579-9.6722-11.8568,.3119-22.0493,3.9005-30.5775,10.7645-9.2554,7.4877-14.4049,17.4718-15.4425,29.9523h95.4749Z"/><path d="M1326.0257,146.7629v-14.352c0-10.4255,8.4516-18.8771,18.8771-18.8771h9.3592v-32.4487l19.0883-.1009c10.4644-.0553,18.9769,8.4123,18.9769,18.8768v13.6728h26.1859v14.352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-7.3088v120.5929c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1881V146.7629h-28.2363Z"/><path d="M1226.7383,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1877c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0003,7.6162-19.7256,14.275-34.1453,43.635-34.304v27.1254c-.0001,9.4839-6.8211,17.8192-16.2135,19.1335-21.3797,2.9917-27.4216,16.2288-27.4216,55.0946v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M589.2932,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1876c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0004,7.6162-19.7256,14.275-34.1453,43.635-34.304l-.0002,27.1255c0,9.4839-6.821,17.8192-16.2134,19.1335-21.3796,2.9918-27.4215,16.2288-27.4215,55.0945v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M1566.8913,262.7671c-2.6614,3.3276-6.3205,6.3205-13.3072,11.3103-14.9677,10.311-28.9377,14.9677-44.903,14.9677-46.9015,0-81.4935-38.2511-81.4935-90.1424,0-50.2258,36.2558-88.4786,83.4888-88.4786,21.2881,0,43.9054,10.9772,56.2149,26.9425v-2.4111c0-10.4255,8.4516-18.8771,18.8771-18.8771h18.0449v148.4352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-18.0449v-20.6236Zm-49.8944-10.311c27.9401,0,48.8968-22.6189,48.8968-52.5542,0-30.6032-20.6253-52.5558-48.8968-52.5558-27.9401,0-49.563,23.2834-49.563,53.2204,0,29.2708,21.9543,51.8897,49.563,51.8897Z"/></g></g><g id="d"><path class="g" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g><g id="f" class="i"><path class="h" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g></svg>
						{else}
							<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 530.3963 114.1832" n:syntax="off"><defs><style>.a{fill:url(#a);}.b{opacity:0.53;}.c{fill:#2bb673;}</style><linearGradient id="a" x1="13.0185" y1="67.1923" x2="111.5204" y2="67.1923" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><path d="M135.5618,104.66h-6.3394V36.0118a6.2362,6.2362,0,0,1,6.2362-6.2362h3.9683l26.3877,56.0733,26.3872-56.0733H196.17a6.2362,6.2362,0,0,1,6.2362,6.2362V104.66h-6.3389a6.2363,6.2363,0,0,1-6.2363-6.2362V61.5232l-20.1,43.1372h-7.834l-20.1-43.1372v36.901A6.2362,6.2362,0,0,1,135.5618,104.66Z" transform="translate(-13.0185 -10.1007)"/><path d="M251.3848,94.8166q-10.7724-10.7709-10.7714-27.6245,0-16.9035,10.7714-27.6245,10.771-10.7709,27.6241-10.771t27.625,10.771q10.7709,10.7205,10.7714,27.6245,0,16.8531-10.7714,27.6245t-27.625,10.7715Q262.1558,105.5881,251.3848,94.8166Zm45.3536-47.7758a26.8076,26.8076,0,0,0-35.4581,0q-8.0918,7.4736-8.0918,20.1513t8.0918,20.1a26.6535,26.6535,0,0,0,35.4581,0q8.09-7.4215,8.0918-20.1Q304.83,54.514,296.7384,47.0408Z" transform="translate(-13.0185 -10.1007)"/><path d="M322.8808,61.1623V46.5256a19.6663,19.6663,0,0,1,1.8047-8.6069,14.9751,14.9751,0,0,1,7.06-7.0606A19.2313,19.2313,0,0,1,340.5585,28.9a30.3644,30.3644,0,0,1,13.2451,3.1953l-5.4629,11.2866a17.8937,17.8937,0,0,0-7.7822-1.9068,7.4282,7.4282,0,0,0-3.2978.6182,2.6134,2.6134,0,0,0-1.2891,1.2886,7.7685,7.7685,0,0,0-.5147,3.144V50.133h8.9669a6.2363,6.2363,0,0,1,6.2363,6.2363v4.793H335.4569V98.4242a6.2362,6.2362,0,0,1-6.2362,6.2362h-6.34Z" transform="translate(-13.0185 -10.1007)"/><path d="M367.2188,80.6438a15.9479,15.9479,0,0,0,3.917,8.9673,16.0561,16.0561,0,0,0,16.543,4.69,16.3474,16.3474,0,0,0,8.04-5.3081h14.8428q-5.6689,9.5353-13.3477,13.3482a30.7246,30.7246,0,0,1-14.122,3.247q-12.936,0-21.2334-8.3491-8.35-8.2976-8.3487-21.2851,0-12.9361,8.3487-21.2339,8.2968-8.349,21.2334-8.3492,12.9888,0,21.2851,8.3492,8.35,8.2983,8.35,21.2339v4.69Zm31.541-9.6895a15.35,15.35,0,0,0-5.9785-10.2558,16.513,16.513,0,0,0-10.3593-3.1953A16.327,16.327,0,0,0,372.32,61.0593a13.6289,13.6289,0,0,0-5.1016,9.895Z" transform="translate(-13.0185 -10.1007)"/><path d="M451.0832,58.5852V53.8439a6.2361,6.2361,0,0,1,6.2362-6.2362h3.0919v-10.72l6.306-.0333a6.2362,6.2362,0,0,1,6.2692,6.2361v4.517h8.6507V52.349a6.2362,6.2362,0,0,1-6.2362,6.2362h-2.4145v39.839A6.2363,6.2363,0,0,1,466.75,104.66h-6.339V58.5852Z" transform="translate(-13.0185 -10.1007)"/><path d="M513.987,46.68q12.8832,0,21.1816,8.2979,8.2457,8.2462,8.2461,21.13,0,12.9369-8.2461,21.1822-8.2983,8.2983-21.1816,8.2978-12.8848,0-21.1826-8.2978-8.2456-8.2449-8.2461-21.1822,0-12.8832,8.2461-21.13Q501.1027,46.68,513.987,46.68ZM502.3913,89.56a16.3447,16.3447,0,0,0,11.5957,4.4321A16.0587,16.0587,0,0,0,525.5827,89.56q5.2573-4.9988,5.2568-13.4517,0-8.3994-5.2568-13.4a16.353,16.353,0,0,0-11.5957-4.4321,16.06,16.06,0,0,0-11.5957,4.4321q-5.2574,5-5.2578,13.4Q497.1335,84.5618,502.3913,89.56Z" transform="translate(-13.0185 -10.1007)"/><path d="M418.2827,63.0977c0-5.6656-.0128-11.69-.11-16.4588h6.6692a6.2353,6.2353,0,0,1,6.2337,6.0469c.055,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4153-11.3326l0,8.9611a6.3352,6.3352,0,0,1-5.3562,6.3209c-7.063.9884-9.059,5.3614-9.059,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6465Z" transform="translate(-13.0185 -10.1007)"/><path d="M207.6968,63.0977c0-5.6656-.0127-11.69-.11-16.4588h6.6692a6.2351,6.2351,0,0,1,6.2336,6.0469c.0551,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4152-11.3326V55.27a6.3353,6.3353,0,0,1-5.3563,6.3209c-7.0629.9884-9.0589,5.3614-9.0589,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6466Z" transform="translate(-13.0185 -10.1007)"/><path class="a" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/><g class="b"><path class="c" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/></g></svg>
						{/if}
					</a>
					<p class="fz-s color-grey mb-3">{_"$websiteType.footer.text"}</p>
					<p class="fz-s color-grey mb-3">Copyright © {date('Y')} {_"$websiteType.footer.copyright"}</p>
				</div>
				<div class="k-footer__column">
					<div class="k-footer__wrapper">
						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.shops"}:</strong>
								<a n:foreach="$footerShops() as $footerShop" n:href="Shop:shop $footerShop" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerShop->getName()}</a>
							</p>
						</div>		

						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.article"}:</strong>
								<a n:foreach="$footerArticles() as $footerArticle" n:href="Article:article $footerArticle" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerArticle->getName()}</a>
							</p>
						</div>						

						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.aboutOferto"}:</strong>
								<a n:href="Leaflets:leaflets" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.leaflets"}</a>
								<a n:href="Shops:shops" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.shops"}</a>
								<a n:href="Articles:articles" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.articles"}</a>

								<a n:href="Static:aboutUs" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.aboutUs"}</a>
								{*<a n:href="Static:cookies" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_oferto.footer.cookies}</a>*}
								{if $conditions}
									<a n:href="Conditions:default, $conditions" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$conditions->getName()}</a>
								{/if}
							</p>
						</div>													
						
					</div>	
				</div>
			</div>

			<div class="">
				<p>
					<strong class="d-block fz-m color-grey tt-uppercase mb-3">{_"$websiteType.footer.nextCountries"}:</strong>

					{foreach $footerWebsites() as $footerWebsite}
                        {continueIf $footerWebsite === $website}

						<a href="{$footerWebsite->getDomain()}" class="fz-m color-black td-none td-hover-underline mb-4">{$footerWebsite->getModule() === 'oferto_com' ? 'MrOferto.com' : $footerWebsite->getLocalization()->getOriginalName()}{sep}, {/sep}</a>
					{/foreach}
				</p>
			</div>	
		</div>
	</footer>

	{block scripts}
		<script src="{$basePath}/js/lazysizes/lazysizes.min.js" async></script>		
		<script src="{$basePath}/js/main.js?v={$version}" async></script>

		{if isset($userLoggedIn) && $pageExtension && $pageExtension->getKeywords()}
			<script src="{$basePath}/js/page-extension.js?v={$version}" async></script>
		{/if}

		<script src="{$basePath}/js/main.oferto.js?v={$version}" async></script>

		{if $localization->hasGeolocation()}
		<script src="/js/nice-select2.js"></script>
		<script>
			const cityPicker = document.getElementById('frm-cityPickerControl-form-cityId');
			const form = document.getElementById('frm-cityPickerControl-form');
			const niceSelect = NiceSelect.bind(cityPicker, { searchable: true });

			cityPicker.addEventListener('change', function(event) {
				const selectedCityId = event.target.value;

				if (selectedCityId === 'currentLocation') {
					getLocationFromBrowser();
				} else {
					form.submit();
				}
			});

			function checkCookie(name) {
				var cookies = document.cookie.split(';');
				for (var i = 0; i < cookies.length; i++) {
					var cookie = cookies[i].trim();
					if (cookie.indexOf(name + '=') === 0) {
						return true;
					}
				}
				return false;
			}

			function getCookie(name) {
				let cookieArr = document.cookie.split(";");

				for (let i = 0; i < cookieArr.length; i++) {
					let cookie = cookieArr[i].trim();

					if (cookie.indexOf(name + "=") === 0) {
						return cookie.substring(name.length + 1);
					}
				}

				return null;
			}

			function resolveCityIdFromUserIp() {
				const xhr = new XMLHttpRequest();
				xhr.open('POST', {link :Api:Offerista:resolveUserLocationFromIp, websiteId: $presenter->website->getId()}, true);
				xhr.setRequestHeader('Content-Type', 'application/json');

				xhr.onload = function() {
					if (xhr.status === 200) {
						const response = JSON.parse(xhr.responseText);
						setUserCityId(response.cityId);

						return response;
					} else {
						console.error('Error:', xhr.status, xhr.statusText);
					}
				};

				xhr.onerror = function() {
					console.error('Request failed.');
				};

				xhr.send();
			}

			function getLocationFromBrowser()
			{
				navigator.geolocation.getCurrentPosition(function(position) {
					let latitude = position.coords.latitude;
					let longitude = position.coords.longitude;

					const xhr = new XMLHttpRequest();
					xhr.open('POST', {link :Api:Offerista:resolveUserLocationFromPosition, websiteId: $presenter->website->getId()}, true);
					xhr.setRequestHeader('Content-Type', 'application/json');

					xhr.onload = function() {
						if (xhr.status === 200) {
							const response = JSON.parse(xhr.responseText);
							setUserCityId(response.cityId);

							return response;
						} else {
							console.error('Error:', xhr.status, xhr.statusText);
						}
					};

					xhr.onerror = function() {
						console.error('Request failed.');
					};

					xhr.send(JSON.stringify({ latitude: latitude, longitude: longitude }));
				}, function(error) {
					alert({_kaufino.cityPicker.error});

					userLocation = JSON.parse(decodeURIComponent(getCookie('userLocation')));
					setUserCityId(userLocation.cityId);
				});
			}

			function setUserCityId(cityId)
			{
				cityPicker.value = cityId
				niceSelect.update();
			}

			let userLocation;
			if (checkCookie('userLocation') === false) {
				userLocation = resolveCityIdFromUserIp();
			} else {
				userLocation = JSON.parse(decodeURIComponent(getCookie('userLocation')));
				setUserCityId(userLocation.cityId);
			}
		</script>
	{/if}
	{/block}
</body>
</html>