{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}
    {else}
        {$metaTitle}
    {/if}
{/block}

{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block head}
    {include parent}          
    
    <!-- oferto.com / sticky -->
    {include "../components/sticky.latte"}                                     
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_"$websiteType.navbar.leaflets"}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag() !== null">{$shop->getTag()->getName()}</a> |
            <span class="color-grey">{$shop->getName()}</span>
        </p>
    </div>
{/block}

{block scripts}
    {include parent}

<script type="application/ld+json" n:if="count($leaflets)">
    {
        "@context": "https://schema.org",
        "@type": "OfferCatalog",
        "name": {_"$websiteType.homepage.h1"},
        "url": {$presenter->link('//this')},
        "itemListElement": [
            {foreach $leaflets as $key => $leaflet}
                {
                    "@type": "SaleEvent",
                    "name": {$leaflet->getName()},
                    "url": {$presenter->link('Leaflet:leaflet', $leaflet->getShop(), $leaflet)},
                    "startDate": {$leaflet->getValidSince()|date:'Y-m-d'},
                    "endDate": {$leaflet->getValidTill()|date:'Y-m-d'},
                    "location": {
                        "@type": "Place",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {$presenter->link('Shop:shop', $leaflet->getShop())}
                    }
                }{sep},{/sep}
    {/foreach}
    ]
}
</script>
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": {_oferto.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$shop->getTag()->getName()},
                "item": {link //Tag:tag $shop->getTag()}
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": {$shop->getName()},
                "item": {link //this}
            }
      ]
    }
</script>


    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
        {/foreach}
            ]
        }
    </script>

    <script>
        const elementsWithBrochureId = document.querySelectorAll('[data-brochure-id]');
        console.log(elementsWithBrochureId.length);

        if (elementsWithBrochureId.length > 0) {
            if (checkCookie('userLocation') === false && navigator.geolocation) {
                getLocationFromBrowser();
            }
        }

        elementsWithBrochureId.forEach(element => {
            const brochureId = element.dataset.brochureId;

            element.addEventListener('click', function(event) {
                event.preventDefault();

                const linkElement = element.querySelector('a');
                if (!linkElement) {
                    console.error('No <a> tag found in this element.');
                    return;
                }

                const xhr = new XMLHttpRequest();
                xhr.open('POST', {link :Api:Offerista:brochureClick}, true);
                xhr.setRequestHeader('Content-Type', 'application/json');

                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        // Redirect to the href of the <a> tag
                        window.location.href = linkElement.href;
                    }
                };

                xhr.send(JSON.stringify({ brochureId: brochureId }));
            });

            const xhr = new XMLHttpRequest();
            xhr.open('POST', {link :Api:Offerista:brochureImpression}, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.send(JSON.stringify({ brochureId: brochureId }));
        });
    </script>
{/block}

{define cities}
    <div n:if="$shop->isStore() && count($cities) > 0">
        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
            {_kaufino.shop.city, [brand => $shop->getName()]}
        </h2>

        <p class="k-tag mb-5">
            {foreach $cities as $city}
                <span class="k-tag__inner {$iterator->counter > 24 ? 'hidden' : ''}">
                    <a n:href="City:shop $city, $shop" class="k-tag__item">{$shop->getName()} {$city->getName()}</a>
                </span>
            {/foreach}
        </p>

        <p n:if="$shop->isStore() && count($cities) > 23" class="d-flex">
            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
        </p>
    </div>
{/define}

{block content}

{var $actualLeaflets = 4}
{var $actualShops = 30}
{var $actualActiveLeaflets = 42}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container o-leaflet-brand">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">                
                <div class="k-profile-header k-profile-header--sm-center">                    
                    <a n:href="Shop:shop $shop" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source data-srcset="{$shop->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:160,140}" width="160" height="140" alt="{$shop->getName()}" class="lazyload k-profile-header__logo">
                        </picture>							
                    </a>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {var $heading = $getHeadingFromPageExtension($pageExtension)}
                                {$heading}
                            {else}
                                {$heading1}
                            {/if}                                                        
                        </h1>                                                                    
                    </div>                                       
                </div>        

                {if $user->isLoggedIn() && $localization->isHungarian()}
                    <div class="k-tabs k-content">
                        <div class="k-tabs__buttons">
                            <a n:href="shop, $shop" class="k-tabs__tabLink active">                                
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {$shop->getName()}
                            </a>
                            <a n:href="offers, $shop" class="k-tabs__tabLink">                                
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {_oferto.shop.offers.title, ['shopName' => $shop->getName()]}
                            </a>
                            <a n:href="stores, $shop" class="k-tabs__tabLink">                                
                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {_oferto.shop.stores.title, ['shopName' => $shop->getName()]}
                            </a>                            
                        </div>                        
                    </div>                    
                {/if}                                  

                {* Letáky *}
                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper mt-3">                                                                              

                        {if $channel == 'c1' && $localization->isCzech()}
                            <!-- Medium rectangle -->
                            <div id="protag-medium_rectangle"></div>
                            <script type="text/javascript">
                            window.googletag = window.googletag || { cmd: [] };
                            window.protag = window.protag || { cmd: [] };
                            window.protag.cmd.push(function () {
                                window.protag.display("protag-medium_rectangle");
                            });
                            </script>

                        {elseif $channel !== 'e1' && $channel !== 'e2' && $channel !== 'e3'}
                            {if $website->hasAdSense()}        
                                <div class="k-leaflets__item k-leaflets__item--first mb-5">              
                                    <!-- oferto.com / mobile_rectangle1 -->
                                    {include "../components/mobile_rectangle1.latte"}
                                </div> 
                            {/if}  
                        {/if}                          

                        {foreach $leaflets as $leaflet}
                            {breakIf $leaflet->isExpired() && $iterator->getCounter() > 5}
                            
                            <div class="k-leaflets__item mb-5" n:attr="data-brochure-id: $leaflet->getOfferistaBrochureId()">
                                <div class="">
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                                        <picture n:if="$leaflet->getFirstPage()">
                                            <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                                        </picture>
                                    </a>
                                    <div class="k-leaflets__title mt-0 mb-0">
                                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                                            {if $leaflet->isChecked() === false}
                                                {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                                            {else}
                                                {$leaflet->getName()}
                                            {/if}
                                        </a>
                                    </div>
                                    <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button mt-3">{_"$websiteType.shop.showLeaflet"}</a>
                                </div>
                            </div>
                                                    
                            {if $iterator->getCounter() == 2}        
                                {if $channel == 'c1' && $localization->isCzech()}
                                    <!-- Medium rectangle1 -->
                                    <div id="protag-medium_rectangle_1"></div>
                                    <script type="text/javascript">
                                    window.googletag = window.googletag || { cmd: [] };
                                    window.protag = window.protag || { cmd: [] };
                                    window.protag.cmd.push(function () {
                                        window.protag.display("protag-medium_rectangle_1");
                                    });
                                    </script>
                                {else}                        
                                    <div n:if="$website->hasAdSense()" class="k-leaflets__item k-leaflets__item--mobile mb-5">
                                        <!-- oferto.com / mobile_rectangle2 -->
                                        {include "../components/mobile_rectangle2.latte"}
                                    </div>                                
                                {/if}
                            {/if}
                        {/foreach}                        
                    </div>

                    {if $expiredLeaflets}
                        <p class="d-flex">
                            <a n:href="Archive:archive, $shop" class="link ml-auto">{_"$websiteType.leaflets.expiredMetaTitle", [brand => $shop->getName()]} »</a>
                        </p>
                    {/if}
                {elseif (!$shop->isEshop())}
                    <div class="alert alert-info mx-3">{_"$websiteType.shop.noLeaflets"}</div>
                {/if}                                                          
                
                {if $channel == 'c1' && $localization->isCzech()}
                    <!-- Leaderboard -->
                    <div id="protag-leaderboard"></div>
                    <script type="text/javascript">
                    window.googletag = window.googletag || { cmd: [] };
                    window.protag = window.protag || { cmd: [] };
                    window.protag.cmd.push(function () {
                        window.protag.display("protag-leaderboard");
                    });
                    </script>

                {else}
                    <!-- oferto.com / pr_native1 -->
                    {include "../components/pr_native1.latte"}                

                    <!-- oferto.com / pr_native3 -->
                    {include "../components/pr_native3.latte"}                                
                {/if}

                {*
                <div class="mt-4 mb-5">                                                         
                    <p class="k__text mw-900 mb-0 ml-0">{_oferto.shop.bottomText, [brand => $shop->getName(), actualLeaflets => $actualLeaflets]|noescape}</p>
                </div>                 
                *}

                {*include cities*}                                          

                {if $contentBlocksAllowed}
                    {foreach $contentBlocks as $contentBlock}
                        {continueIf $contentBlock->getType() === 'legacy'}

                        <div class="k-content k__text mw-900 mb-5 ml-0" n:if="$contentBlock->getContent()">
                            <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                            {$contentBlock->getContent() |content|noescape}
                        </div>
                    {/foreach}
                {else}
                    <p class="k-profile-header__text mw-900 ml-0">
                        {if $pageExtension && $pageExtension->getShortDescription()}
                            {$pageExtension->getShortDescription()}
                        {else}
                            {_"$websiteType.shop.text", [brand => $shop->getName()]|noescape}
                        {/if}
                    </p>

                    {if $pageExtension && $pageExtension->getLongDescription()}
                        <div class="k-content k__text mw-900 mb-5 ml-0">
                            {$pageExtension->getLongDescription()|content|noescape}
                        </div>
                    {/if}
                {/if}                

                <div n:if="count($similarShops)-1 > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.shop.otherShops.title"}</h2>
                    <p class="k__text mw-900 mb-5 ml-0">{_"$websiteType.shop.otherShops.text", [brand => $shop->getName(), actualShops => $actualShops, actualActiveLeaflets => $actualActiveLeaflets]|noescape}</p>

                    <div class="k-shop">    
                        {foreach $similarShops as $similarShop}
                            {continueIf $similarShop->getId() == $shop->getId()}
                            <a n:href="Shop:shop $similarShop" n:class="$iterator->counter > 13 ? 'hidden', 'k-shop__item'">
                                <span class="k-shop__image-wrapper">
                                    <picture>
                                        <source data-srcset="{$similarShop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$similarShop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$similarShop->getName()}" class="lazyload">
                                    </picture>                                    
                                </span>
                                <small class="k-shop__title">{$similarShop->getName()}</small>
                            </a>
                        {/foreach}
                    </div>

                    <p n:if="count($similarShops) > 13" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                {*if $localization->isCzech() || $localization->isHungarian() || $localization->isItaly()}
                    {var $hasAlternativeName = false}
                    {foreach $similarShops as $similarShop}
                        {continueIf $similarShop->getId() == $shop->getId()}
                        {continueIf !$similarShop->hasAlternativeNames()}
                        {var $hasAlternativeName = true}
                        {breakIf true}
                    {/foreach}

                    <div n:if="$hasAlternativeName && count($similarShops)-1 > 0">
                        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                            {_"$websiteType.shop.alternativeName"}
                        </h2>

                        <p class="k-tag mb-5">
                            {foreach $similarShops as $similarShop}
                                {skipIf $similarShop->getId() == $shop->getId()}
                                {skipIf !$similarShop->hasAlternativeNames()}

                                <span class="k-tag__inner">
                                    <a n:href="Shop:shop $similarShop" class="k-tag__item">{$similarShop->getAlternativeName($shop->getId())}</a>
                                </span>

                                {breakIf $iterator->counter === 18}
                            {/foreach}
                        </p>
                    </div>
                {/if*}
            </div>
        </div>
    </div>	    

	<div class="float-wrapper__stop"></div>	
</div>

<div class="container" n:if="$articles">        
    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_"$websiteType.shop.otherArticles"}</h2>
    {include "../components/article-list.latte", articles => $articles}    
</div>

<script type="application/ld+json" n:if="count($leaflets)">
    {
        "@context": "http://schema.org",
        "itemListElement": [
            {foreach $leaflets as $leaflet}
                {
                    "endDate": {$leaflet->getValidTill()->format('Y-m-d')},
                    "startDate": {$leaflet->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$leaflet->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:Oferto:Shop:shop $leaflet->getShop()},
                        "image": {$shop->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$leaflet->getFirstPage() ? $leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop','webp' : ""},
                    "name": {$leaflet->getName()},
                    "url": {link //:Oferto:Leaflet:leaflet, $leaflet->getShop(), $leaflet},
                    "description": "",
                    "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {link //:Oferto:Shop:shop $leaflet->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
    {/foreach}
        ],
        "@type": "OfferCatalog"
    }
</script>