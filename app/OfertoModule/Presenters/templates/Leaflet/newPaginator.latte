{var $countOfPages = $leaflet->getCountOfPages()}
{var $interval = 1}

{var $nextPageIsPromoPage = false}

{if $currentPage === $leaflet->getCountOfPages()}
    {var $countOfPages = $countOfPages + 1}
    {var $nextPageIsPromoPage = true}
{elseif $isVirtualPage}
    {var $countOfPages = $countOfPages + 1}
{/if}

<div class="k-paginator__wrapper">
    <a n:if="$currentPage > 1" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $currentPage-1" class="k-paginator__button">«&nbsp;<span class="k-paginator__button-desktop-text">{_'app.paginator.prev'}</span></a>
    <a n:if="$currentPage > 1+$interval" n:href="leaflet shop => $shop, leaflet => $leaflet, page => 1" class="k-paginator__item">1</a>
    <small n:if="max(1, $currentPage-$interval) > 1+1" class="k-paginator__separator">...</small>

    {foreach range(max(1, $currentPage-$interval), min($countOfPages, $currentPage+$interval)) as $pageNumber}
        <a n:href="leaflet shop => $shop, leaflet => $leaflet, page => $pageNumber" n:class="$currentPage == $pageNumber ? active, k-paginator__item" {if $openRelatedLeafletAllowed && $pageNumber === $leaflet->getCountOfPages() + 1}onclick="window.open({link openRelatedLeaflet!, leafletId: $leaflet->getId()}, '_blank');"{/if}>{$pageNumber}</a>
    {/foreach}

    <small n:if="min($countOfPages, $currentPage+$interval) < $countOfPages-1" class="k-paginator__separator">...</small>
    <a n:if="$currentPage < $countOfPages-$interval" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $countOfPages" class="k-paginator__item" {if $openRelatedLeafletAllowed && $pageNumber === $countOfPages}onclick="window.open({link openRelatedLeaflet!, leafletId: $leaflet->getId()}, '_blank');"{/if}>{$countOfPages}</a>
    <a n:if="$currentPage < $countOfPages" n:href="leaflet shop => $shop, leaflet => $leaflet, page => $currentPage+1" {if $openRelatedLeafletAllowed && $nextPageIsPromoPage}onclick="window.open({link openRelatedLeaflet!, leafletId: $leaflet->getId()}, '_blank');"{/if} class="k-paginator__button"><span class="k-paginator__button-desktop-text">{_'app.paginator.next'}</span>&nbsp;»</a>
</div>