
{block title}
    {capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
    {capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
    {capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
    {_"$websiteType.leaflet.metaTitle", [brand => $leaflet->getShop()->getName(), validSince => $validSince]}
    {if $currentPage > 1} - {_"$websiteType.leaflet.metaTitlePageSuffix", [page => $currentPage]}{/if}
{/block}

{block description}
    {capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
    {capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
    {capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
    {capture $validTillDay}{$leaflet->getValidTill()|dayGenitive}{/capture}
    {capture $leafletPageCount}{count($leaflet->getPages())}{/capture}

    {_"$websiteType.leaflet.metaDesc", [brand => $leaflet->getShop()->getName(), leafletPageCount => $leafletPageCount, validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay]|noescape}
{/block}

{block robots}{if $leaflet->isArchived()}noindex,follow{elseif $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block head}
    <!-- oferto.com / sticky -->
    {include "../components/sticky.latte"}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_"$websiteType.navbar.leaflets"}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag()">{$shop->getTag()->getName()}</a> |
            <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a> |

            {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
                {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
            {else}
                <span class="color-grey">{$leaflet->getName()}</span>
            {/if}
        </p>
    </div>
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{capture $validTillDay}{$leaflet->getValidTill()|dayGenitive}{/capture}
{capture $leafletPageCount}{count($leaflet->getPages())}{/capture}

<div class="leaflet k-lf-layout o-optimize-2">
    <div class="container">
        <div class="leaflet__content">
            <div class="d-block overflow-hidden">
                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    <div class="" n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                        <!-- MrOferto - Responsive - 1-->
                        <ins class="adsbygoogle adslot-2"
                            style="display:block"
                            data-ad-client="ca-pub-3454721603118795"
                            data-ad-slot="9919352231"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    <div class="" n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                        <!-- MrOferto - Responsive - 1 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4233432057183172"
                            data-ad-slot="8096953920"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                        <!-- MrOferto - Responsive - 1 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-6277661924921144"
                            data-ad-slot="5929296447"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>

                    {/if}

                    {if $website->hasAdSense() && $website->hasStarioAdsense()}
                        <!-- MrOferto - Responsive - 1 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4363508753950122"
                            data-ad-slot="6524392817"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    {/if}
                </div>
                
                <div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
                    <div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {_"$websiteType.leaflet.leaflet", [brand => $leaflet->getShop()->getName()]} c5a
                            <span class="leaflet__date" n:if="$leaflet->isChecked()">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate}</span>
                        </h1>

                        {*
                        <p class="page-header__text mw-600">
                            {_oferto.leaflet.metaDesc, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay, leafletPageCount => $leafletPageCount]|noescape}
                        </p>
                        *}
                    </div>

                    {*
                    <div class="leaflet__detail-header-side">
                        <a n:href="Shop:shop $leaflet->getShop()">
                            <picture>
                                <source data-srcset="{$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$leaflet->getShop()->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$leaflet->getShop()->getName()}" class="lazyload leaflet__detail-header-logo">
                            </picture>
                        </a>
                    </div>
                    *}
                </div>

                {if false && $leaflet->isArchived() === false}
                    {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage, withoutNumbers: $localization->isGermany() === false}
                {/if}

                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    <div n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                        <!-- MrOferto - Responsive - 5 -->
                        <ins class="adsbygoogle adslot-2"
                            style="display:block"
                            data-ad-client="ca-pub-3454721603118795"
                            data-ad-slot="1719922261"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    <div n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                        <!-- MrOferto - Responsive - 5 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4233432057183172"
                            data-ad-slot="9693052724"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                        <!-- MrOferto - Responsive - 5 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-6277661924921144"
                            data-ad-slot="4041499701"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>

                    {/if}

                    {if $website->hasAdSense() && $website->hasStarioAdsense()}
                        <!-- MrOferto - Responsive - 5 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4363508753950122"
                            data-ad-slot="1929761435"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    {/if}
                </div>

                <div class="leaflet-preview o-leaflet-preview mb-5">

                    <picture id="" data-expand="100">
                        <source
                                data-srcset="
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                            "
                                type="image/webp"
                        >
                        <img
                                src="{$basePath}/images/placeholder-870.png"
                                data-srcset="
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null} 768w,
                                {$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:1740,null} 1740w
                            "
                                data-sizes="auto"
                                width="870"
                                height="1190"
                                alt="{$leaflet->getShop()->getName()}"
                                class="lazyload"
                        >
                    </picture>
                </div>

                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    <div n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                        <!-- MrOferto - Responsive - 4 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-3454721603118795"
                            data-ad-slot="3541097402"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    <div n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                        <!-- MrOferto - Responsive - 4 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4233432057183172"
                            data-ad-slot="6783872258"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                        <!-- MrOferto - Responsive - 4 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-6277661924921144"
                            data-ad-slot="9293826384"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    {/if}

                    {if $website->hasAdSense() && $website->hasStarioAdsense()}
                        <!-- MrOferto - Responsive - 4 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4363508753950122"
                            data-ad-slot="5869006448"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    {/if}
                </div>

                {if $leaflet->isArchived() === false}
                    {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage, withoutNumbers: $localization->isGermany() === false}
                {/if}

                <div class="ads-container">
                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                    <div n:if="$website->hasAdSense() && $website->hasOfertoAdsense()">
                        <!-- MrOferto - Responsive - 2 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-3454721603118795"
                            data-ad-slot="8234472140"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    <div n:if="$website->hasAdSense() && $website->hasKaufinoAdsense()">
                        <!-- MrOferto - Responsive - 2 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4233432057183172"
                            data-ad-slot="5842309769"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    </div>

                    {if $website->hasAdSense() && $website->hasAdsalvaAdsense()}
                        <!-- MrOferto - Responsive - 2 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-6277661924921144"
                            data-ad-slot="6859234731"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    {/if}

                    {if $website->hasAdSense() && $website->hasStarioAdsense()}
                        <!-- MrOferto - Responsive - 2 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4363508753950122"
                            data-ad-slot="8244668592"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>
                    {/if}
                </div>

                {capture $leafletBrandLink}
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                <div class="px-3 px-lg-0">
                    <p class="color-grey fz-m lh-15 mb-3"><strong>{_"$websiteType.leaflet.smallTitle", [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc.1", [leafletBrandLink => $leafletBrandLink, brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}
                    </p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc.2", [leafletBrandLink => $leafletBrandLink, brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay, validTill => $validTill, validTillDay => $validTillDay, leafletPageCount => $leafletPageCount] |noescape}
                    </p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_"$websiteType.leaflet.desc.3"}
                    </p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_"$websiteType.leaflet.backToLeaflets"}</a>
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_"$websiteType.leaflet.allBrandLeaflets", [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>
            </div>
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;" n:if="count($similarLeaflets) > 0">
            <!-- oferto.com / halfpage1 -->
            {*include "../components/halfpage1.latte"*}

            <div class="lf__box">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_"$websiteType.leaflet.similarLeaflets", [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">
                    <div n:foreach="$similarLeaflets as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source data-srcset="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$similarLeaflet->getName()}" class="img-responsive lazyload">
                            </picture>
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="lf__box lf__box-lg-border">
                <h3 class="lf__box-title px-3 px-lg-0">{_"$websiteType.leaflet.recommendedLeaflets"}</h3>
                <div class="lf__box-wrapper">
                    {foreach $recommendedLeaflets as $recommendedLeaflet}
                        {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                        <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                            <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                <picture>
                                    <source data-srcset="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                    <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$recommendedLeaflet->getName()}" class="img-responsive lazyload">
                                </picture>
                            </a>
                            <p class="fz-xxs fz-sm-xs mb-0">
                                <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                            </p>
                        </div>
                    {/foreach}
                </div>
            </div>

            <div class="float-wrapper">
                <!-- oferto.com / halfpage2 -->
                {include "../components/halfpage1.latte"}
            </div>
        </div>
    </div>

    <div class="float-wrapper__stop"></div>    
</div>


