navbar:
	shops: "<PERSON><PERSON>kkanlar"
	leaflets: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
	search:
		placeholder: "Mağazaları arayın"
		submit: "Arama"

	moreShops: "<PERSON><PERSON>er mağazalar"
	home: "Anasayfa"

footer:
	copyright: "Letado Tüm hakları saklıdır."	
	shops: "Dükkanlar"
	category: "Kategoriler"	
	aboutLetado: "Letado Hakkında"
	cookies: "Cookies"
	leaflets: "Broşürler"
	aboutUs: "Hakkımızda"
	nextCountries: "<PERSON>ğer ülkeler"

search:
	title: "<PERSON>ma sonuçları \"%query%\""
	noResults: "Ne kadar bakarsak bakalım, hiçbir şey bulamıyoruz."

homepage:
	title: "Son broşürler ve satıştaki ürünler"
	text: "Büyük perakendecilerden geniş bir yelpazede indirimli ürünler sunan son broşürler"
	allLeaflets: "Tüm broşürler"
	shops: "Dükkanlar"
	allShops: "<PERSON><PERSON><PERSON> mağazalar"	

leaflets:
	title: "<PERSON><PERSON>şürler"
	text: "En son broşürlerin teklifi. Her zaman özel ürünler bulabilmeniz için her gün sizin için broşürler ekliyoruz."

leaflet:
	metaTitle: 'En son %brand% broşürü şu tarihten itibaren geçerlidir %validSince%'
	metaTitlePageSuffix: 'sayfa %page%'
	metaDesc: 'Najnovší leták z obchodu  %brand% itibaren geçerli &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Son broşür %leafletBrandLink% 'ten %validSince% ile %validTill% arasında geçerlidir. leafletPageCount% sayfasında tüm güncel indirimleri bulabilirsiniz. Letado sayfasında, favori mağazalarınızın sunduğu tüm broşürler hakkında her zaman güncel bilgiler bulacaksınız."
	longDesc1: "GeçerliSince% ile %geçerliTill% arasındaki güncel promosyon broşüründe bulacağınız %leafletBrandLink% 'in özel tekliflerinden yararlanın. Bugünlerde her şey giderek daha pahalı hale geliyor - arabalar, uçuşlar, tatiller, turlar, elektronik eşyalar, beyaz eşyalar, aynı zamanda kıyafetler ve çok daha fazlası. Ancak, düzenli aylık harcamalarınız için tüketici kredisi veya başka bir kredi almanıza gerek yok. Letada'da, en popüler mağazaların indirimlerini mümkün olan en kısa sürede size ulaştırmak için çalışıyoruz. Böylece en son promosyonlardan veya indirimlerden yararlanabilir ve ev bütçenizden tasarruf edebilirsiniz."
	longDesc2: "Bizimle, mali durumunuzla ilgili size yardımcı olması için bir mali danışman tutmanıza gerek yok çünkü bunu sizin için yapabiliriz. Daha sonra kalan parayı yurt dışı tatilleri, yerel otel ve misafirhanelere geziler gibi şeyler için veya bir sonraki ipotek ödemeniz için finansal tampon olarak kullanabilirsiniz."
	longDesc3: "Finansal olarak bağımsız olmak ve fon fazlasına sahip olmak harika bir duygudur. Bu aynı zamanda hayat sigortası, ev sigortası ya da zorunlu sigorta ve arıza sigortası gibi kaliteli sigortaları karşılayabileceğiniz anlamına gelir. Bu, mali durumunuzu önemli ölçüde olumsuz etkileyebilecek beklenmedik etkilerden korur. Dolayısıyla sigorta mali durumunuzun istikrarını korur."		
	longDesc4: "Letado olarak, günlük alışverişlerinizde mümkün olduğunca çok tasarruf etmenize yardımcı olmak için elimizden gelen her şeyi yapmaya devam edeceğiz, böylece hayalinizdeki arabayı, en sevdiğiniz kıyafetleri, elektronik eşyaları satın alabilir veya kaliteli sigorta için ödeme yapabilirsiniz. Umarız bu %leafletBrandLink% broşürü, %validSince% ile %validTill% arasında geçerlidir, size en azından biraz yardımcı olur ve hayallerinize daha yakın olursunuz!"
	smallTitle: "%brand% itibaren geçerli"	
	recommendedLeaflets: "Popüler broşürler"
	similarLeaflets: "Diğer broşürler %brand%"
	backToLeaflets: "Tüm broşürlerin listesine geri dön"	
	allBrandLeaflets: "Tüm broşürler %brand%"
	goToShop: "Mağazaya git"

shops:
	title: "Dükkanlar"
	text: "Her gün size yeni broşürlerini sunduğumuz en popüler perakendecilerden bir seçki."

shop:
	leaflets: "broşürler"
	text: "Son broşür %brand% avantajlı tekliflerle."
	button: "Mağazaya git %brand%"	
	noLeaflets: "Sizin için en son broşürü arıyoruz... Lütfen daha sonra tekrar deneyin."
	otherShops: "Diğer mağazalar."
	defaultTitleSuffic: '%shopName% - son broşür, satıştaki ürünler'
	otherLeaflets: "Diğer broşürler %brand%"
	type:
		shopTitle: "{$shopName|upper} broşür {if $currentLeafletFromDate} kimden {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + eylem broşürü gelecek hafta"
		eshopTitle: "%brand% İndirim"
		eshop: "İlham ve pazarlıklarla dolu kataloglarındaki en son %brand% promosyonlarına göz atın. En son %brand% indirimleri her zaman mevcuttur, böylece indirimli ürünleri asla kaçırmazsınız."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} gelecek hafta {$nextLeafletFromDate|dayGenitive} adresinden broşür {$nextLeafletFromDate|date:'j.n.Y'} + geçerli broşür"
	     withCurrentLeaflet: "{$shopName|upper} broşür gelecek hafta + mevcut broşürden {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} broşür gelecek hafta + güncel promosyon broşürü çevrimiçi"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} broşürü gelecek hafta ✅ Özel {$shopName|upper}'a göz atın NEXT WEEK'S LETTER from {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Ayrıca bu haftanın {$shopName|upper} broşürünün güncel PDF'i de çevrimiçi olarak mevcuttur."
	    withCurrentLeaflet: "{$shopName|upper} broşürü gelecek hafta ✅ Özel {$shopName|upper}'a göz atın Gelecek hafta için FLYER. Ayrıca bu haftanın güncel PDF broşürüne {$shopName|upper} adresinden ulaşabilirsiniz. {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} broşürü gelecek hafta ✅ Özel {$shopName|upper}'a göz atın Gelecek hafta için FLYER. Bu haftanın promosyonlarını içeren güncel PDF broşürü {$shopName|upper} çevrimiçi olarak da mevcuttur."

tag:
	text: "Kategorideki en son broşürlerin teklifi %tag%."
	noLeaflets: "Sizin için en son broşürü arıyoruz... Lütfen daha sonra tekrar deneyin."
	otherShops: "Diğer mağazalar"	

about:
	title: "Hakkımızda"
	text: "Amacımız kullanıcılara zaman ve para kazandırmaktır. Her gün size en popüler perakendecilerin güncel broşürlerini getiriyor ve ürünlerdeki özel teklifleri ararken zaman kazanmanızı sağlıyoruz."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
