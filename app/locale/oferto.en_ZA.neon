navbar:
	shops: Stores
	leaflets: Catalogues
	articles: Blog
	search:
		placeholder: Store
		submit: Search

	moreShops: Other stores
	home: Home

footer:
	copyright: MrOferto - all rights reserved.
	text: 'Mr<PERSON>fer<PERSON> is your go-to website for finding current and upcoming promotional catalogues from your favorite retailers. We work hard to bring you each catalogue as soon as possible, so you have plenty of time to plan your next purchase and shop smartly. Don''t forget to check out our Blog for various advice, tips, and information on interesting topics that will expand your horizons and inspire your everyday shopping and cooking. We believe MrOferto will be a valuable resource for you, helping you find exactly what you need.'
	shops: Stores
	category: Category
	article: Blog
	aboutOferto: Website Features
	cookies: Cookies
	leaflets: Catalogues
	articles: Blog
	aboutUs: About MrOferto
	nextCountries: Other countries

search:
	title: search results "%query%"
	noResults: Page not found. Let's get you back on track with our homepage.

homepage:
	title: Latest specials
	h1: Latest specials
	text: 'Save big today! Browse the current specials to find amazing deals on a wide range of items. Hurry, these offers won''t last forever!'
	allLeaflets: Other catalogues & specials
	shops:
		title: Current deals and specials from your favorite retailers
		text: Stay updated with the newest deals and specials from leading retailers. Browse our comprehensive collection of promotional catalogues to find the best discounts and savings on your favorite products.

	article:
		title: Blog
		text: Get inspired with our tips for enhancing your shopping and dining experiences! Explore our blog to broaden your horizons.
		button: More articles

	allShops: More stores
	city: Check out the latest specials in your city

articles:
	title: Blog

article:
	button: View all blog articles

leaflets:
	title: Discover the latest catalogues
	text: Stay updated with the latest catalogues full of great specials from your favorite stores. Explore current deals and save on your next shopping trip.
	expiredTitle: Expired catalogues
	expiredText: 'If you are interested, check out the expired catalogues from the most popular retailers'
	expiredMetaTitle: Expired %brand% catalogues

leaflet:
	metaTitle: %brand% specials valid from %validSince%
	metaTitlePageSuffix: page %page%
	metaDesc: Discover amazing specials and save big with the this %brand% catalogue!
	leaflet: %brand% specials
	desc:
		1: 'The %leafletBrandLink% catalogue, valid from %validSince% and %validTill%, highlights the latest specials on %leafletPageCount% pages. Browse the entire catalogue and save big on your next purchase!'
		2: 'If you didn''t find what you''re looking for in this catalogue, or if you want to compare current prices, check out others from our rich selection. Compare the current specials and make sure you get the best value for your money on your next purchase!'
		3: ''

	smallTitle: %brand% catalogue valid from
	recommendedLeaflets: Similar catalogues
	similarLeaflets: Other %brand% catalogues
	backToLeaflets: Back to the list of all catalogues
	allBrandLeaflets: Other %brand% catalogues
	button: View more
	otherArticles: Latest articles
	expiredLeafletTitle: %brand% specials valid until %validTill%
	expiredLeafletDescription: This %brand% catalogue is no longer valid.
	actualLeafletValidSince: 'The current catalogue is available and valid from %validSinceDay%, %validSince%. ✅'
	expiredLeafletHeading: This catalogue is no longer valid
	expiredLeafletLinkToShop: You can find the current %brand% catalogue here
	futureLeafletTitle: Latest %brand% specials valid from %validSince% ⭐
	futureLeafletDescription: Check out the upcoming %brand% specials. Plan your next purchase and enjoy great savings on your favourite products! 🛒
	leafletPageDescription: 'Page %pageNumber% of the %brand% flyer, valid from %validSinceDay% %validSince%'

shops:
	title: All your favorite retailers
	metaTitle: 'Browse the latest Boxer specials, Shoprite specials, Pick n Pay specials and more! Start saving now with our selection of catalogues.'
	text: Stay updated with the newest specials from leading retailers. Browse our comprehensive collection of promotional catalogues to find the best discounts and savings on your favorite products.
	metaDescription: 'Stay updated with the newest specials from leading retailers. Shoprite specials, Boxer specials and more! Browse our comprehensive collection of promotional catalogues.'

shop:
	showLeaflet: Browse specials
	leaflets: catalogues
	text: Be the first to browse the latest %brand% specials and save on your next purchase! Enjoy the %brand% catalogue at your fingertips with MrOferto.
	button: Go to %brand%
	noLeaflets: We're looking for a promotional catalogue for you... Please try again later.
	bottomText: '%brand% is one of the most popular stores in the country and regularly publishes new promotional catalogues containing dozens of products from a wide range of categories. This is the perfect place to get inspiration for your upcoming purchases! On this page, you will find a total of %actualLeaflets%. You can view each %brand% stores catalogue on both your computer and/or mobile device.'
	offersLeaflets: Flyers from the category %category%
	otherShops:
		title: Other stores
		text: 'Want to see more? On MrOferto, you can find the latest specials from many popular retailers. Check out their catalogues, compare what they offer, and make sure you are getting the best deal!'

	defaultTitleSuffic: %shopName% specials
	otherArticles: Latest Articles
	type:
		shopTitle: '{$shopName|upper} catalogue{if $currentLeafletFromDate} valid from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''n.j.Y''}{/if}'
		eshopTitle: %brand% discount
		eshop: 'Check out the latest events %brand% in their catalogue full of inspiration and bargains. The latest %brand% discounts are always available to you, so you''ll never miss out on discounted items thanks to the promotional catalogue.'

tag:
	text: Offer of promotional catalogues from the %tag% category.
	noLeaflets: We're looking for a promotional catalogue for you... Please try again later.
	otherShops: Other stores
	offers:
		title: %tag% deals in current catalogues
		metaDescription: Check out the deals on %tag% in the flyers of the most well-known stores. Don't miss out on discounts and promotional prices that can be found in the new flyers.
		titleWithBestOffer: %tag% on sale from ⏩ %price% in the current flyer.
		metaDescriptionWithBestOffer: Check out the promotions for product %tag% in the flyers of the most well-known stores. Don't miss out on discounts on products at promotional prices in the new flyers.
		text: 'Explore all the promotions for %tag% in the <a href="%leafletLink%">leaflets</a> and don''t miss out on additional discounts on selected products. The promotional prices for %tag% from the current leaflets are regularly updated, and finding the <strong>cheapest</strong> price is very easy.'

about:
	title: About us
	text: Our goal is to save users time and money. Every day we bring you up-to-date catalogues from the most popular retailers and save you time searching for product deals.
	address: 'Adsalva s.r.o.<br>Prague, Czech Republic<br>Na Porici 1067/25, New Town Prague 1<br><br>Tax number CZ03786986 <br><br> info[at]adsalva.com'

city:
	city:
		title: Catalogues in %city%
		text: 'Current promotional catalogues in %city%. In the promotional catalogues in the city of %city%, you will find not only discounted products but also many other discounts. Discounts can be found in stores such as %stores% and more.'
		text2: 'Promotional catalogues and current discounts in the city of %city%. In the catalogues of popular stores in the city of %city%, you will find not only discounted products but also many other discounts and offers at the best prices. Browse catalogues from the largest stores in the city of %city%, such as %stores%.'
		h2: 'Catalogues, promotions, and discounts of stores in %city%'
		storesTitle: Branches in %city%
		storesMoreButton: More branches »
		leafletStores:
			title: %brand% catalogue
			store: %brand% catalogue %city%
			storeWithCity: %brand% catalogue %city%

		otherShops: Stores in %city%
		nearestCity: Other cities with catalogues nearby
		nearestCityWithShop: Other cities with %shopName% catalogues nearby
		categoriesInText: %category% catalogues
		citiesInText: Catalogues in %city%
		generatedText:
			1: 'The city of %city%, with a population of %population%, offers numerous stores, to which we bring new catalogues every week. Interesting promotions and discounts are prepared not only for the city of %city% but also for others nearby, such as the cities of %cities%.'
			2: 'The list of current catalogues from the largest stores in the city of %city% can be found here:'
			3: Promotional catalogues from stores such as %stores% and many others are also available. Their promotional offers for the month of %month% can be found in the %shopsLink% section.
			leaflet: Catalogue from %brand% current %validSince% – %validTill%
			and: and
			or: or

	store:
		h1: %brand% %city% %address%
		store: %fullAddress%
		h2: Stores of %brand% in %city%
		title: '%brand% %address%, catalogue, and opening hours 🕔'
		description: 'Compare offers in catalogue, find the exact address and opening hours, or read about the assortment that awaits you at the %brand% store %address%.'
		open: Open
		closed: Closed
		text: 'The %brand% store %address% regularly offers advantageous promotions and discounts on a diverse range of products, where you can take advantage of the popular %brand% catalogue when making purchases.'
		h2bottom: %brand% catalogue %city% %street%
		text2WithoutStores: 'Customers can conveniently view it online, as well as promotions available at other branches.'
		text2: 'Customers can conveniently view it online, as well as promotions available at branches %stores%.'
		or: or
		others: and others
		textBottom: 'The %brand% store %address% offers customers not only a wide range of products but also low prices regularly communicated in the %brand% catalogues. The %fullAddress% branch is a favorite place for those looking for competitively priced offers. Thanks to the online availability of the %brand% catalogue, buyers always have current discounts at hand. If %brand% %address% does not offer everything the customer needs, they can also use other nearby stores, such as:'
		textBottom2: 'Find out the exact address, trading hours of your favorite stores, or the contact details all in one place. It also includes information on which branches are in your area and where additional advantageous offers can be found, as indicated by the promotional catalogues of selected stores.'
		sections:
			leaflets: Other catalogues in the category
			shops: Other stores nearby
			stores: Other %brand% stores nearby

	shop:
		title: %brand% catalogues in %city%
		metaTitle: %brand% catalogues in %city%
		storesTitle: %brand% branches in %city%
		h2: 'Catalogues, promotions, and discounts at %brand% store in %city%'
		text: '%brand% catalogues in %city% and their current discounts and promotions. In the %brand% catalogues %city%, you will find a wide range of products at the best prices. However, in the city of %city%, there is not only %brand%. Among other popular stores are %stores%.'
		metaDescription: '%brand% catalogues in %city% and their current discounts and promotions. In the %brand% catalogues %city%, you will find a wide range of products at the best prices. However, in the city of %city%, there is not only %brand%. Among other popular stores are %stores%.'
		leafletStores:
			title: %brand% catalogues %city%
			store: %brand% %city%

		cityLink: Catalogues in %city%
		shopLink: %shop% catalogues
		otherShops: Other stores in %city%
		shopLeaflet: %brand% catalogues
		citiesInText: %brand% catalogues in %city%
		offers: Offers from %brand% catalogues in %city%
		generatedText:
			1: Look for %shop% stores in %city% and check out their latest specials.
			2: 'To view the latest %shop% specials in %city%, valid from %leafletValidSince% to %leafletValidTill%, simply click <a href="%actualLeafletUrl%">here</a>.'
			3: 'Catalogues offer time-limited deals, discounts, as well as seasonal offers. They offer something for anyone. Detailed information about %shop% specials can be found in %shop% %city% catalogues, which are always availabe.'
			4: 'You can also find %shop% specials in nearby cities, such as %cities%.'
			5: 'For more shopping options, you can explore specials of other stores such as %stores% and others. If you are looking for a different store and its specials, you will surely find it in the <a href="%leafletsUrl%">Catalogues</a> section on the main page.'

