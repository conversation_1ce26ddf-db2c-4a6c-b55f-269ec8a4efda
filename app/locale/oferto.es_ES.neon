navbar:
	shops: "Tiendas"
	leaflets: "Folletos"
	articles: "Revista"
	search:
		placeholder: "Nombre de la tienda"
		submit: "Busque en"

	moreShops: "Otras tiendas"
	home: "Inicio"

footer:
	copyright: "Mr<PERSON>ferto Todos los derechos reservados."	
	text: "MrOferto es un sitio web que te permite encontrar folletos promocionales próximos, actuales e históricos de todas tus tiendas favoritas. Ofrecemos folletos de docenas de las marcas más populares y constantemente añadimos más. Nuestro equipo se asegura de que tengas la oportunidad de ver todos los próximos folletos tan pronto como estén disponibles, dándote tiempo suficiente para aprovechar las increíbles promociones. Además, en nuestra revista proporcionamos varios consejos, trucos y sugerencias que alimentan la creatividad y proporcionan inspiración para tus compras y tareas culinarias diarias. Esperamos que nuestro sitio web sea útil para ayudarte a realizar tus compras."
	shops: "Tiendas"
	category: "Categoría"	
	article: "Revista"
	aboutOferto: "MrOferto"
	cookies: "Cookies"
	leaflets: "Folletos"
	articles: "Revista"
	aboutUs: "Sobre nosotros"
	nextCountries: "Otros países"

search:
	title: "Resultados de la búsqueda \"%query%\""
	noResults: "Por mucho que busquemos, no encontramos nada."

homepage:
	title: "Folletos promocionales actuales"
	h1: "Folletos promocionales actuales"
	text: "Elige un folleto de una de tus tiendas favoritas. Esto te permitirá encontrar los numerosos folletos que ofrecemos de todos los hipermercados y supermercados más populares de toda España."
	allLeaflets: "Otros folletos promocionales"
	shops: 
		title: "Tiendas con folletos promocionales"
		text: "En esta oferta, puedes encontrar muchas tiendas individuales diferentes que emiten folletos promocionales especiales. Empieza haciendo clic en el logotipo de la tienda y podrás ver una selección de todos los folletos futuros, actuales y pasados. Esto te permitirá examinar cualquiera de ellos con más detalle."	
	article:
		title: "Revista"
		text: "Inspírate con nuestros consejos para una mejor experiencia gastronómica y de compra. Sigue mirando y averigua más sobre los productos que ofrecemos en nuestra revista."
		button: "Otros artículos"
	allShops: "Otros comercios con folletos promocionales"	

articles:
	title: "Revista"

article:
	button: "Ver todos los artículos"

leaflets:
	title: "Folletos promocionales actuales"
	text: "Oferta de folletos de descuentos actuales. Añadimos folletos actuales para usted todos los días."
	expiredTitle: "Folletos promocionales caducados"
	expiredText: "Eche un vistazo a la gama de folletos promocionales caducados de los minoristas más populares."
	expiredMetaTitle: "Folletos caducados %brand%"

leaflet:
	metaTitle: 'Folleto de oferta especial %brand% válido desde %validSince%.'
	metaTitlePageSuffix: 'página %page%'
	metaDesc: 'Estás viendo un folleto de %brand% con fechas de validez desde %validSinceDay% hasta %validTillDay%. En las páginas de %leafletPageCount% encontrarás todos los productos que están a la venta en la tienda de %brand% durante ese periodo.'
	leaflet: "%brand%"	
	desc: 
		1: "El folleto de la tienda %leafletBrandLink%, que está vigente entre %validSince% y %validTill%, ofrece todos los productos especiales en las páginas %leafletPageCount%. Navega por todo el folleto para conocer todas las ofertas actuales de %brand%."
		2: "Si ya has visto el folleto %leafletBrandLink% de %validSinceDay% a %validTill% y estás interesado en ver otro periodo, puedes volver al listado de todos los folletos de %brand% haciendo clic en el logotipo de la tienda que hay encima del folleto. Al hacerlo, tendrás la opción de elegir entre todas las ofertas actuales y futuras. Si estás interesado en consultar los folletos de otras tiendas, lo más fácil es hacer clic en el logotipo de MrOferto en la parte superior de la página. Esto te llevará a la página principal del sitio que muestra las marcas más populares de tu zona, y lo que tienen que ofrecer."
		3: "En MrOferto también encontrarás consejos y trucos que te ayudarán a facilitar tu experiencia de compra. Esto incluye consejos para ayudarte a preparar tus platos favoritos o para darte nuevas ideas e inspiración sobre lo que podrían ser esas comidas diarias. Toda esta información se encuentra en nuestra revista."		
	smallTitle: "%brand% válida desde"	
	recommendedLeaflets: "Folletos promocionales similares"
	similarLeaflets: "Otros folletos de la %brand%"
	backToLeaflets: "Volver a la lista de todos los folletos promocionales"	
	allBrandLeaflets: "Otros folletos de la %brand%"
	button: "Ver más páginas de folletos"
	otherArticles: "Últimos artículos"
	expiredLeafletTitle: "%brand% folleto válido hasta %validTill%"
	expiredLeafletDescription: "Este folleto de %brand% ya no está vigente. Su validez expiró el %validTill%."
	actualLeafletValidSince: "El folleto actual es válido desde %validSinceDay%, %validSince%. ✅"
	expiredLeafletHeading: "Este folleto ya no está vigente"
	expiredLeafletLinkToShop: "Puedes encontrar el folleto actual de %brand% aquí"
	futureLeafletTitle: "Último folleto de %brand% válido desde %validSince% ⭐"
	futureLeafletDescription: "Consulta las próximas promociones en el último folleto de %brand% ⭐ ¡Planifica tu próxima compra y compra de la manera más ventajosa! ✅"
	leafletPageDescription: 'Página %pageNumber% del folleto de %brand%, válido desde %validSinceDay% %validSince%'
shops:
	title: "Tiendas con folletos promocionales"
	metaTitle: "Tiendas con folletos promocionales"
	text: "Lista de todas las tiendas populares de las que te traemos nuevos folletos cada día."
	metaDescription: "Lista de todas las tiendas populares de las que te traemos nuevos folletos cada día."

shop:
	showLeaflet: "Ver el folleto"
	leaflets: "Folletos"
	text: "En esta página encontrarás todos los folletos y ofertas promocionales actuales de la tienda %brand%. Cada folleto muestra la fecha de caducidad de la promoción que se ofrece actualmente en esa tienda. Ten en cuenta que algunos de los artículos incluidos en el folleto pueden no estar disponibles mientras dure la promoción. Es importante que compruebes las fechas del folleto, ya que algunos productos promocionales pueden no estar a la venta durante todo el periodo de la promoción. Esta información se encuentra directamente en el interior del folleto, en las páginas individuales."
	button: "Ir a la tienda %brand%"	
	noLeaflets: "Estamos buscando un folleto promocional para ti... Por favor, inténtelo más tarde."
	bottomText: "%brand% es una de las tiendas más populares del país y publica regularmente nuevos folletos promocionales con decenas de productos de una amplia gama de categorías. Este es el lugar perfecto para inspirarte en tus próximas compras. En esta página encontrarás un total de %actualLeaflets%. Puedes ver cada folleto de las tiendas de %brand% tanto en tu ordenador como en tu dispositivo móvil."
	offersLeaflets: Flyers from the category %category%
	otherShops:
		title: "Otras tiendas"
		text: "¿Has visto ya todos los folletos y las increíbles promociones de esta tienda %brand%? ¡Asegúrate de estar informado haciendo clic en el logotipo de una tienda y comprobando los magníficos productos y promociones que ofrecen!"
	defaultTitleSuffic: '%shopName% - folleto de venta, productos en venta'			
	otherArticles: "Últimos artículos"
	type:
		shopTitle: "{$shopName|upper} cartas{if $currentLeafletFromDate} de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + folleto promocional de la próxima semana"
		eshopTitle: "%brand% descuento"
		eshop: "Consulta las últimas promociones de %brand% en su catálogo lleno de inspiración y ofertas. Los últimos descuentos de %brand% están siempre disponibles, para que nunca te pierdas nada."

tag:
	text: "Oferta de folletos promocionales de la categoría %tag%."
	noLeaflets: "Estamos buscando un folleto promocional para ti... Por favor, inténtelo más tarde."
	otherShops: "Otras tiendas"	
	offers:
		title: %tag% ofertas
		metaDescription: Consulte las ofertas de %tag% en los catálogos de las tiendas más conocidas. No te pierdas los descuentos y precios especiales que se pueden encontrar en los nuevos catálogos.
		titleWithBestOffer: %tag% en oferta desde ⏩ %price% en el folleto actual
		metaDescriptionWithBestOffer: Mire las ofertas de productos %tag% en los folletos de las tiendas más conocidas. No se pierda los descuentos en productos a precios promocionales en los nuevos folletos.
		text: 'Explora todas las promociones de %tag% en <a href="%leafletLink%">folletos</a> y no te pierdas más descuentos en productos seleccionados. Los precios de oferta de %tag% en los folletos actuales se actualizan regularmente y encontrar el <strong>precio más bajo</strong> es muy fácil.'

about:
	title: "Sobre nosotros"
	text: "Nuestro objetivo es ahorrar tiempo y dinero a los usuarios. Todos los días le ofrecemos folletos actualizados de las tiendas más populares y le ahorramos tiempo en la búsqueda de ofertas de productos."
	address: "Adsalva s.r.o.<br>Prague, Czech Republic<br>Na Porici 1067/25, New Town Prague 1<br><br>Tax number CZ03786986"

city:
	city:
		title: "Leaflets in %city%"
		text: "Current promotional leaflets in %city%. In the promotional leaflets in the city of %city%, you will find not only discounted products but also many other discounts. Discounts can be found in stores such as %stores% and more."
		text2: "Promotional leaflets and current discounts in the city of %city%. In the leaflets of popular stores in the city of %city%, you will find not only discounted products but also many other discounts and offers at the best prices. Browse leaflets from the largest stores in the city of %city%, such as %stores%."
		h2: "Leaflets, promotions, and discounts of stores in %city%"
		storesTitle: "Branches in %city%"
		storesMoreButton: "More branches »"
		leafletStores:
			title: "%brand% leaflet"
			store: "%brand% leaflet %city%"
			storeWithCity: "%brand% leaflet %city%"
		otherShops: "Stores in %city%"
		nearestCity: "Other cities with leaflets nearby"
		nearestCityWithShop: "Other cities with %shopName% leaflets nearby"
		categoriesInText: "%category% leaflets"
		citiesInText: "Leaflets in %city%"
		generatedText:
			1: "The city of %city%, with a population of %population%, offers numerous stores, to which we bring new leaflets every week. Interesting promotions and discounts are prepared not only for the city of %city% but also for others nearby, such as the cities of %cities%."
			2: "The list of current leaflets from the largest stores in the city of %city% can be found here:"
			3: "Promotional leaflets from stores such as %stores% and many others are also available. Their promotional offers for the month of %month% can be found in the %shopsLink% section."
			leaflet: "Leaflet from %brand% current %validSince% – %validTill%"
			and: "and"
			or: "or"

	store:
		store: %fullAddress%
		h2: "Stores of %brand% in %city%"
		title: "%brand% %address%, leaflet, and opening hours 🕔"
		description: "Compare offers in leaflets, find the exact address and opening hours, or read about the assortment that awaits you at the %brand% store %address%."
		open: "Open"
		closed: "Closed"
		text: "The %brand% store %address% regularly offers advantageous promotions and discounts on a diverse range of products, where you can take advantage of the popular %brand% leaflet when making purchases."
		h2bottom: "%brand% leaflet %city% %street%"
		text2WithoutStores: "Customers can conveniently view it online, as well as promotions available at other branches."
		text2: "Customers can conveniently view it online, as well as promotions available at branches %stores%."
		or: "or"
		others: "and others"
		textBottom: "The %brand% store %address% offers customers not only a wide range of products but also low prices regularly communicated in the %brand% leaflet. The %fullAddress% branch is a favorite place for those looking for competitively priced offers. Thanks to the online availability of the %brand% leaflet, buyers always have current discounts at hand. If %brand% %address% does not offer everything the customer needs, they can also use other nearby stores, such as:"
		textBottom2: "Find out the exact address, contact the customer service hotline, or opening hours of favorite stores all in one place. It also includes information on which branches are in your area and where additional advantageous offers can be found, as indicated by the promotional leaflets of selected stores."
		sections:
			leaflets: "Other leaflets in the category"
			shops: "Other stores nearby"
			stores: "Other %brand% stores nearby"

	shop:
		title: "%brand% leaflets in %city%"
		metaTitle: "%brand% leaflets in %city%"
		storesTitle: "%brand% branches in %city%"
		h2: "Leaflets, promotions, and discounts at %brand% store in %city%"
		text: "Promotional leaflets from %brand% in %city% and their current discounts and promotions. In the %brand% leaflet %city%, you will find a wide range of products at the best prices. However, in the city of %city%, there is not only %brand%. Among other popular stores are %stores%."
		metaDescription: "Promotional leaflets from %brand% in %city% and their current discounts and promotions. In the %brand% leaflet %city%, you will find a wide range of products at the best prices. However, in the city of %city%, there is not only %brand%. Among other popular stores are %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "Leaflets in %city%"
		shopLink: "Leaflets at %shop%"
		otherShops: "Other stores in %city%"
		shopLeaflet: "%brand% Leaflets"
		citiesInText: "%brand% leaflets in %city%"
		offers: "Offers from %brand% leaflets in %city%"
		generatedText:
			1: "The store %brand% in the city of %city% offers promotional leaflets, which we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% Leaflet in %city%</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains interesting promotional offers and discounts, seasonal promotions, or club prices and a wide range of products."
			3: "However, the store %brand% is not only located in the city of %city%. Leaflets from the store %brand% can also be found in other nearby stores %stores%. All promotional leaflets are available in the <a href=\"%leafletsUrl%\">Leaflets</a> section."
			4: "If you are looking for other stores, popular ones include %stores%."