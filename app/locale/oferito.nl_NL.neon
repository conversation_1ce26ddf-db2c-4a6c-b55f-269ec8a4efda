navbar:
	shops: "Winkels"
	leaflets: "Folders"
	search:
		placeholder: "Winkels zoeken"
		submit: "Zoeken"

	moreShops: "Meer winkels"
	home: "Home"

footer:
	copyright: "Letado Alle rechten voorbehouden."	
	shops: "Winkels"
	category: "Categorieën"	
	aboutLetado: "Over Letado"
	cookies: "Cookies"
	leaflets: "folders"
	aboutUs: "Over ons"
	nextCountries: "Andere landen"

search:
	title: "Zoekresultaten \"%query%\""
	noResults: "Hoe hard we ook zoeken, we kunnen niets vinden."

homepage:
	title: "Laatste folders en goederen in de uitverkoop"
	text: "Nieuwste folders met een breed aanbod aan goederen van grote retailers"
	allLeaflets: "Alle folders"
	shops: "Winkels"
	allShops: "Alle winkels"	

leaflets:
	title: "folders"
	text: "Aanbod van de nieuwste folders. We voegen elke dag folders voor je toe, zodat je altijd speciale producten kunt vinden."

leaflet:
	metaTitle: 'Laatste folder %brand% geldig vanaf %validSince%'
	metaTitlePageSuffix: 'pagina %page%'
	metaDesc: 'De nieuwste folder uit de winkel %brand% geldig vanaf&nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Laatste folder van %leafletBrandLink% geldig van %validSince% tot %validTill%. Op de %leafletPageCount% pagina vind je alle actuele kortingen. Op de Letado pagina vind je altijd actuele informatie over alle folders in de aanbieding van jouw favoriete winkels.."
	longDesc1: "Profiteer van de speciale aanbiedingen van %leafletBrandLink%, die te vinden zijn in de huidige promotiefolder van %validSince% tot %validTill%. Alles wordt steeds duurder tegenwoordig - auto's, vluchten, vakanties, reizen, elektronica, witgoed, maar ook kleding en nog veel meer. Je hoeft echter geen consumentenlening of andere lening af te sluiten voor je gewone maandelijkse uitgaven. Bij Letada streven we ernaar om je zo snel mogelijk kortingen te geven van alle populaire winkels. Zo kun je profiteren van de nieuwste promoties of kortingen en geld besparen op je huishoudbudget."
	longDesc2: "Bij ons hoef je geen financieel adviseur in te huren om je te helpen met je financiën, want dat kunnen wij voor je doen. Het geld dat je overhoudt, kun je gebruiken voor bijvoorbeeld buitenlandse vakanties, uitstapjes naar lokale hotels en pensions, of als financiële buffer voor je volgende hypotheekbetaling."
	longDesc3: "Het is een geweldig gevoel om financieel onafhankelijk te zijn en een overschot aan geld te hebben. Het stelt je ook in staat om je kwaliteitsverzekeringen te veroorloven, of het nu gaat om levensverzekeringen, opstalverzekeringen of verplichte verzekeringen en ongevallenverzekeringen. Dit beschermt je financiën tegen onverwachte invloeden die er een aanzienlijk negatief effect op kunnen hebben. Verzekeringen beschermen dus de stabiliteit van je financiën."	
	longDesc4: "Wij van Letado zullen er alles aan blijven doen om je te helpen zoveel mogelijk geld te besparen op je dagelijkse aankopen, zodat je het je kunt veroorloven om je droomauto, favoriete kleding, elektronica of een goede verzekering te kopen. We hopen dat deze %leafletBrandLink% flyer, geldig van %validSince% tot %validTill%, je in ieder geval een beetje helpt en je dichter bij je dromen brengt!"
	smallTitle: "%brand% geldig vanaf"	
	recommendedLeaflets: "Populaire folders"
	similarLeaflets: "Andere folders %brand%"
	backToLeaflets: "Terug naar de lijst met alle folders"	
	allBrandLeaflets: "Alle folders %brand%"
	goToShop: "Naar de winkel"

shops:
	title: "Winkels"
	text: "Een selectie van de populairste winkeliers van wie we elke dag nieuwe folders brengen."

shop:
	leaflets: "folders"
	text: "Laatste folder %brand% met voordelige aanbiedingen."
	button: "Naar de winkel %brand%"	
	noLeaflets: "We zijn op zoek naar de nieuwste folder... Probeer het later nog eens."
	otherShops: "Andere Winkels."
	defaultTitleSuffic: '%shopName% - nieuwste folder, goederen in de uitverkoop'
	otherLeaflets: "Andere folders %brand%"
	type:
		shopTitle: "{$shopName|upper} folder{if $currentLeafletFromDate} van {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + actiefolder volgende week"
		eshopTitle: "%brand% korting"
		eshop: "Bekijk de laatste %brand% promoties in hun catalogus vol inspiratie en koopjes. De laatste %brand% kortingen zijn altijd beschikbaar, zodat je nooit afgeprijsde artikelen mist."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} folder volgende week van {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + huidige folder"
	     withCurrentLeaflet: "{$shopName|upper} folder volgende week + huidige folder van {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} folder volgende week + huidige promotiefolder online"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} folder volgende week✅ Blader door de {$shopName|upper} folder van volgende week van {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Ook online beschikbaar is de huidige PDF-folder van deze week {$shopName|upper}."
	    withCurrentLeaflet: "{$shopName|upper} folder volgende week ✅ Bekijk de {$shopName|upper} folder voor volgende week. Ook online beschikbaar is de huidige PDF {$shopName|upper} flyer van deze week van {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} folder volgende week ✅ Bekijk de {$shopName|upper} flyer voor volgende week. Ook online beschikbaar is de huidige PDF flyer {$shopName|upper} met de acties van deze week."

tag:
	text: "Aanbod van de nieuwste folders uit de categorie %tag%."
	noLeaflets: "We zijn op zoek naar de nieuwste folder... Probeer het later nog eens."
	otherShops: "Andere winkels"	

about:
	title: "Over ons"
	text: "Ons doel is om gebruikers tijd en geld te besparen. Elke dag brengen we je up-to-date folders van de populairste retailers en besparen we je tijd met het zoeken naar speciale aanbiedingen op producten."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
