navbar:
	shops: "Lojas"
	leaflets: "Folhetos"
	search:
		placeholder: "Busca de lojas"
		submit: "Pesquisa"

	moreShops: "Outras lojas"
	home: "Início"

footer:
	copyright: "Letado Todos os direitos reservados."	
	shops: "Lojas"
	category: "Categorias"	
	aboutLetado: "Sobre Letado"
	cookies: "Cookies"
	leaflets: "Folheto"
	aboutUs: "Sobre nós"
	nextCountries: "Outros países"

search:
	title: "Resultados da pesquisa \"%query%\""
	noResults: "Por mais que procuremos, não conseguimos encontrar nada."

homepage:
	title: "Últimos folhetos e produtos à venda"
	text: "Folhetos mais recentes que oferecem uma ampla variedade de produtos em promoção dos principais varejistas"
	allLeaflets: "Todos os folhetos"
	shops: "Lojas"
	allShops: "Todas as lojas"	

leaflets:
	title: "Folhetos"
	text: "Oferta dos últimos folhetos. Adicionamos folhetos para você todos os dias, para que você sempre encontre produtos especiais."

leaflet:
	metaTitle: 'Folheto mais recente da %brand% válido a partir de %validSince%'
	metaTitlePageSuffix: 'página %page%'
	metaDesc: 'Folheto mais recente da %brand% válido a partir de&nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Último folheto de %leafletBrandLink% válido de %validSince% a %validTill%. Na página %leafletPageCount%, você encontrará todos os descontos atuais. Na página Letado, você sempre encontrará informações atualizadas sobre todos os folhetos em oferta de suas lojas favoritas."
	longDesc1: "Aproveite as ofertas especiais do %leafletBrandLink%, que você encontrará no folheto promocional atual de %validSince% a %validTill%. Tudo está ficando cada vez mais caro hoje em dia - carros, voos, férias, passeios, eletrônicos, produtos da linha branca, mas também roupas e muito mais. No entanto, você não precisa fazer um empréstimo ao consumidor ou outro empréstimo para suas despesas mensais regulares. Na Letada, nós nos esforçamos para trazer descontos de todas as lojas mais populares o mais rápido possível. Assim, você pode aproveitar as últimas promoções ou descontos e economizar dinheiro do seu orçamento doméstico."
	longDesc2: "Conosco, você não precisa contratar um consultor financeiro para ajudá-lo com suas finanças, pois podemos fazer isso por você. Você pode então usar o dinheiro restante para coisas como férias no exterior, viagens a hotéis e pousadas locais ou como uma reserva financeira para o próximo pagamento de sua hipoteca."
	longDesc3: "É uma ótima sensação ser financeiramente independente e ter um excedente de fundos. Isso também significa que você pode pagar um seguro de boa qualidade, seja um seguro de vida, um seguro residencial ou um seguro obrigatório e cobertura contra avarias. Isso protege suas finanças de quaisquer influências inesperadas que possam ter um impacto negativo significativo sobre elas. Portanto, o seguro protege a estabilidade de suas finanças."		
	longDesc4: "Na Letado, continuaremos a fazer tudo o que pudermos para ajudá-lo a economizar o máximo possível em suas compras diárias, para que você possa comprar o carro dos seus sonhos, suas roupas favoritas, eletrônicos ou pagar por um seguro de qualidade. Esperamos que este folheto %leafletBrandLink%, válido de %validSince% a %validTill%, o ajude pelo menos um pouco e que você esteja mais perto de seus sonhos!"
	smallTitle: "%brand% válido a partir de"	
	recommendedLeaflets: "Folhetos populares"
	similarLeaflets: "Outros folhetos %brand%"
	backToLeaflets: "Voltar à lista de todos os folhetos"	
	allBrandLeaflets: "Todos os folhetos %brand%"
	goToShop: "Ir para a loja"

shops:
	title: "Lojas"
	text: "Uma seleção dos varejistas mais populares, cujos novos folhetos trazemos a você todos os dias."

shop:
	leaflets: "folhetos"
	text: "O mais recente folheto da %brand% com ótimas ofertas."
	button: "Ir para a loja %brand%"	
	noLeaflets: "Estamos procurando o último folheto para você... Tente novamente mais tarde."
	otherShops: "Outras lojas"
	defaultTitleSuffic: '%shopName% - folheto mais recente, produtos à venda'
	otherLeaflets: "Outros folhetos %brand%"
	type:
		shopTitle: "{$shopName|upper} folheto{if $currentLeafletFromDate} de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + folheto de ação na próxima semana"
		eshopTitle: "%brand% desconto"
		eshop: "Confira as promoções mais recentes da %brand% em seu catálogo repleto de inspirações e pechinchas. Os descontos mais recentes da %brand% estão sempre disponíveis, para que você nunca perca uma mercadoria com desconto."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} folheto na próxima semana de {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + afolheto atual"
	     withCurrentLeaflet: "{$shopName|upper} folheto na próxima semana + folheto atual de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} folheto para a próxima semana + folheto on-line atual"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} folheto da próxima semana ✅ Navegue pelo especial {$shopName|upper} CARTA DA PRÓXIMA SEMANA de {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'}. Também está disponível on-line o PDF atual do folheto {$shopName|upper} desta semana."
	    withCurrentLeaflet: "{$shopName|upper} folheto da próxima semana ✅ Navegue pelo folheto especial {$shopName|upper} FLYER para a próxima semana. Também está disponível on-line o PDF atual do folheto {$shopName|upper} desta semana de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} lpróxima semana ✅ Navegue pela ação {$shopName|upper} FLYER para a próxima semana. Também está disponível on-line o flyer atual em PDF {$shopName|upper} com as promoções desta semana."

tag:
	text: "Oferta dos últimos folhetos da categoria %tag%."
	noLeaflets: "Estamos procurando o último folheto para você... Por favor, tente novamente mais tarde."
	otherShops: "Outras lojas"	

about:
	title: "Sobre nós"
	text: "Nosso objetivo é economizar tempo e dinheiro dos usuários. Todos os dias, trazemos a você folhetos atualizados dos varejistas mais populares e economizamos seu tempo na busca de ofertas especiais de produtos."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
