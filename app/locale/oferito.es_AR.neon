navbar:
	shops: "Tiendas"
	leaflets: "Folletos"
	search:
		placeholder: "Buscar tiendas"
		submit: "Buscar"

	moreShops: "Otras tiendas"
	home: "Inicio"

footer:
	copyright: "Letado Todos los derechos reservados."	
	shops: "Comercios"
	category: "Categorías"	
	aboutLetado: "Sobre Letado"
	cookies: "Cookies"
	leaflets: "Folletos"
	aboutUs: "Sobre nosotros"
	nextCountries: "Otros países"

search:
	title: "Resultados de la búsqueda \"%query%\""
	noResults: "Por mucho que busquemos, no encontramos nada."

homepage:
	title: "Últimos folletos y productos a la venta"
	text: "Últimos folletos con una amplia gama de productos en oferta de los principales minoristas"
	allLeaflets: "Todos los folletos"
	shops: "Tiendas"
	allShops: "Todas las tiendas"	

leaflets:
	title: "Folletos"
	text: "Oferta de los últimos folletos. Añadimos folletos para usted todos los días, para que siempre pueda encontrar productos especiales."

leaflet:
	metaTitle: 'Último folleto %brand% válido a partir de %validSince%'
	metaTitlePageSuffix: 'página%page%'
	metaDesc: 'Último folleto de %brand% válido a partir de &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%"	
	desc: "Último folleto de %leafletBrandLink% válido desde %validSince% hasta %validTill%. En la página %leafletPageCount% encontrarás todos los descuentos actuales. En la página Letado encontrarás siempre información actualizada sobre todos los folletos en oferta de tus tiendas favoritas."
	longDesc1: "Aproveche las ofertas especiales de %leafletBrandLink%, que encontrará en el folleto promocional actual de %validSince% a %validTill%. Hoy en día todo es cada vez más caro: los coches, los vuelos, las vacaciones, los viajes, la electrónica, los electrodomésticos, pero también la ropa y mucho más. Sin embargo, no necesita pedir un préstamo al consumo u otro tipo de préstamo para sus gastos mensuales habituales. En Letada, nos esforzamos por ofrecerte los descuentos de las tiendas más populares lo antes posible. Así podrás aprovechar las últimas promociones o descuentos y ahorrar dinero de tu presupuesto doméstico."
	longDesc2: "Con nosotros no necesita contratar a un asesor financiero para que le ayude con sus finanzas, porque nosotros lo hacemos por usted. Así podrá utilizar el dinero sobrante para vacaciones en el extranjero, viajes a hoteles y pensiones locales o como colchón financiero para el próximo pago de su hipoteca."
	longDesc3: "Es una gran sensación ser económicamente independiente y tener un excedente de fondos. También significa que puedes permitirte un seguro de buena calidad, ya sea un seguro de vida, un seguro de hogar o un seguro obligatorio y de accidentes. Esto protege tus finanzas de cualquier influencia inesperada que pudiera tener un impacto significativamente negativo en ellas. Por tanto, los seguros protegen la estabilidad de sus finanzas."		
	longDesc4: "En Letado, seguiremos haciendo todo lo posible para ayudarte a ahorrar el máximo dinero posible en tus compras diarias, para que puedas permitirte comprar el coche de tus sueños, tu ropa favorita, aparatos electrónicos o pagar un seguro de calidad. Esperamos que este folleto de %leafletBrandLink%, válido desde %validSince% hasta %validTill%, te ayude al menos un poco y ¡que estés más cerca de tus sueños!"
	smallTitle: "%brand% válido a partir de"	
	recommendedLeaflets: "Folletos populares"
	similarLeaflets: "Otros folletos %brand%"
	backToLeaflets: "Volver a la lista de todos los folletos"	
	allBrandLeaflets: "Todos los folletos %brand%"
	goToShop: "Ir a la tienda"

shops:
	title: "Tiendas"
	text: "Una selección de los comercios más populares cuyos nuevos folletos te traemos cada día."

shop:
	leaflets: "prospectos"
	text: "El último prospecto de %brand% con buenas ofertas."
	button: "Ir a la tienda %brand%"	
	noLeaflets: "Le buscamos el último folleto... Por favor, inténtelo más tarde."
	otherShops: "Otras tiendas"
	defaultTitleSuffic: '%shopName% - último folleto, productos en venta'
	otherLeaflets: "Otros folletos %brand%"
	type:
		shopTitle: "{$shopName|upper} folleto {if $currentLeafletFromDate} a partir de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}{/if} + folleto de acción la próxima semana"
		eshopTitle: "%brand% descuento"
		eshop: "Echa un vistazo a las últimas promociones de %brand% en su catálogo lleno de inspiración y gangas. Los últimos descuentos de %brand% están siempre disponibles, para que nunca te pierdas productos rebajados."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} folleto la próxima semana  {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:'j.n.Y'} + folleto actual"
	     withCurrentLeaflet: "{$shopName|upper} folleto la próxima semana + folleto actual a partir de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper}folleto la próxima semana + folleto promocional actual en línea"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} folleto de la próxima semana ✅ Examine el especial {$shopName|upper} FOLLETO DE LA PRÓXIMA SEMANA de {$nextLeafletFromDate|dayGenitive}. {$nextLeafletFromDate|fecha:'j.n.Y'}. También está disponible en línea el PDF actual del folleto de esta semana de {$shopName|upper}."
	    withCurrentLeaflet: "{$shopName|upper} flyer la semana que viene ✅ Echa un vistazo al especial {$shopName|upper} FLYER de la próxima semana. También está disponible en línea el PDF actual del folleto de {$shopName|upper} de esta semana de {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'j.n.Y'}."
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} folleto la semana que viene ✅ Mira el especial {$shopName|upper} FLYER para la próxima semana. También está disponible online el folleto actual en PDF {$shopName|upper} con las promociones de esta semana."

tag:
	text: "Oferta de los últimos folletos de la categoría %tag%."
	noLeaflets: "Le buscamos el último folleto... Por favor, inténtelo más tarde."
	otherShops: "Otras tiendas"	

about:
	title: "Quiénes somos"
	text: "Nuestro objetivo es ahorrar tiempo y dinero a los usuarios. Cada día te traemos folletos actualizados de los minoristas más populares y te ahorramos tiempo buscando ofertas especiales en productos."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."
