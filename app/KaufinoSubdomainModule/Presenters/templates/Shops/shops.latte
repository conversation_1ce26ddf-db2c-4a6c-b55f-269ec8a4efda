{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block scripts}
    {include parent}
{/block}

{block description}{_kaufino.shops.metaDescription}{/block}
{block title}{_kaufino.shops.metaTitle}{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {var $localizedUrl = $getLocalizedUrl($otherWebsite, 'shops')}
        <link rel="alternate" n:attr="hreflang: $otherWebsite !== $website ? $otherWebsite->getLocalization()->getFullLocale('-') : 'x-default'" href={$localizedUrl} />
    {/foreach}
{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="k__title ta-center mt-5 mb-4">{_kaufino.shops.title}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_kaufino.shops.text}</p>
    </div>

    <div class="k-shop mb-6">    
        {foreach $storeShops as $shop}
            {include '../components/shop-logo.latte', shop => $shop}
        {/foreach}
    </div>

    {if $onlineShops}
        <div class="mb-6">
            <h2 class="k__title ta-center">{_kaufino.shops.otherShops.title}</h2>
        </div>

        <div class="k-shop">    
            {foreach $onlineShops as $shop}
                {include '../components/shop-logo.latte', shop => $shop}
            {/foreach}
        </div>
    {/if}
</div>
