<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Forms\CityPickerControl\CityPickerControl;
use <PERSON><PERSON><PERSON>\Forms\CityPickerControl\CityPickerControlFactory;
use <PERSON><PERSON><PERSON>\Model\Conditions\DocumentFacade;
use Ka<PERSON>ino\Model\Content\ContentGenerator;
use <PERSON><PERSON>ino\Model\EntityManager;
use Ka<PERSON>ino\Model\Geo\Entities\City;
use Ka<PERSON><PERSON>\Model\Geo\GeoFacade;
use Ka<PERSON><PERSON>\Model\GoogleOptimize\GoogleOptimize;
use <PERSON><PERSON><PERSON>\Model\GoogleOptimize\GoogleOptimizeExperiment;
use <PERSON><PERSON><PERSON>\Model\Marketing\MarketingFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoFacade;
use Ka<PERSON><PERSON>\Model\Seo\SeoGenerator;
use Ka<PERSON><PERSON>\Model\Shops\ShopFacade;
use Ka<PERSON><PERSON>\Model\Tags\Entities\Tag;
use Ka<PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Router\RouterFactory;
use Nette\Application\Helpers;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;
use Tracy\Debugger;

abstract class BasePresenter extends \Kaufino\Presenters\BasePresenter
{
    public const COOKIE_CHANNEL_NAME = 'channel-2';

    /** @var string @persistent */
    public ?string $channel = null;

	/** @var EntityManager @inject */
	public $entityManager;

	/** @var SeoGenerator @inject */
	public $seoGenerator;

    /** @var DocumentFacade @inject */
    public $documentFacade;

	/** @var ContentGenerator @inject */
	public $contentGenerator;

	/** @var ITranslator @inject */
	public $translator;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var SeoFacade @inject */
	public $seoFacade;

	/** @var GoogleOptimize @inject */
	public $googleOptimize;

	/** @var GoogleOptimizeExperiment|null */
	protected $googleOptimizeExperiment;

    /** @var GeoFacade @inject */
    public $geoFacade;

    /** @var MarketingFacade @inject */
    public $marketingFacade;

    /** @var CityPickerControlFactory @inject */
    public $cityPickerControlFactory;

	protected function startup()
	{
		parent::startup();

        $domainsWithChannelRequired = ['https://letaky.kaufino.com'];

        if (in_array($this->website->getDomain(), $domainsWithChannelRequired)) {
            $channelFromCookie = $this->getChannelFromCookie();

            if (empty($this->channel) || $this->channel !== $channelFromCookie) {
               $this->redirect('this', ['channel' => $channelFromCookie]);
            }
        }

        $this->website = $this->websiteFacade->resolveCurrentWebsite();

        $this->localization = $this->website->getLocalization();

		if (!$this->localization) {
			# Debugger::log('Region does not exist.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->website->isActive() && !$this->getUser()->isLoggedIn()) {
			# Debugger::log('Website is not active.');
			$this->redirect('this', ['region' => null]);
		}

		if ($this->getParameter('cookie') && $this->getParameter('cookie') === 'wVPkTDuR8QSQXKsU') {
			$this->getHttpResponse()->setCookie('d2s0KZA1rp9pwsRI9n0l', 'Rj1Z53FM17fL6nskc5NG', new \DateTime('+ 1 month'));

			$this->redirect('this');
		}

		if ($this->getUser()->isLoggedIn()) {
			$this->template->userLoggedIn = true; // only for starting session purpose
		}

		$pageExtensionSlug = Strings::substring($this->getHttpRequest()->getUrl()->getPath(), 4);
		$this->template->pageExtension = $this->seoFacade->findPageExtensionBySlug($this->website, $pageExtensionSlug);
		$this->template->pageExtensionSlug = $pageExtensionSlug;

		$this->template->allPageCacheAllowed = false;
		$this->template->canonicalUrl = $this->seoGenerator->generateCanonicalUrl($this->getHttpRequest()->getUrl()->getAbsoluteUrl());
		$this->template->translator = $this->translator;
		$this->template->headerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, false, 10, Website::MODULE_KAUFINO_SUBDOMAIN);
		};
		$this->template->footerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, false, 10, Website::MODULE_KAUFINO_SUBDOMAIN);
		};
		$this->template->footerShopsTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_SHOPS, 11, null, null, true);
		};
		$this->template->footerOffersTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 11);
		};
		$this->template->footerWebsites = function () {
			return $this->websiteFacade->findActiveWebsites(Website::MODULE_KAUFINO);
		};

		$this->template->getLocalizedUrl = function(Website $website, ?string $path = null) {
			return RouterFactory::getLocalizedUrl($website, $path);
		};

        $this->template->channel = $this->channel;

        $this->template->cities = $this->geoFacade->findCities($this->localization, 36);

        $route = str_replace('KaufinoSubdomain:', '', $this->getName()) . ':' . $this->getAction();

        if ($this->channel) {
            $this->template->adUnits = $this->marketingFacade->findAdUnitsByChannelByRoute($this->localization, $this->channel, $route);
        } else {
            $this->template->adUnits = [];
        }

        $this->template->conditions = $this->documentFacade->findByLocalization($this->localization);

		$this->initGoogleOptimize();
	}

	protected function initGoogleOptimize() {
		$this->googleOptimizeExperiment = new GoogleOptimizeExperiment('UsGY6n_FQF6PF45uD1Cq6w', 3);

		$group = $this->googleOptimize->getVariant('group', 3);
		$this->googleOptimizeExperiment->setVariant($group % $this->googleOptimizeExperiment->getCountOfVariants());

		$this->template->googleOptimizeExperiment = $this->googleOptimizeExperiment;

		Debugger::barDump('experimentId: ' . $this->googleOptimizeExperiment->getExperimentId() . ';group: ' . $group . ';variant: ' . $this->googleOptimizeExperiment->getVariant());
	}

	protected function getGoogleOptimizeVariant() {
		if (!$this->googleOptimizeExperiment) {
			return null;
		}

		return $this->googleOptimizeExperiment->getVariant();
	}

    public function getChannelFromCookie()
    {
        $cookieChannel = $this->getHttpRequest()->getCookie(self::COOKIE_CHANNEL_NAME);

        if ($cookieChannel === null) {
            $channels = [
                'k' => 50,
                'l' => 10,
                'm' => 30,
                'n' => 10,
            ];

            $totalWeight = array_sum($channels);
            $rand = random_int(1, $totalWeight);

            $runningWeight = 0;
            foreach ($channels as $channel => $weight) {
                $runningWeight += $weight;
                if ($rand <= $runningWeight) {
                    $cookieChannel = $channel;
                    break;
                }
            }

            Debugger::log($cookieChannel, 'channels');

            $this->getHttpResponse()->setCookie(self::COOKIE_CHANNEL_NAME, $cookieChannel, new \DateTime('+ 5 years'));
        }

        return $cookieChannel;
    }

    public function formatTemplateFiles(): array
    {
        $result = [];

        if ($this->channel) {
            [, $presenter] = Helpers::splitName($this->getName());
            $dir = dirname(static::getReflection()->getFileName());
            $dir = is_dir("$dir/templates") ? $dir : dirname($dir);

            $result[] = "$dir/templates/$presenter/$this->view-$this->channel.latte";
        }

        $result = array_merge($result, parent::formatTemplateFiles());

        return $result;
    }

    protected function createComponentCityPickerControl(): CityPickerControl
    {
        $control = $this->cityPickerControlFactory->create($this->localization, Website::MODULE_KAUFINO);

        $control->onSuccess[] = function (City $city) {
            $this->redirect('this');
        };

        return $control;
    }
}
