<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoSubdomainModule\Presenters;

use Ka<PERSON>ino\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Azure\AzureClient;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class HomepagePresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

    /** @var OfferFacade @inject */
    public $offerFacade;

    /** @var ArticleFacade @inject */
    public $articleFacade;

	public function renderDefault(): void
	{
		$this->responseCacheTags[] = 'shops';
		$this->responseCacheTags[] = 'leaflets';

		$this->template->leaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 10, Website::MODULE_KAUFINO);
		$this->template->shops = $this->shopFacade->findTopLeafletShops($this->localization, true, 18, Website::MODULE_KAUFINO_SUBDOMAIN);

        $offerTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 36);

		$this->template->offersTags = $offerTags;
        $this->template->topOffers =  $this->offerFacade->findTopOffersByTags($this->localization, $offerTags, 3, 15);

        $this->template->articles = $this->articleFacade->findArticles($this->website, 4);
	}

	public function actionOther($other): void
	{
		$this->redirectPermanent('Homepage:default');
	}
}
