<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Shops\ContentBlockFacade;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Kaufino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class TagPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

    /** @var GeoFacade @inject */
    public $geoFacade;

    /** @var ContentBlockFacade @inject */
    public $contentBlockFacade;

	public function actionTag(Tag $tag): void
	{
        if ($tag->isActive() === false && ($tag->getActiveTill() === null || $tag->getActiveTill() < new \DateTime)) {
            $this->redirectPermanent("Homepage:default");
        }

		if ($tag->isShopsType()) {
			$this->prepareTagShops($tag);
		}

		if ($tag->isOffersType()) {
			$this->prepareTagOffers($tag);
		}

		$this->responseCacheTags[] = 'tag/' . $tag->getId();

		$this->template->tag = $tag;
	}

	private function prepareTagShops(Tag $tag): void
	{
		$this->setView('tagShops');

        $shops = $this->shopFacade->findLeafletShopsByTag($tag, true, 100, Website::MODULE_KAUFINO_SUBDOMAIN);

        $leaflets = $this->leafletFacade->findLeafletsByTag($tag, false, 60, Website::MODULE_KAUFINO_SUBDOMAIN);

        if (count($leaflets) < 60) {
            $leaflets = array_merge($leaflets, $this->leafletFacade->findLeafletsByShops($shops, 60, true, $leaflets, Website::MODULE_KAUFINO_SUBDOMAIN));
        }

        $this->template->leaflets = array_slice($leaflets, 0, 60);
        $this->template->shops = $shops;

        $this->template->cities = $shops ? $this->geoFacade->findCitiesByShops($shops, 100) : [];
	}

	private function prepareTagOffers(Tag $tag): void
	{
		$this->setView('tagOffers');

        $offers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), true, null, true, Website::MODULE_KAUFINO_SUBDOMAIN);
        $expiredOffers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), false, 10, true, Website::MODULE_KAUFINO_SUBDOMAIN);

        $leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($tag->getMatchRule(), $tag->getLocalization(), Website::MODULE_KAUFINO_SUBDOMAIN, 25, $tag);
		$this->template->leafletPages = $leafletPages;

		$this->template->offers = $offers;
		$this->template->expiredOffers = $expiredOffers;
		$this->template->childTags = $this->tagFacade->findTagsbyTag($tag, 100);

		$this->template->bestOffer = $this->findBestOffer($offers);
		$this->template->bestExpiredOffer = $this->findBestOffer($expiredOffers);

		$this->template->frequentOfferShops = $this->getMostFrequentShops($offers);
		$this->template->frequentExpiredOfferShops = $this->getMostFrequentShops($expiredOffers);

        $this->template->name = $tag->getName();

        $contentBlocks = $this->contentBlockFacade->findContentBlocksByTag($tag, Website::MODULE_KAUFINO);
        $this->template->contentBlocks = $contentBlocks;
	}

	private function findBestOffer($offers): ?Offer
	{
		$bestOffer = null;
		foreach ($offers as $offer) {
            if ($offer->getCurrentPrice() === 0 || $offer->getCurrentPrice() === null) {
                continue;
            }

			if ($bestOffer === null || $offer->getCurrentPrice() < $bestOffer->getCurrentPrice()) {
				$bestOffer = $offer;
			}
		}

		return $bestOffer;
	}

    private function getMostFrequentShops(array $entities): array
    {
        foreach ($entities as $entity) {
            $shops[$entity->getShop()->getId()] = $entity->getShop();
        }

        return $shops ?? [];
    }
}
