<?php

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Nette\Localization\ITranslator;

class DayGenitiveFilter
{
	/**
	 * @var ITranslator
	 */
	private $translator;

	public function __construct(ITranslator $translator)
	{
		$this->translator = $translator;
	}

	public function __invoke($date)
	{
		return $this->translator->translate(sprintf('app.day.%s.genitive', $date->format('N')));
	}
}
