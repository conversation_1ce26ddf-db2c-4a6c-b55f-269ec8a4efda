<?php

namespace <PERSON><PERSON><PERSON>\Model\Conditions;

use <PERSON><PERSON><PERSON>\Model\Conditions\Repositories\DocumentRepository;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class DocumentFacade
{
    private DocumentRepository $documentRepository;

    public function __construct(DocumentRepository $documentRepository)
    {
        $this->documentRepository = $documentRepository;
    }

    public function findByLocalization(Localization $localization): ?Entities\Document
    {
        return $this->documentRepository->findByLocalization($localization);
    }

    public function findBySlug(Localization $localization, string $slug)
    {
        return $this->documentRepository->findBySlug($localization, $slug);
    }
}
