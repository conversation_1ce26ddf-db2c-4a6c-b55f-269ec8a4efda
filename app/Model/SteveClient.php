<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Nette\SmartObject;
use Nette\Utils\Json;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Nette\Utils\JsonException;
use Psr\Http\Message\ResponseInterface;

class SteveClient
{
	use SmartObject;

	/** @var Configuration */
	private $configuration;

	/** @var EntityManager */
	private $em;

	public function __construct(Configuration $configuration, EntityManager $em)
	{
		$this->configuration = $configuration;
		$this->em = $em;
	}

	public function getLeaflets(Localization $localization)
	{
		$region = $localization->getRegion();

		if ($region === 'cz') {
			$region = 'cs';
		}

		$response = $this->sendRequest('/leaflets2', $region);

		return Json::decode($response->getBody()->getContents());
	}

	public function getNewsletters(Localization $localization)
	{
		$response = $this->sendRequest('/leaflets2', $localization->getLocale(), [
			'type' => 'newsletter',
		]);

		return Json::decode($response->getBody()->getContents());
	}

	/**
	 * @return mixed
	 * @throws GuzzleException
	 * @throws JsonException
	 */
	public function getShops()
	{
		$response = $this->sendRequest('/shops', null);

		//dump($response->getStatusCode()); // 200
		//dump((string) $response->getBody()); // '{"id": 1420053, "name": "guzzle", ...}'

		return Json::decode((string)$response->getBody()->getContents());
	}

	public function getStores(?string $locale = null)
	{
		$response = $this->sendRequest('/stores', $locale);

		//dump($response->getStatusCode()); // 200
		//dump((string) $response->getBody()); // '{"id": 1420053, "name": "guzzle", ...}'

		return Json::decode((string)$response->getBody()->getContents());
	}

	public function getCities()
	{
		$response = $this->sendRequest('/cities', null);

		//dump($response->getStatusCode()); // 200
		//dump((string) $response->getBody()); // '{"id": 1420053, "name": "guzzle", ...}'

		return Json::decode((string)$response->getBody()->getContents());
	}

	public function getDeals(Localization $localization)
	{
		$response = $this->sendRequest('/deals', $localization->getLocale());

		return Json::decode($response->getBody()->getContents());
	}

	public function getProducts(Localization $localization)
	{
		$response = $this->sendRequest('/products', $localization->getLocale());

		return Json::decode($response->getBody()->getContents());
	}

	/**
	 * @throws GuzzleException
	 */
	private function sendRequest(string $path, ?string $locale = null, array $params = []): ResponseInterface
	{
		$query = [
			'token' => $this->configuration->getSteveApiToken(),
			'locale' => $locale,
		];

		$client = new Client(['verify' => !$this->configuration->isDevelopmentMode()]);

		$response = $client->request(
			'GET',
			$this->configuration->getSteveApiUrl() . $path,
			['query' => array_merge($query, $params)]
		);

		//dump($response); // 200
		//dump($response->getStatusCode()); // 200
		//dump((string) $response->getBody()->getContents()); // '{"id": 1420053, "name": "guzzle", ...}'

		return $response;
	}
}
