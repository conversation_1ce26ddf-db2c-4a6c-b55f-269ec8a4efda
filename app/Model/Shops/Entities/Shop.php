<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Model\Shops\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use <PERSON><PERSON>ino\Model\Geo\Entities\City;
use Kaufino\Model\Leaflets\Entities\Leaflet;
use Kaufino\Model\Localization\Entities\Localization;
use Kaufino\Model\Offers\Entities\Offer;
use Kaufino\Model\Tags\Entities\Tag;
use Nette\Utils\Strings;
use Tracy\Debugger;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Shops\Repositories\ShopRepository")
 * @ORM\Table(name="kaufino_shops_shop", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="shop_unique", columns={"localization_id", "slug"})}
 * )
 */
class Shop
{
    public const TYPE_STORE = 'store';
    public const TYPE_ESHOP = 'eshop';
    public const TYPE_SERVICE = 'service';

    /**
     * @var int
     * @ORM\Column(type="integer", nullable=FALSE)
     * @ORM\Id
     * @ORM\GeneratedValue
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
     * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
     */
    protected $localization;

    /**
     * @ORM\OneToMany(targetEntity="\Kaufino\Model\Leaflets\Entities\Leaflet", mappedBy="shop")
     */
    protected $leaflets;

    /**
     * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Geo\Entities\City", inversedBy="shops")
     * @ORM\JoinTable(name="kaufino_shops_shop_city")
     */
    protected $cities;

    /**
     * @ORM\ManyToMany(targetEntity="Kaufino\Model\Tags\Entities\Tag")
     * @ORM\JoinTable(name="kaufino_shops_shop_tag",
     *      joinColumns={@ORM\JoinColumn(name="shop_id", referencedColumnName="id")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="tag_id", referencedColumnName="id")}
     *      )
     * @var ArrayCollection|PersistentCollection
     */
    private $tags;

    /**
     * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Tags\Entities\Tag")
     * @ORM\JoinColumn(name="tag_id", referencedColumnName="id")
     */
    protected $tag;

    /**
     * @ORM\Column(type="integer", unique=true, nullable=true)
     */
    protected $shopId;

    /**
     * @ORM\Column(type="string", nullable=false)
     */
    protected $name;

    /**
     * @ORM\Column(type="string", nullable=false)
     */
    protected $slug;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $type;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $logoUrl;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $domain;

    /**
     * @ORM\Column(type="string", length=1024, nullable=true)
     */
    protected $alternativeNames;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    protected $description;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    protected $heading;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    protected $title;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $reward;

    /**
     * @ORM\Column(type="boolean")
     */
    protected bool $useOcr = false;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected $boost = 0;

    /**
     * @ORM\Column(type="integer", nullable=false)
     */
    protected $boostCoupons = 0;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected $priorityLeaflets;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected $priorityCoupons;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $activeCities = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private $activeLeaflets = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private $activeCoupons = false;

    /**
     * @ORM\Column(type="boolean")
     */
    protected $activeKaufino = false;

    /**
     * @ORM\Column(type="boolean")
     */
    protected $activeLetado = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private $activeOferto = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private $activeOfertoCom = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private $useLeafletTemplateForNewsletters = false;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $updatedSitemapAt;

    /**
     * @ORM\Column(type="boolean")
     */
    protected bool $hidden = false;

    /**
     * @ORM\Column(type="datetime")
     */
    private $createdAt;

    /**
     * @ORM\OneToMany(targetEntity="ContentBlock", mappedBy="shop", cascade={"persist"})
     * @var ContentBlock[]|ArrayCollection|PersistentCollection
     */
    private $contentBlocks;

    /**
     * @ORM\OneToMany(targetEntity="Kaufino\Model\Offers\Entities\Offer", mappedBy="shop", cascade={"persist"})
     * @var Offer[]|ArrayCollection|PersistentCollection
     */
    private $offers;

    /**
     * @ORM\OneToMany(targetEntity="Kaufino\Model\Shops\Entities\Store", mappedBy="shop", cascade={"persist"})
     * @var Store[]|ArrayCollection|PersistentCollection
     */
    private $stores;

    /**
     * @ORM\OneToMany(targetEntity="Kaufino\Model\Shops\Entities\Review", mappedBy="shop", cascade={"persist"})
     * @var Review[]|ArrayCollection|PersistentCollection
     */
    private $reviews;

    /**
     * @ORM\ManyToMany(targetEntity="Kaufino\Model\Tags\Entities\Tag")
     * @ORM\JoinTable(name="kaufino_shops_shop_label",
     *      joinColumns={@ORM\JoinColumn(name="shop_id", referencedColumnName="id")},
     *      inverseJoinColumns={@ORM\JoinColumn(name="tag_id", referencedColumnName="id")}
     *      )
     * @var ArrayCollection|PersistentCollection
     */
    private $labels;

    public function __construct(Localization $localization, string $name, string $slug, int $shopId = null, string $logoUrl = null, string $domain = null, ?string $description = null, int $priorityLeaflets = 0, int $priorityCoupons = 0)
    {
        $this->localization = $localization;
        $this->name = $name;
        $this->slug = $slug;
        $this->shopId = $shopId;
        $this->logoUrl = $logoUrl;
        $this->domain = $domain;
        $this->description = $description;
        $this->priorityLeaflets = $priorityLeaflets;
        $this->priorityCoupons = $priorityCoupons;
        $this->updatedSitemapAt = new DateTime();
        $this->createdAt = new DateTime();

        $this->reviews = new ArrayCollection();
        $this->cities = new ArrayCollection();
        $this->contentBlocks = new ArrayCollection();
        $this->offers = new ArrayCollection();
        $this->stores = new ArrayCollection();
        $this->tags = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getLocalization(): Localization
    {
        return $this->localization;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName($name): void
    {
        $this->name = $name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getShopId(): ?int
    {
        return $this->shopId;
    }

    public function setShopId($shopId): void
    {
        $this->shopId = $shopId;
    }

    public function getLogoUrl(): ?string
    {
        return $this->logoUrl;
    }

    public function setLogoUrl(string $logoUrl): void
    {
        $this->logoUrl = $logoUrl;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setDomain($domain): void
    {
        $this->domain = $domain;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    /**
     * @return Tag
     */
    public function getTag(): ?Tag
    {
        return $this->tag;
    }

    /**
     * @param Tag $tag
     */
    public function setTag(Tag $tag): void
    {
        $this->tag = $tag;
    }

    /**
     * @return bool
     */
    public function isActiveLeaflets(): bool
    {
        return $this->activeLeaflets;
    }

    public function isActive(): bool
    {
        return $this->isActiveLeaflets();
    }

    /**
     * @return bool
     */
    public function isStore(): bool
    {
        return $this->type == self::TYPE_STORE;
    }

    /**
     * @return bool
     */
    public function isEshop(): bool
    {
        return $this->type == self::TYPE_ESHOP;
    }

    /**
     * @return bool
     */
    public function isService(): bool
    {
        return $this->type == self::TYPE_STORE;
    }

    /**
     * @param bool $active
     */
    public function setActiveLeaflets(bool $active): void
    {
        $this->activeLeaflets = $active;
    }

    /**
     * @param mixed $priorityLeaflets
     */
    public function setPriorityLeaflets($priorityLeaflets): void
    {
        $this->priorityLeaflets = $priorityLeaflets;
    }

    /**
     * @param mixed $priorityCoupons
     */
    public function setPriorityCoupons($priorityCoupons): void
    {
        $this->priorityCoupons = $priorityCoupons;
    }

    /**
     * @param bool $activeCoupons
     */
    public function setActiveCoupons(bool $activeCoupons): void
    {
        $this->activeCoupons = $activeCoupons;
    }

    /**
     * @return string
     */
    public function getHeading(): ?string
    {
        return $this->heading;
    }

    /**
     * @param string $heading
     */
    public function setHeading($heading): void
    {
        $this->heading = $heading;
    }

    /**
     * @return string
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle($title): void
    {
        $this->title = $title;
    }

    public static function getTypes(): array
    {
        return [
            self::TYPE_STORE => 'Store',
            self::TYPE_ESHOP => 'Eshop',
            self::TYPE_SERVICE => 'Service',
        ];
    }

    public function getCurrentAndFutureLeaflets(): ?ArrayCollection
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->gte('validTill', new DateTime()))
            ->andWhere(Criteria::expr()->eq('type', 'leaflet'))
            ->andWhere(Criteria::expr()->eq('archivedAt', null))
            ->andWhere(Criteria::expr()->eq('deletedAt', null))
            ->orderBy(['priority' => 'DESC'])
            ->orderBy(['validSince' => 'DESC']);

        return $this->leaflets->matching($criteria);
    }

    public function getCurrentAndFutureNewsletter(): ?ArrayCollection
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->gte('validTill', new DateTime()))
            ->andWhere(Criteria::expr()->eq('type', 'newsletter'))
            ->orderBy(['priority' => 'DESC'])
            ->orderBy(['validSince' => 'DESC']);

        return $this->leaflets->matching($criteria);
    }

    public function getExpiredLeaflets(): ?ArrayCollection
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->lte('validTill', new DateTime()))
            ->andWhere(Criteria::expr()->eq('type', 'leaflet'))
            ->orderBy(['priority' => 'DESC'])
            ->orderBy(['validSince' => 'DESC']);

        return $this->leaflets->matching($criteria);
    }

    public function getExpiredNewsletters(): ?ArrayCollection
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->lte('validTill', new DateTime()))
            ->andWhere(Criteria::expr()->eq('type', 'newsletter'))
            ->orderBy(['priority' => 'DESC'])
            ->orderBy(['validSince' => 'DESC']);

        return $this->leaflets->matching($criteria);
    }

    public function getCurrentLeaflet(): ?Leaflet
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->gte('validTill', new DateTime()))
            ->andWhere(Criteria::expr()->lte('validSince', new DateTime()))
            ->orderBy(['priority' => 'DESC'])
            ->orderBy(['validSince' => 'DESC']);

        $leaflets = $this->leaflets->matching($criteria);

        if (count($leaflets) == 0) {
            return null;
        }

        return $leaflets[0];
    }

    public function getNextLeaflet(): ?Leaflet
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->gte('validSince', new DateTime()))
            ->orderBy(['priority' => 'DESC'])
            ->orderBy(['validSince' => 'DESC']);

        $leaflets = $this->leaflets->matching($criteria);

        if (count($leaflets) == 0) {
            return null;
        }

        return $leaflets[0];
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     */
    public function setType($type): void
    {
        $this->type = $type;
    }

    /**
     * @param string $slug
     */
    public function setSlug(string $slug): void
    {
        $this->slug = $slug;
    }

    public function setUseOcr(bool $useOcr)
    {
        $this->useOcr = $useOcr;
    }

    public function isUseOcr(): bool
    {
        return $this->useOcr;
    }

    public function getUpdatedSitemapAt()
    {
        return $this->updatedSitemapAt;
    }

    public function updateSitemap(): void
    {
        $this->updatedSitemapAt = new \DateTime();
    }

    public function setCities(array $cities)
    {
        $this->cities->clear();
        foreach ($cities as $city) {
            $this->cities->add($city);
        }
    }

    public function addCity(City $city)
    {
        return $this->cities->add($city);
    }

    public function addTag(Tag $tag)
    {
        if ($this->tags->contains($tag) === false) {
            return $this->tags->add($tag);
        }
    }

    public function getTags()
    {
        return $this->tags;
    }

    public function removeTag(Tag $tag)
    {
        $this->tags->removeElement($tag);
    }

	public function removeCity(City $city)
	{
		$this->cities->removeElement($city);
	}

	public function hasCity(City $city): bool
	{
		return $this->cities->contains($city);
	}

	public function getCities()
	{
		return $this->cities;
	}

	/**
	 * @return mixed
	 */
	public function getBoost()
	{
		return $this->boost;
	}

	/**
	 * @param mixed $boost
	 */
	public function setBoost($boost): void
	{
		$this->boost = $boost;
	}

	/**
	 * @return int
	 */
	public function getBoostCoupons(): int
	{
		return $this->boostCoupons;
	}

	/**
	 * @param int $boostCoupons
	 */
	public function setBoostCoupons(int $boostCoupons): void
	{
		$this->boostCoupons = $boostCoupons;
	}

	/**
	 * @return bool
	 */
	public function isActiveOferto(): bool
	{
		return $this->activeOferto;
	}

    public function isActiveOfertoCom(): bool
    {
        return $this->activeOfertoCom;
    }

	/**
	 * @param bool $activeOferto
	 */
	public function setActiveOferto(bool $activeOferto): void
	{
		$this->activeOferto = $activeOferto;
	}

    public function setActiveOfertoCom(bool $activeOferto): void
    {
        $this->activeOfertoCom = $activeOferto;
    }

	/**
	 * @return mixed
	 */
	public function getAlternativeNames()
	{
		return $this->alternativeNames;
	}

	/**
	 * @param mixed $alternativeNames
	 */
	public function setAlternativeNames($alternativeNames): void
	{
		$this->alternativeNames = $alternativeNames;
	}

	/**
	 * @return bool
	 */
	public function hasAlternativeNames()
	{
		return $this->alternativeNames && Strings::length(trim($this->alternativeNames)) > 0;
	}

	/**
	 * @return mixed
	 */
	public function getAlternativeName(int $offset)
	{
		if (!$this->hasAlternativeNames()) {
			return null;
		}

		$names = explode("\n", trim($this->alternativeNames));

		if (count($names) == 0) {
			return null;
		}

		$adjustedOffset = $offset % count($names);

		return $names[$adjustedOffset];
	}

	/**
	 * @return ArrayCollection|PersistentCollection|ContentBlock[]
	 */
	public function getContentBlocks()
	{
		return $this->contentBlocks;
	}

	/**
	 * @return mixed
	 */
	public function getPriorityLeaflets()
	{
		return $this->priorityLeaflets;
	}

	/**
	 * @return mixed
	 */
	public function getPriorityCoupons()
	{
		return $this->priorityCoupons;
	}

	/**
	 * @return bool
	 */
	public function isActiveCoupons(): bool
	{
		return $this->activeCoupons;
	}

	/**
	 * @return ArrayCollection|PersistentCollection
	 */
	public function getLabels()
	{
		return $this->labels;
	}

	public function addLabel(Tag $label)
	{
		if ($this->labels->contains($label) === false) {
			$this->labels->add($label);
		}
	}

	public function removeLabel(Tag $label)
	{
		$this->labels->removeElement($label);
	}

    public function getReward(): ?string
    {
        return $this->reward;
    }

    public function setReward(?string $reward): void
    {
        $this->reward = $reward;
    }

    public function getStores()
    {
        return $this->stores;
    }

    public function setUseLeafletTemplateForNewsletters(): bool
    {
        return $this->useLeafletTemplateForNewsletters = true;
    }

    public function unsetUseLeafletTemplateForNewsletters(): bool
    {
        return $this->useLeafletTemplateForNewsletters = false;
    }

    public function useLeafletTemplateForNewsletters(): bool
    {
        return $this->useLeafletTemplateForNewsletters;
    }

    public function isActiveKaufino(): bool
    {
        return $this->activeKaufino;
    }

    public function setActiveKaufino(bool $activeKaufino): void
    {
        $this->activeKaufino = $activeKaufino;
    }

    public function isActiveLetado(): bool
    {
        return $this->activeLetado;
    }

    public function setActiveLetado(bool $activeLetado): void
    {
        $this->activeLetado = $activeLetado;
    }

    public function hasActiveCities(): bool
    {
        return $this->activeCities;
    }

    public function setActiveCities(bool $activeCities): void
    {
        $this->activeCities = $activeCities;
    }

    public function isHidden(): bool
    {
        return $this->hidden;
    }
}
