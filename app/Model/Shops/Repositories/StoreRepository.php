<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use Kaufino\Model\Geo\Entities\City;
use Kaufino\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Kaufino\Model\Websites\Entities\Website;

class StoreRepository extends EntityRepository
{
    public function getStores(bool $onlyActive = false): QueryBuilder
    {
        $qb = $this->createQueryBuilder('s')
            ->leftJoin('s.shop', 'shop')
            ->leftJoin('s.city', 'city')
        ;

        if ($onlyActive === true) {
            $qb->andWhere('s.active = 1');
        }

        return $qb;
    }

    public function findByStoreId(int $storeId): ?Store
    {
        return $this->getStores()
            ->andWhere('s.storeId = :storeId')
            ->setParameter('storeId', $storeId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findBySlug(Localization $localization, string $slug): ?Store
    {
        return $this->getStores()
            ->andWhere('s.slug = :slug')
            ->andWhere('s.localization = :localization')
            ->setParameter('slug', $slug)
            ->setParameter('localization', $localization)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findNearestStores(Store $store, int $limit, bool $sameShop = true, string $websiteType = Website::MODULE_KAUFINO): array
    {
        $query = $this->getEntityManager()->getConnection()->executeQuery(
            sprintf("SELECT id, name, lat, lng, SQRT(
                        POW(69.1 * (lat - %s), 2) +
                        POW(69.1 * (%s - lng) * COS(lat/ 57.3), 2)) AS distance
                    FROM kaufino_shops_store
                    WHERE localization_id = %d AND id != %d AND lat IS NOT NULL AND lng IS NOT NULL 
                    ". ($sameShop === true ? "AND shop_id = " . $store->getShop()->getId() : "") ."
                    AND " . ($websiteType === Website::MODULE_OFERTO ? "active_oferto = 1" : "active = 1") . "
                    HAVING distance < %s ORDER BY distance LIMIT %s", $store->getLat(), $store->getLng(), $store->getLocalization()->getId(), $store->getId(), 25, $limit)
        );

        $storeIds = [];
        foreach ($query->fetchAllAssociative() as $result) {
            $storeIds[] = $result['id'];
        }

        $qb = $this->getStores()
            ->andWhere('s.id IN (:storeIds)')
            ->setParameter('storeIds', $storeIds)
        ;

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('shop.hidden = 0');
        }

        $stores = $qb->getQuery()->getResult();

        usort($stores, function ($a, $b) use ($storeIds) {
            $posA = array_search($a->getId(), $storeIds);
            $posB = array_search($b->getId(), $storeIds);
            return $posA - $posB;
        });

        return $stores;
    }

    public function findStoresByCity(City $city, int $limit, string $websiteType = Website::MODULE_KAUFINO) {
        $qb = $this->getStores(true)
            ->andWhere('s.city = :city')
            ->setParameter('city', $city)
            ->addOrderBy('s.zipCode', 'ASC')
        ;

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('shop.activeKaufino = 1');
        } elseif ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('shop.activeOferto = 1');
        }

        return $qb->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findStoresByShopAndCity(Shop $shop, City $city, int $limit = 10, string $websiteType = Website::MODULE_KAUFINO)
    {
        $qb = $this->getStores()
            ->andWhere('s.shop = :shop')
            ->andWhere('s.city = :city')
            ->setParameter('shop', $shop)
            ->setParameter('city', $city)
        ;

        if ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
            $qb->andWhere('shop.activeKaufino = 1')
                ->andWhere('s.active = 1');
            ;
        } elseif ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('shop.activeOferto = 1')
                ->andWhere('s.activeOferto = 1');
            ;
        }

        return $qb->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findStoresByShop(Shop $shop, ?int $limit = 10, ?string $websiteType = null)
    {
        $qb = $this->getStores(true)
            ->andWhere('s.shop = :shop')
            ->setParameter('shop', $shop)
        ;

        if ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('shop.activeKaufino = 1');
        } elseif ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('shop.activeOferto = 1');
        }

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()
        ->getResult();
    }

    public function findStoreBySlug(City $city, Shop $shop, string $slug, ?string $websiteType = null): ?Store
    {
        $qb = $this->getStores(false)
            ->andWhere('s.shop = :shop')
            ->andWhere('s.city = :city')
            ->andWhere('s.slug = :slug')
            ->setParameter('shop', $shop)
            ->setParameter('city', $city)
            ->setParameter('slug', $slug)
        ;

        if ($websiteType === Website::MODULE_KAUFINO) {
          //  $qb->andWhere('s.active = 1');
        } elseif ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = 1');
        }

        return $qb->getQuery()
            ->getOneOrNullResult();
    }

    public function findStoresToActivate(Localization $localization, int $limit)
    {
        return $this->getStores()
            ->andWhere('s.active = 0')
            ->andWhere('city.activeBrandsKaufino = true')
            ->andWhere('s.localization = :localization')
            ->setParameter('localization', $localization)
            ->addOrderBy('shop.priorityLeaflets', 'DESC')
            ->addOrderBy('city.population', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findStoresToActivateOferto(Localization $localization, int $limit)
    {
        return $this->getStores()
            ->andWhere('s.activeOferto = 0')
            ->andWhere('city.activeBrandsOferto = true')
            ->andWhere('s.localization = :localization')
            ->setParameter('localization', $localization)
            ->addOrderBy('shop.priorityLeaflets', 'DESC')
            ->addOrderBy('city.population', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findStores(Localization $localization, ?int $limit = 10, ?string $websiteType = null)
    {
        $qb = $this->getStores()
            ->andWhere('s.localization = :localization')
            ->setParameter('localization', $localization)
        ;

        if ($websiteType === Website::MODULE_OFERTO) {
            $qb->andWhere('s.activeOferto = true');
        } elseif ($websiteType === Website::MODULE_KAUFINO) {
            $qb->andWhere('s.active = true');
        }

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }
}