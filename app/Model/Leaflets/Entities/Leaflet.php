<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Leaflets\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Nette\Utils\Json;
use Nette\Utils\JsonException;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Leaflets\Repositories\LeafletRepository")
 * @ORM\Table(name="kaufino_leaflets_leaflet", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="leaflet_unique", columns={"localization_id", "slug"})}
 * )
 */
class Leaflet
{
	public const TYPE_LEAFLET = 'leaflet';
	public const TYPE_NEWSLETTER = 'newsletter';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Shops\Entities\Shop", inversedBy="leaflets")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	protected $shop;

	/**
	 * @ORM\OneToMany(targetEntity="\Kaufino\Model\Leaflets\Entities\LeafletPage", mappedBy="leaflet")
	 * @var ArrayCollection
	 */
	private $pages;

	/**
	 * @ORM\Column(type="integer", unique=true, nullable=true)
	 */
	protected $leafletId;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    protected $offeristaBrochureId;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $offeristaTrackingBugs;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", length=25, nullable=false)
	 */
	protected $type;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="string", length=250, nullable=true)
	 */
	protected $subject;

	/**
	 * @ORM\Column(type="string", length=250, nullable=true)
	 */
	protected $partnerLink;

	/**
	 * @ORM\Column(type="boolean", name="is_primary")
	 */
	private $primary = true;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $top = false;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $isOfferistaPromo = false;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $priority = 0;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $checked = false;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $deletedAt;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private $countOfArchivedPages = 0;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $archivedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $archiveAt;

    /**
     * @ORM\Column(type="datetime")
     */
    private $updatedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

    /**
     * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Geo\Entities\City", inversedBy="leaflets")
     * @ORM\JoinTable(name="kaufino_leaflets_leaflet_city")
     */
    protected $cities;

	public function __construct(Localization $localization, Shop $shop, $leafletId, $name, $type, $slug, $subject, $primary, DateTime $validSince, DateTime $validTill)
	{
		$this->localization = $localization;
		$this->shop = $shop;
		$this->leafletId = $leafletId;
		$this->pages = new ArrayCollection();
		$this->cities = new ArrayCollection();
		$this->name = $name;
		$this->type = $type;
		$this->slug = $slug;
		$this->subject = $subject;
		$this->primary = $primary;
		$this->validSince = $validSince;
		$this->validTill = $validTill;
		$this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
	}

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return string
	 */
	public function getName(): string
	{
		return $this->name;
	}

	/**
	 * @param string $name
	 */
	public function setName(string $name): void
	{
		$this->name = $name;
	}

	/**
	 * @return LeafletPage
	 */
	public function getFirstPage(): ?LeafletPage
	{
		$criteria = Criteria::create()
			->andWhere(Criteria::expr()->eq('pageNumber', 1));

		$page = $this->pages->matching($criteria)->first();

		return $page ? $page : null;
	}

	/**
	 * @return LeafletPage
	 */
	public function getPageByNumber(int $page): ?LeafletPage
	{
		$criteria = Criteria::create()
			->andWhere(Criteria::expr()->eq('pageNumber', $page));

		$pages = $this->pages->matching($criteria);

		if (count($pages) > 0) {
			return $pages->first();
		}

		return null;
	}

	public function hasPageByNumber(int $page): bool
	{
		if (!$this->getPageByNumber($page)) {
			return false;
		}

		return true;
	}

	/**
	 * @return int|null
	 */
	public function getLeafletId(): ?int
	{
		return $this->leafletId;
	}

	/**
	 * @return string
	 */
	public function getSlug(): string
	{
		return $this->slug;
	}

	public function getShop(): Shop
	{
		return $this->shop;
	}

	public function getPages()
	{
		return $this->pages;
	}

	public function getValidTill()
	{
		return $this->validTill;
	}

    public function getValidTillDays()
    {
        return $this->validTill->diff(new DateTime())->days;
    }

	public function getValidSince(): ?DateTime
	{
		return $this->validSince;
	}

	public function isTop(): bool
	{
		return $this->top;
	}

	public function setTop(bool $top = true): void
	{
		$this->top = $top;
	}

	public function isExpired(): bool
	{
		return $this->checked !== false && $this->validTill <= new DateTime();
	}

    public function isFuture(): bool
    {
        return $this->validSince > new DateTime();
    }

	public function isValid(): bool
	{
		return ($this->validTill > new \DateTime() && $this->validSince < new \DateTime());
	}

	public function setDeletedAt($deletedAt): void
	{
		$this->deletedAt = $deletedAt;
	}

	public function isInNoIndexPeriod(): bool
	{
		return $this->isExpired() && $this->deletedAt && ((int) $this->deletedAt->diff(new DateTime())->format('%a')) < 10;
	}

	public function isDeleted(): bool
	{
		return $this->deletedAt != null &&  $this->deletedAt <= new DateTime();
	}

	public function isLeaflet(): bool
	{
		return $this->type === self::TYPE_LEAFLET;
	}

	public function isNewsletter(): bool
	{
		return $this->type === self::TYPE_NEWSLETTER;
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getType()
	{
		return $this->type;
	}

	public function setType($type): void
	{
		$this->type = $type;
	}

	public function getSubject()
	{
		return $this->subject;
	}

	public function setSubject($subject): void
	{
		$this->subject = $subject;
	}

	public function getUpdatedSitemapAt()
	{
		$now = new DateTime();

		if ($now > $this->validTill) {
			return $this->validTill;
		} elseif ($now > $this->validSince) {
			return $this->validSince;
		} else {
			return $this->createdAt;
		}
	}

	public function getCities()
	{
		return $this->cities;
	}

	public function setCities(array $cities)
	{
		$this->cities->clear();
		foreach ($cities as $city) {
			$this->cities->add($city);
		}
	}

	public function getCitiesHash()
	{
		$cities = $this->cities->toArray();
		$hash = '.';

		foreach ($cities as $city) {
			$hash .= DIRECTORY_SEPARATOR . $city->getId();
		}

		return sha1($hash);
	}

	public function addCity(City $city)
	{
		return $this->cities->add($city);
	}

	public function removeCity(City $city)
	{
		$this->cities->removeElement($city);
	}

	public function getPartnerLink()
	{
		return $this->partnerLink;
	}

	public function setPartnerLink($partnerLink): void
	{
		$this->partnerLink = $partnerLink;
	}

	public function isPrimary(): bool
	{
		return $this->primary;
	}

	public function setPrimary($primary)
	{
		$this->primary = $primary;
	}

	public function getCountOfArchivedPages()
	{
		return $this->countOfArchivedPages;
	}

	public function setCountOfArchivedPages($countOfArchivedPages): void
	{
		$this->countOfArchivedPages = $countOfArchivedPages;
	}

	public function setArchivedAt($archivedAt): void
	{
		$this->archivedAt = $archivedAt;
	}

	public function isArchived(): bool
	{
		return $this->archivedAt !== null;
//        return $this->archivedAt != null &&  $this->archivedAt <= new DateTime();
	}

	public function getArchiveAt(): ?\DateTime
	{
		return $this->archiveAt;
	}

	public function setArchiveAt(?\DateTime $archiveAt): void
	{
		$this->archiveAt = $archiveAt;
	}

	public function setValidSince(\DateTime $validSince): void
	{
		$this->validSince = $validSince;
	}

	public function setValidTill(\DateTime $validTill): void
	{
		$this->validTill = $validTill;
	}

	public function archive()
	{
		return $this->archivedAt = new \DateTime();
	}

	public function removeLeafletPage(LeafletPage $leafletPage)
	{
		if ($this->pages->contains($leafletPage)) {
			$this->pages->removeElement($leafletPage);
		}
	}

	public function isChecked(): bool
	{
		return $this->checked;
	}

	public function setChecked(bool $checked): void
	{
		$this->checked = $checked;
	}

    public function getUpdatedAt(): DateTime
    {
        return $this->updatedAt;
    }

    public function getOfferistaBrochureId()
    {
        return $this->offeristaBrochureId;
    }

    public function hasOfferistaId(): bool
    {
        return $this->offeristaBrochureId !== null;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function setOfferistaBrochureId($id): void
    {
        $this->offeristaBrochureId = $id;
    }

    public function getCountOfPages(): int
    {
        return $this->pages->count();
    }

    public function setIsOfferistaPromo(bool $isOfferistaPromo): void
    {
        $this->isOfferistaPromo = $isOfferistaPromo;
    }

    public function getOfferistaTrackingBugs()
    {
        try {
            return Json::decode($this->offeristaTrackingBugs ?: '[]');
        } catch (JsonException $e) {
            return [];
        }
    }

    public function setOfferistaTrackingBugs(?string $offeristaTrackingBugs): void
    {
        $this->offeristaTrackingBugs = $offeristaTrackingBugs;
    }

    public function isOfferista(): bool
    {
        return $this->offeristaBrochureId !== null;
    }
}
