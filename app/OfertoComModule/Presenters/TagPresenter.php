<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class TagPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	public function actionTag(Tag $tag): void
	{
		if ($tag->isShopsType()) {
			$this->prepareTagShops($tag);
		}

		if ($tag->isOffersType()) {
			$this->prepareTagOffers($tag);
		}

		$this->responseCacheTags[] = 'tag/' . $tag->getId();

		$this->template->tag = $tag;
	}

	private function prepareTagShops(Tag $tag): void
	{
		$this->setView('tagShops');

		$this->template->leaflets = $this->leafletFacade->findLeafletsByTag($tag, true, 60, Website::MODULE_OFERTO_COM);
		$this->template->shops = $this->shopFacade->findLeafletShopsByTag($tag, true, 100, Website::MODULE_OFERTO_COM);
	}

	private function prepareTagOffers(Tag $tag): void
	{
		$this->setView('tagOffers');

		$this->template->leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($tag->getMatchRule(), $tag->getLocalization());
		$this->template->offers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), true);
		$this->template->expiredOffers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), false);
	}
}
