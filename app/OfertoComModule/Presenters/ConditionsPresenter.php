<?php

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Conditions\Entities\Document;

class ConditionsPresenter extends BasePresenter
{
    public function renderDefault(Document $document)
    {
        $this->template->document = $document;

        $website = str_replace(['https://', 'www.'], '', $this->website->getDomain());
        $companyName = $this->website->getCompanyName();
        $companyId = $this->website->getCompanyId();
        $companyVatId = $this->website->getCompanyVatId();

        $this->template->content = str_replace(
            ['{$website}', '{$companyName}', '{$companyId}', '{$companyVatId}'],
            [$website, $companyName, $companyId, $companyVatId],
            $document->getContent()
        );
    }

    public function actionCookies()
    {
        $document = $this->documentFacade->findByLocalization($this->localization);

        $this->redirect('default', $document);
    }
}
