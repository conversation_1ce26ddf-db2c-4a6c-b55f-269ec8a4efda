<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Forms\ShopReviewControl\ShopReviewControl;
use <PERSON><PERSON><PERSON>\Forms\ShopReviewControl\ShopReviewControlFactory;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Seo\Entities\PageExtension;
use Ka<PERSON>ino\Model\Shops\ContentBlockFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ReviewFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON><PERSON>\Model\Websites\Entities\Website;

final class ShopPresenter extends BasePresenter
{
	private ?Shop $shop = null;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	/** @var ReviewFacade @inject */
	public $reviewFacade;

	/** @var ShopReviewControlFactory @inject */
	public $shopReviewControlFactory;

	public function actionShop(Shop $shop): void
	{
		if (!$shop->isActiveCoupons() && !$this->getUser()->isLoggedIn()) {
			$this->redirectPermanent(':OfertoCom:Homepage:default');
		}

		$this->shop = $shop;
		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		$this->template->shop = $shop;

		$this->template->coupons = $this->offerFacade->findOffersByShop($shop, 20, true, Offer::TYPE_COUPON);
        $this->template->deals = $this->offerFacade->findOffersByShop($shop, 20, true, Offer::TYPE_DEAL);
		$this->template->expiredOffers = $this->offerFacade->findOffersByShop($shop, 20, false, Offer::TYPE_COUPON);

		if ($couponId = $this->getRequest()->getParameter('oid')) {
			$offer = $this->offerFacade->findOffer($couponId);

			if ($offer->isCoupon()) {
				$popupCoupon = $offer;
			}
		}

		$this->template->popupCoupon = $popupCoupon ?? null;

        $topCouponRelative = $this->offerFacade->findTopOfferByShop($shop, Offer::DISCOUNT_TYPE_RELATIVE, Offer::TYPE_COUPON);
        $topCouponAbsolute = $this->offerFacade->findTopOfferByShop($shop, Offer::DISCOUNT_TYPE_ABSOLUTE, Offer::TYPE_COUPON);

        $hasCoupon = $topCouponRelative || $topCouponAbsolute;

        $topOfferRelative = null;
        if ($topCouponRelative === null) {
            $topOfferRelative = $this->offerFacade->findTopOfferByShop($shop, Offer::DISCOUNT_TYPE_RELATIVE);
        }

        $topOfferAbsolute = null;
        if ($topCouponAbsolute === null) {
            $topOfferAbsolute = $this->offerFacade->findTopOfferByShop($shop, Offer::DISCOUNT_TYPE_ABSOLUTE);
        }

        /** @var Offer $topOfferAbsolute */
        $topOfferAbsolute = $topCouponAbsolute ?: ($topOfferAbsolute ?: 0);

        /** @var Offer $topOfferRelative */
        $topOfferRelative = $topCouponRelative ?: ($topOfferRelative ?: 0);

		$this->template->topOfferAbsolute = $topOfferAbsolute;
        $this->template->topOfferRelative = $topOfferRelative;

        $this->template->topOfferAbsoluteFormatted = $topOfferAbsolute ? $this->priceFilter->__invoke($topOfferAbsolute->getDiscountAmount(), $this->localization) : 0;

        $topOffer = $topCouponRelative ?: $topCouponAbsolute ?: $topOfferAbsolute ?: $topOfferRelative;

        $this->template->topOffer = $topOffer;
        $this->template->hasCoupon = $hasCoupon;

        $this->template->seoGenerator = $this->seoGenerator;

		$contentBlocks = $this->contentBlockFacade->findContentBlocksByShop($shop, Website::MODULE_OFERTO_COM);

		$this->template->getContentBlockByType = function(string $type) use ($contentBlocks) {
			return $contentBlocks[$type] ?? null;
		};

		$this->template->getHeadingFromPageExtension = function(PageExtension $pageExtension) use ($shop) {
			return $this->seoGenerator->generateShopHeadingFromPageExtension($pageExtension, $shop, $this->website);
		};

		$this->template->leaflets = $this->leafletFacade->findLeafletsByShop($shop, 10);

		$this->template->faqContentBlocks = $this->contentBlockFacade->findFaqContentBlocks($shop, Website::MODULE_OFERTO_COM);

		$this->template->getAverageShopReview = (function () use ($shop) {
			return $this->reviewFacade->findAverageShopReview($shop);
		});

		$this->template->getCountOfTotalReviews = (function () use ($shop) {
			return $this->reviewFacade->findCountOfShopReviews($shop);
		});

		$length = 0;
		foreach ($contentBlocks as $contentBlock) {
			$content = $contentBlock->getContent();

			if ($content === null) {
				continue;
			}

			$length += strlen($content);

			if ($length >= 50) {
				break;
			}
		}

		$this->template->contentBlocksAllowed = $length > 50;

		$this->template->getAlternativeShopsByLabel = function(Tag $label, Shop $shop) {
			$result = [];

			$alternativeShops = $this->shopFacade->findShopsByLabel($label, null);

			/** @var Shop $alternativeShop */
			foreach ($alternativeShops as $alternativeShop) {
				if ($alternativeShop === $shop) {
					continue;
				}

				if ($alternativeShop->hasAlternativeNames() === false) {
					continue;
				}

				$result[] = $alternativeShop;
			}

			return $result;
		};
	}

	protected function createComponentShopReviewControl(): ShopReviewControl
	{
		$control = $this->shopReviewControlFactory->create($this->shop);

		$control->onSuccess[] = (function () {
			$this->flashMessage($this->translator->translate('ofertocom.shop.review.sent'), 'success');
			$this->redirect('this');
		});

		return $control;
	}
}
