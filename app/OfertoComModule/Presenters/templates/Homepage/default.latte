{block scripts}
    {include parent}
{/block}

{block title}{_ofertocom.homepage.metaTitle}{/block}
{block description}{_ofertocom.homepage.metaText}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="k__title ta-center mt-5 mb-4">{_ofertocom.homepage.title}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_ofertocom.homepage.text}</p>
    </div>

    <div class="ocom-coupon__wrapper">
        {foreach $offers as $offer}
            {include '../components/offer-item-half.latte', offer => $offer, hideBadge => true, hideButton => true}
        {/foreach}
    </div>

    <div class="ta-center">

    </div>

    <div class="mb-4">
        <h2 class="k__title ta-center mt-5 mb-4">{_ofertocom.homepage.shops.title}</h2>
        <p class="k__text ta-center mw-700 mb-0">{_ofertocom.shops.text}</p>
    </div>

    <div class="k-shop">
        {foreach $shops as $shop}
            {include '../components/shop-logo.latte', shop => $shop}
        {/foreach}
    </div>

    <div class="ta-center mb-5">
        <a href="{link Shops:shops}" class="color-grey td-hover-underline">
            {_ofertocom.homepage.allShops}
        </a>
    </div>

    {if count($leaflets) > 0}
        <div class="mb-4">
            <h2 class="k__title ta-center mt-5 mb-4">{_ofertocom.homepage.leaflets.title}</h2>
        </div>

        <div class="k-leaflets__wrapper mt-5">
            {if true}
                <div class="k-leaflets__item k-leaflets__item--mobile mb-5">
                    <!-- letado.com / mobile_rectangle1 -->
                    {include "../components/mobile_rectangle1.latte"}
                </div>
            {/if}

            {foreach $leaflets as $leaflet}
                <div class="k-leaflets__item mb-5">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                        <picture n:if="$leaflet->getFirstPage()">
                            <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                        </picture>
                    </a>
                    <div class="k-leaflets__title mt-0 mb-0">
                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                            {$leaflet->getName()}
                        </a>
                    </div>
                    <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                </div>
            {/foreach}
        </div>
    {/if}
</div>