<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\AdminModule\Forms\ILeaflatPageControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ILeafletPageControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\LeaflatPageControl;
use <PERSON><PERSON>ino\AdminModule\Forms\LeafletPageControl;
use Ka<PERSON>ino\Model\Leaflets\AnnotationCollection;
use Nette\Utils\Image;
use Nette\Utils\Json;

class LeafletPagePresenter extends BasePresenter
{
	/** @var ILeafletPageControlFactory @inject */
	public $leafletPageControlFactory;

    /** @persistent */
    public ?int $localizationId = null;

	public function actionAnnotations($id = null, $backId = null)
	{
        $localization = null;
        if ($this->localizationId) {
            $localization = $this->localizationFacade->findLocalization($this->localizationId);
        }

        if ($id) {
			$leafletPage = $this->leafletFacade->findLeafletPage($id);

			$imageUrl = ($this->imageFilter)($leafletPage->getImageUrl(), 750);
			$image = Image::fromString(file_get_contents($imageUrl));

            $annotations = null;
			if ($leafletPage->isAnnotated()) {
				$annotations = AnnotationCollection::fromJson($leafletPage->getAnnotationsOutput());
			}

			$this->template->backId = $backId;
			$this->template->imageUrl = $imageUrl;
            $annotationsData = $annotations
                ? $annotations->exportAnnotatorArray($image->getWidth(), $image->getHeight())
                : [];

            $this->template->annotations = array_map(
                static fn($annotation) => Json::encode($annotation),
                $annotationsData
            );

		} else {
			$leafletPages = $this->leafletFacade->findLeafletPagesToAnnotate(1, $localization);

			if (count($leafletPages) > 0) {
				$this->redirect('annotations', ['id' => $leafletPages[0]->getId(), 'backId' => $backId]);
			}

			$leafletPage = null;
		}

        $this->template->localizationId = $this->localizationId;
        $this->template->localizationsWithLeafletPagesToAnnotate = $this->leafletFacade->findCountOfLeafletPagesToAnnotateByLocalization();
		$this->template->countOfleafletPagesRemaining = $this->leafletFacade->getCountOfLeafletPagesToAnnotate($localization);
        $this->template->countOfleafletAllPagesRemaining = $this->leafletFacade->getCountOfLeafletPagesToAnnotate();
		$this->template->leafletPage = $leafletPage;
	}

	protected function createComponentLeafletPageControl(): LeafletPageControl
	{
		$leafletPage = $this->getParameter('id') ? $this->leafletFacade->findLeafletPage($this->getParameter('id')) : null;
		$control = $this->leafletPageControlFactory->create($leafletPage);

		$control->onSuccess[] = function ($entity) use ($leafletPage) {
			$this->flashMessage('Successfully saved.');
			$this->redirect(':Admin:LeafletPage:annotations', ['localizationId' => $this->localizationId, 'backId' => $leafletPage->getId()]);
		};

		return $control;
	}

    public function handleUnArchiveOffer(int $offerId)
    {
        $offer = $this->offerFacade->findOffer($offerId);
        $offer->unArchive();

        $this->offerFacade->saveOffer($offer);

        $this->redirect('this');
    }

    public function actionExcludeFromTag(int $leafletPageId, int $tagId, string $backLink)
    {
        $leafletPage = $this->leafletFacade->findLeafletPage($leafletPageId);
        $leafletPage->addExcludedTag($tagId);

        $this->leafletFacade->saveLeafletPage($leafletPage);

        $this->redirectUrl($backLink);
    }
}
