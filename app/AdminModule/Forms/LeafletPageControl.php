<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Images\ImageFilter;
use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Leaflets\AnnotationCollection;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\LeafletPage;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use Ka<PERSON>ino\Model\Users\UserIdentity;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Image;

class LeafletPageControl extends Nette\Application\UI\Control
{
	public array $onSuccess = [];
    private LeafletPage $leafletPage;
    private LeafletFacade $leafletFacade;
    private UserIdentity $userIdentity;
    private ImageStorage $imageStorage;
    private ImageFilter $imageFilter;

    public function __construct(?LeafletPage $leafletPage, LeafletFacade $leafletFacade, UserIdentity $userIdentity, ImageStorage $imageStorage, ImageFilter $imageFilter)
	{
        $this->leafletPage = $leafletPage;
        $this->leafletFacade = $leafletFacade;
        $this->userIdentity = $userIdentity;
        $this->imageStorage = $imageStorage;
        $this->imageFilter = $imageFilter;
    }

	public function createComponentForm(): Form
    {
		$form = new Form();

		$form->addTextArea('annotations', 'Annotations output:', null, 10)
			->setHtmlAttribute('class', 'annotator');

		$form->addHidden('startTime', time());

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('skip', 'Skip');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$leafletPage = $this->leafletPage;

			if ($form['submit']->isSubmittedBy()) {
				if (!$values->annotations) {
					throw new InvalidArgumentException("Annotations output is required.");
				}

				$sourceImage = Image::fromString(
					file_get_contents(
						($this->imageFilter)($leafletPage->getImageUrl(), 750, null, 'fit')
					)
				);

				$annotationCollection = AnnotationCollection::importAnnotatorArray(
					Nette\Utils\Json::decode($values->annotations, Nette\Utils\Json::FORCE_ARRAY),
					$sourceImage->getWidth(),
					$sourceImage->getHeight()
				);

				$leafletPage->setAnnotationsOutput($annotationCollection->toJson());
				$leafletPage->setAnnotated();
				$leafletPage->setAnnotator($this->userIdentity->getIdentity());
				$leafletPage->setTimeToAnnotate(time() - $values->startTime);
                $leafletPage->resetProcess();

				$image = Image::fromString(file_get_contents($leafletPage->getImageUrl()));
				if ($image->getWidth() > 2500) {
					$image->resize(2500, null);
				}

				$this->imageStorage->saveImagev2($image, Image::JPEG, ImageStorage::NAMESPACE_LEAFLET_ANNOTATED, $leafletPage->getId());
			} else {
				$leafletPage->fail();
			}

			$this->leafletFacade->saveLeafletPage($leafletPage);

			$this->onSuccess($leafletPage);
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render($annotations = null)
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface ILeafletPageControlFactory
{
	public function create(LeafletPage $leafletPage = null): LeafletPageControl;
}
