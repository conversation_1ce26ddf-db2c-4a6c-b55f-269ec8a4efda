{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block scripts}
    {include parent}
{/block}

{block description}{_kaufino.offer.metaDescription}{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {var $localizedUrl = $getLocalizedUrl($otherWebsite, 'offers')}

        <link rel="alternate" n:if="$localizedUrl" n:attr="hreflang: $otherWebsite !== $website ? $otherWebsite->getLocalization()->getFullLocale('-') : 'x-default'" href={$localizedUrl} />
    {/foreach}
{/block}


{block content}
<div class="container">
    <div class="mb-4">
        <h1 n:block="title" class="k__title ta-center mt-4 mt-sm-5 mb-3">{_kaufino.offer.title}</h1>
        <p class="k__text ta-center mw-700 mb-3">{_kaufino.offer.text}</p>						    
    </div>
    
    {if count($offerTagsAlphabetically) > 0}
        <div class="d-flex space-between mt-5 mb-5">
            {foreach $offerTagsAlphabetically as $char => $tags}
                <a href="#{$char}" class="k-tag">{$char}</a>
            {/foreach}
        </div>

        <div>
            {foreach $offerTagsAlphabetically as $char => $tags}
                <div id="{$char}">
                    <strong class="d-block mb-3">{$char}</strong>
                    <div class="k-tag mb-3">
                        <div n:foreach="$tags as $tag" class="k-tag__inner">
                            <a n:href="Offers:tag $tag" class="k-tag__item">{$tag->getName()}</a>
                        </div>
                    </div>
                </div>
            {/foreach}
        </div>
    {/if}

    <div n:if="count($offers) > 0">
        <h2 id="offers" class="k__title ta-center mt-4 mt-sm-5 mb-4">{_kaufino.offer.whatOnSale}</h2>

        <div class="k-offers">
            {foreach $offers as $offer}
                {continueIf !$offer->getLeafletPage()}
                {include '../components/offer-item.latte', offer => $offer}
            {/foreach}
        </div>
    </div>
</div>
