{var $tag = $offer->getTags()->last()}
<div class="k-offers__item">
    <div class="k-offers__inner" data-line="{_kaufino.shop.expired}">
        <a n:href="Offers:tag $tag" class="k-offers__image-wrapper">
            <picture>
                <source
                        srcset="
                        {$offer->getImageUrl()|image:150,150,'fit','webp'} 1x,
                        {$offer->getImageUrl()|image:300,300,'fit','webp'} 2x
                    "
                        type="image/webp"
                >
                <img
                        loading="lazy"
                        src="{$offer->getImageUrl()|image:150,150,'fit'}"
                        srcset="
                        {$offer->getImageUrl()|image:150,150,'fit'} 1x,
                        {$offer->getImageUrl()|image:300,300,'fit'} 2x
                    "
                        data-sizes="auto"
                        width="150"
                        height="150"
                        alt="{$offer->getName()}"
                        class="k-offers__image"
                >
            </picture>
        </a>

        <div class="k-offers__content">
            <small class="k-offers__small k-offers__small--logo">
                <a n:href="Offers:tag $tag" class="k-offers__logo-wrapper">
                    <img src="{$offer->getShop()->getLogoUrl() |image:80,80}" alt="{$offer->getShop()->getName()}" loading="lazy">
                </a>
            </small>

            <p class="k-offers__title line-clamp-2">
                {_kaufino.tag.offerTagTitle, [tag => $tag->getName()]}
            </p>

            <div class="k-offers__price">
                <div class="k-offers__right">
                    <strong>{_kaufino.tag.offerTagPriceFrom, ['price' => ($offer->getCurrentPrice()|price:$offer->getLocalization())]}</strong>
                </div>
            </div>
        </div>
    </div>
</div>