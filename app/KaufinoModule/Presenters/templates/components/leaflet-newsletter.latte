<div class="k-leaflets__item mb-5">                
    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
        <picture n:if="$leaflet->getFirstPage()">
            <source 
                srcset="
                    {$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop2','webp'} 1x, 
                    {$leaflet->getFirstPage()->getImageUrl() |image:460,576,'exactTop2','webp'} 2x
                " 
                type="image/webp"
            >            
            <img 
                src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop2'}"                             
                srcset="
                    {$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop2'} 1x,
                    {$leaflet->getFirstPage()->getImageUrl() |image:460,576,'exactTop2'} 2x
                "
                width="230" 
                height="288" 
                alt="{$leaflet->getName()}" 
                class="k-leaflets__image"
                loading="lazy"
            >
        </picture>                    
    </a>
    <div class="k-leaflets__title mt-0 mb-0">
        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
            {$leaflet->getName()}
        </a>
    </div>                
    <p class="k-leaflets__date mt-0 mb-0">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
</div>  