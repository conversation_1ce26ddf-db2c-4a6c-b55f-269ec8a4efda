{if isset($city) && $city !== null &&  $shop->isStore() && $shop->hasCity($city)}
<a n:href="City:shop $city, $shop" class="k-shop__item {isset($cssClass) ? $cssClass}" title="{_kaufino.shop.storeLeaflet, [brand => $shop->getName()]}">
{else}
<a n:href="Shop:shop $shop" class="k-shop__item {isset($cssClass) ? $cssClass}" title="{_kaufino.shop.storeLeaflet, [brand => $shop->getName()]}">
{/if}
    <span class="k-shop__image-wrapper">
        <picture>                        
            <source 
                srcset="
                    {$shop->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                    {$shop->getLogoUrl() |image:160,140,'fit','webp'} 2x
                " 
                type="image/webp"
            >                                    
            <img 
                src="{$shop->getLogoUrl() |image:80,70,'fit','png'}" 
                srcset="
                    {$shop->getLogoUrl() |image:80,70,'fit','png'} 1x,
                    {$shop->getLogoUrl() |image:160,140,'fit','png'} 2x
                "
                data-sizes="auto"
                width="80" 
                height="70" 
                alt="{$shop->getName()}" 
                class=""
                loading="lazy"
            >
        </picture>                    
    </span>
    <small class="k-shop__title">{$shop->getName()}{isset($city) && $city !== null ? ' ' . $city->getName()}</small>

    {if $user->isLoggedIn()}
        {var $countOfCurrentAndFutureLeaflets = count($shop->getCurrentAndFutureLeaflets())}
        {var $countOfCurrentAndFutureNewsletter = count($shop->getCurrentAndFutureNewsletter())}

        <span n:class="($countOfCurrentAndFutureLeaflets >= 1 && $shop->isStore()) || ($countOfCurrentAndFutureNewsletter >= 1 && $shop->isEshop()) ? 'green', 'k-shop__bubble'">
            L: {$countOfCurrentAndFutureLeaflets} / N: {$countOfCurrentAndFutureNewsletter}
        </span>

        {var $countOfExpiredLeaflets = count($shop->getExpiredLeaflets())}
        {var $countOfExpiredNewsletters = count($shop->getExpiredNewsletters())}

        <span n:class="($countOfExpiredLeaflets !== 0 && $shop->isStore()) || ($countOfExpiredNewsletters !== 0 && $shop->isEshop()) ? 'green', 'k-shop__bubble'"  style="margin-top: 22px">
            L: {$countOfExpiredLeaflets} / N: {$countOfExpiredNewsletters}
        </span>
    {/if}
</a>