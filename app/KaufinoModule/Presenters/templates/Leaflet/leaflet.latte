{block title}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{if $leaflet->isExpired()}
    {_kaufino.leaflet.expiredLeafletTitle, [brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill]}
{elseif $leaflet->isFuture()}
    {_kaufino.leaflet.futureLeafletTitle, [brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill]}
{else}
    {if $leaflet->isChecked()}
        {_kaufino.leaflet.metaTitle, [brand => $leaflet->getName(), validSince => $validSince, validTill => $validTill]}
    {else}
        {_kaufino.leaflet.metaTitleUnChecked, [brand => $leaflet->getName()]}
    {/if}
{/if}
{/block}

{block description}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{if $leaflet->isExpired()}
    {_kaufino.leaflet.expiredLeafletDescription, [brand => $leaflet->getName(), validTill => $validTill]}
    {if $validLeaflet}
        {capture $validSinceDay}{$validLeaflet->getValidSince()|dayGenitive}{/capture}
        {capture $validSince}{$validLeaflet->getValidSince()|localDate:'long'}{/capture}
        {_kaufino.leaflet.actualLeafletValidSince, [validSince => $validSince, validSinceDay => $validSinceDay]}
    {/if}
{elseif $leaflet->isFuture()}
    {_kaufino.leaflet.futureLeafletDescription, [brand => $leaflet->getName(), validSince => $validSince]}
{else}
    {if $leaflet->isChecked()}
        {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
    {else}
        {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
    {/if}
{/if}
{/block}

{block robots}{if $leaflet->isArchived()}noindex,follow{elseif $currentPage > 1 || $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container k-breadcrumb__container--leaflet mt-4">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link">{$shop->getTag()->getName()}</a> |
            <a n:href="Shop:shop $shop" class="link">{$shop->getName()}</a>

            {*
            {if strtolower($leaflet->getName()) === strtolower($shop->getName())}
                {_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('w') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}
            {else}
                <span class="color-grey">{$leaflet->getName()}</span>
            {/if}
            *}
        </p>
    </div>
{/block}

{block head}    
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Leaflet',
            'country' : {{$localization->getRegion()}}
        });
    </script>

    <link
        rel="preload"
        as="image"
        href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null,'fit','webp'}"
    />

    {*}
    <link
        rel="preload"
        as="image"
        href="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:768,null}"
    />
    *}
{/block}

{block scripts}
    {include parent}
    
    {capture $validSinceShort}{$leaflet->getValidSince()|localDate} {/capture}
    {capture $validTillShort}{$leaflet->getValidTill()|localDate}{/capture}

    {capture $leafletName |stripHtml|spaceless}
        {if $leaflet->isChecked()}
            {capture $shopBrandName}{$leaflet->getName()|upper}{/capture}
            {_kaufino.leaflet.title, [brand => $shopBrandName, validSince => $validSinceShort, validTill => $validTillShort]|noescape}
        {else}
            {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
        {/if}
    {/capture}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "ItemList",
            "name": {$leafletSchemaName},
        "url":  {$presenter->link('//this')},
        "numberOfItems": {$leaflet->getCountOfPages()},
        "itemListOrder": "Ascending",
        "itemListElement": [
{foreach $leaflet->getPages() as $page}
{
    "@type": "ListItem",
    "position": {$page->getPageNumber()},
    "name": {$leafletName . ' – ' . ("$websiteType.leaflet.metaTitlePageSuffix", ['page' => $page->getPageNumber()] |translate)},
    "description": {_"$websiteType.leaflet.leafletPageDescription", ['validSinceDay' => $validSinceDay, 'validSince' => $validSince, 'brand' => $shop->getName(), 'pageNumber' => $page->getPageNumber()]},
    "url": {$page->getImageUrl() |image:768,null,'fit','webp'}
}{sep},{/sep}
        {/foreach}
        ]
    }
    </script>

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_kaufino.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$shop->getTag()->getName()},
                "item": {link //Tag:tag $shop->getTag()}
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": {$shop->getName()},
                "item": {link //Shop:shop $shop}
            },
            {
                "@type": "ListItem",
                "position": 5,
                "name": {$leafletName},
                "item": {link //this}
        }
]
}
    </script>

    {if $leaflet->getOfferistaBrochureId() !== null}
        <script>
            (function() {
                fetch({link :Api:Offerista:brochurePageView, websiteId: $presenter->website->getId()}, {
                    method: "POST",
                    headers: {'X-Requested-With': 'XMLHttpRequest'},
                    body: JSON.stringify({
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()}
                    }),
                })
                    .then(response => response.json())
                    .then(payload => {
                        console.log(payload)
                    });
            })();

            (function() {
                const startTime = Date.now();

                function trackTimeSpent() {
                    const timeSpent = Date.now() - startTime;

                    const payload = {
                        brochureId: {$leaflet->getOfferistaBrochureId()},
                        page: {$currentPage},
                        leafletId: {$leaflet->getId()},
                        duration: timeSpent / 1000,
                    };

                    const url = {link :Api:Offerista:brochurePageViewDuration, websiteId: $presenter->website->getId()};
                    const data = new Blob([JSON.stringify(payload)], { type: 'application/json' });

                    if (!navigator.sendBeacon(url, data)) {
                        fetch(url, {
                            method: "POST",
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify(payload),
                            keepalive: true
                        });
                    }
                }

                window.addEventListener('unload', trackTimeSpent);
                window.addEventListener('beforeunload', trackTimeSpent);
            })();
        </script>

        <script>
            const trackingBugs = {$leaflet->getOfferistaTrackingBugs()}

                trackingBugs.forEach(url => {
                    const cacheBuster = Date.now();
                    const finalUrl = url.replace('%%CACHEBUSTER%%', cacheBuster);
                    const img = new Image();
                    img.src = finalUrl;
                });
        </script>
    {/if}
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

{capture $validSinceShort}{$leaflet->getValidSince()|localDate}{/capture}
{capture $validTillShort}{$leaflet->getValidTill()|localDate}{/capture}

{var $cacheKey = 'leaflet-' . $leaflet->getId() . '-page-' . $currentPage}

<div class="leaflet k-lf-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $leaflet->isChecked()}
                                {_kaufino.leaflet.title, [brand => $leaflet->getName(), validSince => $validSinceShort, validTill => $validTillShort]|noescape}
                            {else}
                                {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                            {/if}
                        </h1>						
					</div>

					<div n:if="false" class="leaflet__detail-header-side">
						<a n:href="Shop:shop $leaflet->getShop()">
                            <picture>
                                <source
                                    srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','webp'} 2x
                                    "
                                    type="image/webp"
                                >
                                <img
                                    src="{$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','png'}"
                                    srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','png'} 2x
                                    "
                                    width="80"
                                    height="70"
                                    alt="{$leaflet->getShop()->getName()}"
                                    class="leaflet__detail-header-logo"
                                >
                            </picture>
						</a>
					</div>
				</div>
                
                <div>                    
                    {if $leaflet->isExpired()}
                        <div class="mb-3 text-center">
                            <h3>{_kaufino.leaflet.expiredLeafletHeading}</h3>
                            <div class="d-flex">
                                <a n:href="Shop:shop $leaflet->getShop()" class="link ml-auto k-show-more-button">{_kaufino.leaflet.expiredLeafletLinkToShop, [brand => $leaflet->getShop()->getName()]}</a>
                            </div>
                        </div>
                    {/if}

                    {if $leaflet->isOfferista()}
                        {if $leaflet->isArchived() === false && $leaflet->isExpired() === false}
                            {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                        {/if}

                        <div class="leaflet-preview mb-5">
                            {var $leafletPage = $leaflet->getPageByNumber($currentPage)}
                            <picture id="p-{$leafletPage->getPageNumber()}">
                                <source
                                        srcset="
                                        {$leafletPage->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                        {$leafletPage->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                    "
                                        sizes="
                                        (min-width: 1200px) 645px,
                                        100vw
                                    "
                                        type="image/webp"
                                >
                                <img
                                        src="{$basePath}/images/placeholder-870.png"
                                        srcset="
                                        {$leafletPage->getImageUrl() |image:768,null} 768w,
                                        {$leafletPage->getImageUrl() |image:1740,null} 1740w
                                    "
                                        sizes="
                                        (min-width: 1200px) 645px,
                                        100vw
                                    "
                                        width="870"
                                        height="1190"
                                        alt="{$leaflet->getShop()->getName()}"
                                >
                            </picture>

                        {if $leafletPage->getClickouts()}
                                {foreach $leafletPage->getClickoutsArray() as $clickout}
                                    <div class="kf-click-out" style="position: absolute; z-index: 9999; left: {$clickout['left']*100 |noescape}%; top: {$clickout['top']*100 |noescape}%; min-width: 3%; width: {$clickout['width']*100 |noescape}%; min-height: 3%; height: {$clickout['height']*100 |noescape}%;">
                                        <a n:href=":Api:Offerista:clickout, websiteId: $presenter->website->getId(), target: $clickout['url'], brochureId: $leaflet->getOfferistaBrochureId(), page: $leafletPage->getPageNumber(), " target="_blank" title="">
                                            <svg viewBox="0 0 24 24">
                                                <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"></path>
                                            </svg>
                                        </a>
                                    </div>
                                {/foreach}
                            {/if}
                        </div>

                        {if $leaflet->isArchived() === false && $leaflet->isExpired() === false}
                            {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}
                        {/if}
                    {else}
                        <div class="leaflet-preview mb-5">
                            {foreach $leaflet->getPages() as $page}
                            {breakIf $leaflet->isArchived() && $iterator->getCounter() > $leaflet->getCountOfArchivedPages() || $page->getImageUrl() === null}
                            <div style="position: relative">
                                {capture $imageAltTitle}{if $leaflet->isChecked()}{_kaufino.leaflet.imageAltTitleWithDate, [brand => $leaflet->getShop()->getName(), leafletName => $leaflet->getName(), validSince => $validSinceShort, validTill => $validTillShort, page => $page->getPageNumber()]|noescape}{else}{_kaufino.leaflet.imageAltTitle, [brand => $leaflet->getShop()->getName(), leafletName => $leaflet->getName(), page => $page->getPageNumber()]|noescape}{/if}{/capture}

                                {if $iterator->first}
                                    <picture id="p-{$page->getPageNumber()}">
                                        <source
                                            srcset="
                                                {$page->getImageUrl() |image:384,null,'fit','webp'} 384w,
                                                {$page->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                                {$page->getImageUrl() |image:1152,null,'fit','webp'} 1152w,
                                                {$page->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                            "
                                            sizes="
                                                (min-width: 1200px) 645px,
                                                100vw
                                            "
                                            type="image/webp"
                                        >
                                        <img
                                            src="{$page->getImageUrl() |image:384,null}"
                                            srcset="
                                                {$page->getImageUrl() |image:384,null} 384w,
                                                {$page->getImageUrl() |image:768,null} 768w,
                                                {$page->getImageUrl() |image:1152,null} 1152w,
                                                {$page->getImageUrl() |image:1740,null} 1740w
                                            "
                                            sizes="
                                                (min-width: 1200px) 645px,
                                                100vw
                                            "
                                            width="870"
                                            height="1190"
                                            alt="{$imageAltTitle}"
                                            title="{$imageAltTitle}"
                                            class="{if $leaflet->isExpired()}grayscale{/if}"
                                        >
                                    </picture>
                                {else}
                                    <picture id="p-{$page->getPageNumber()}" data-expand="100" class="{if $iterator->last}leaflet-last-page{/if}">
                                        <source
                                            srcset="
                                                {$page->getImageUrl() |image:384,null,'fit','webp'} 384w,
                                                {$page->getImageUrl() |image:768,null,'fit','webp'} 768w,
                                                {$page->getImageUrl() |image:1152,null,'fit','webp'} 1152w,
                                                {$page->getImageUrl() |image:1740,null,'fit','webp'} 1740w
                                            "
                                            sizes="
                                                (min-width: 1200px) 645px,
                                                100vw
                                            "
                                            type="image/webp"
                                        >
                                        <img
                                            src="{$page->getImageUrl() |image:384,null}"
                                            srcset="
                                                {$page->getImageUrl() |image:384,null} 384w,
                                                {$page->getImageUrl() |image:768,null} 768w,
                                                {$page->getImageUrl() |image:1152,null} 1152w,
                                                {$page->getImageUrl() |image:1740,null} 1740w
                                            "
                                            sizes="
                                                (min-width: 1200px) 645px,
                                                100vw
                                            "
                                            width="870"
                                            height="1190"
                                            alt="{$imageAltTitle}"
                                            title="{$imageAltTitle}"
                                            loading="lazy"
                                            class="{if $leaflet->isExpired()}grayscale{/if}"
                                        >
                                    </picture>
                                {/if}
                            </div>

                            {if $iterator->odd}
                                <div class="ads-container">
                                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                    <!-- Detail letaku - Responsive - 1 -->
                                    <ins
                                        class="adsbygoogle"
                                        style="display:block"
                                        data-ad-client="ca-pub-4233432057183172"
                                        data-ad-slot="4173441893"
                                        data-ad-format="auto"
                                        data-full-width-responsive="true">
                                    </ins>
                                </div>
                            {else}
                                <div class="ads-container">
                                    <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                    <!-- Detail letaku - Responsive - 3 -->
                                    <ins
                                        class="adsbygoogle"
                                        style="display:block"
                                        data-ad-client="ca-pub-4233432057183172"
                                        data-ad-slot="2477216848"
                                        data-ad-format="auto"
                                        data-full-width-responsive="true">
                                    </ins>
                                </div>
                            {/if}

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        {/foreach}
                        </div>
                    {/if}

                <div>
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- Letaky - Detail letaku - Responsive - 2 -->
                        <ins class="adsbygoogle mrec-xs mrec-sm mrec-md leaderboard-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="4885879417" data-ad-format="auto" data-full-width-responsive="true"></ins>

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    </div>
                </div>                

                <div n:if="count($offers) > 0" class="mb-5">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offers, [brand => $shop->getName()]}</h2>

                    <div class="k-offers k-offers--4">
                        {foreach $offers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            {breakIf $iterator->getCounter() > 4}
                            {include '../components/offer-item.latte', offer => $offer, hideShop => true}
                        {/foreach}
                    </div>
                </div>

                {capture $leafletBrandLink}
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                {capture $leafletPageCount}
                    {count($leaflet->getPages())}
                {/capture}

                {*
                <p class="page-header__text ml-0">
                    {if $leaflet->isChecked()}
                        {_kaufino.leaflet.metaDesc, [brand => $leaflet->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
                    {else}
                        {_kaufino.leaflet.metaDescUnChecked, [brand => $leaflet->getName()]|noescape}
                    {/if}
                </p>
                *}

                <div class="px-3 px-lg-0 mt-3">
                    {*<p class="color-grey fz-m lh-15 mb-3" n:if="$leaflet->isChecked()"><strong>{_kaufino.leaflet.smallTitle, [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>*}
                    <p class="color-grey fz-m lh-15 mb-5">
                        {if $leaflet->isChecked()}
                            {_'kaufino.leaflet.desc', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}
                        {else}
                            {_'kaufino.leaflet.descUnChecked', [leafletBrandLink => $leafletBrandLink, leafletPageCount => $leafletPageCount] |noescape}
                        {/if}
                    </p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_kaufino.leaflet.backToLeaflets}</a>
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_kaufino.leaflet.allBrandLeaflets, [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>
            </div>
        </div>



            {var $similarLeaflets = $getSimilarLeaflets()}
            <div class="leaflet__sidebar" style="height: auto !important;">
                <div class="lf__box" n:if="$similarLeaflets && count($similarLeaflets) > 0">
                    <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_kaufino.leaflet.similarLeaflets, [brand => $leaflet->getShop()->getName()]}</h3>
                    <div class="lf__box-wrapper">
                        {foreach $similarLeaflets as $similarLeaflet}
                            <div class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                                {include '../components/leaflet-small.latte', leaflet => $similarLeaflet}
                            </div>
                        {/foreach}
                    </div>
                </div>

                {*
                <div class="float-wrapper">
                    <!-- Detail letaku - Sidebar - 2 -->
                    <ins class="adsbygoogle mrec-xs mrec-sm skyscraper-md halfpage-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="5041874235" data-ad-format="auto" data-full-width-responsive="true"></ins>

                    <script>
                        (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>
                *}

                {var $recommendedLeaflets = $getRecommendedLeaflets()}
                <div class="leaflet__aside" n:if="$recommendedLeaflets && count($recommendedLeaflets) > 0">
                    <div class="lf__box lf__box-lg-border">
                        <h3 class="lf__box-title px-3 px-lg-0">{_kaufino.leaflet.recommendedLeaflets}</h3>
                        <div class="lf__box-wrapper">
                            {foreach $recommendedLeaflets as $recommendedLeaflet}
                                {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                                <div class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                                    {include '../components/shop-leaflet-small.latte', leaflet => $recommendedLeaflet}
                                </div>
                            {/foreach}
                        </div>
                    </div>                    
            </div>
            </div>
    </div>

	<div class="float-wrapper__stop"></div>
</div>

