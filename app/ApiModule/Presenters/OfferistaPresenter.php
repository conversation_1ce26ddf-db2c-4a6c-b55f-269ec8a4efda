<?php

namespace <PERSON><PERSON><PERSON>\ApiModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Datadog\DatadogClient;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Offerista\Client;
use <PERSON><PERSON><PERSON>\Model\Offerista\ClientFactory;
use <PERSON><PERSON><PERSON>\Presenters\BasePresenter;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Tracy\Debugger;

class OfferistaPresenter extends BasePresenter
{
    /** @var ClientFactory @inject */
    public $clientFactory;

    /** @var GeoFacade @inject */
    public $geoFacade;

    /** @var DatadogClient @inject */
    public DatadogClient $datadogClient;

    private string $userId;
    private string $utmSource;
    private Client $client;

    public function startup()
    {
        parent::startup();

        if ($this->website === null && $this->getParameter('websiteId')) {
            $this->website = $this->websiteFacade->findWebsite($this->getParameter('websiteId'));
        }

        if ($this->website) {
            $locale = $this->website->getLocalization()->getLocale();
        } elseif ($this->localization) {
            $locale = $this->localization->getLocale();
        } else {
            $locale = 'cs';
        }

        if ($this->website === null || $this->website->isKaufino() || $this->website->isKaufinoSubdomain()) {
            $credentials = Client::CREDENTIALS_KAUFINO[$locale] ?? null;
            $this->utmSource = Client::UTM_SOURCE_KAUFINO;
        } elseif ($this->website->isOferto()) {
            $credentials = Client::CREDENTIALS_OFERTO[$locale] ?? null;
            $this->utmSource = Client::UTM_SOURCE_OFERTO;
        }

        if (!isset($credentials)) {
            $this->sendJson(['status' => 'error', 'message' => 'Invalid locale']);
        }

        $this->client = $this->clientFactory->create($credentials[0], $credentials[1], $this->datadogClient);

        if ($this->getHttpRequest()->getCookie('userId') === null) {
            $userId = $this->getUserUuid();
            $this->getHttpResponse()->setCookie('userId', $userId, '1 year');
        } else {
            $userId = $this->getHttpRequest()->getCookie('userId');
        }

        if ($this->getHttpRequest()->getMethod() !== 'POST') {
            // $this->sendJson(['status' => 'error', 'message' => 'Only POST requests are allowed']);
        }

        $this->userId = $userId;
    }

    public function actionResolveUserLocationFromIp(): void
    {
        $userLocation = $this->getUserLocation();

        $this->setUserLocationCookie($userLocation['latitude'], $userLocation['longitude'], $userLocation['cityId'], $userLocation['strategy']);
        $this->sendUserLocationResponse($userLocation['latitude'], $userLocation['longitude'], $userLocation['cityId'], false, $userLocation['ip']);
    }

    private function setUserLocationCookie($latitude, $longitude, $cityId, $strategy, bool $confirmed = false): void
    {
        $locationData = [
            'latitude' => $latitude,
            'longitude' => $longitude,
            'cityId' => $cityId,
            'strategy' => $strategy,
            'confirmed' => $confirmed
        ];

        $this->getHttpResponse()->setCookie('userLocation', Json::encode($locationData), '10 years', httpOnly: false);
    }

    private function sendUserLocationResponse($latitude, $longitude, $cityId, bool $confirmed = false, ?string $ip = null): void
    {
        $responseData = [
            'status' => '200',
            'latitude' => $latitude,
            'longitude' => $longitude,
            'cityId' => $cityId,
            'confirmed' => $confirmed,
            'ip' => $ip
        ];

        $this->sendJson($responseData);
    }

    public function actionResolveUserLocationFromPosition()
    {
        $body = $this->getRequestBody();
        $lat = $body->latitude ?? null;
        $lon = $body->longitude ?? null;

        if ($lat === null || $lon === null) {
            $this->error('Latitude or longitude is missing');
        }

        $cityId = $this->geoFacade->findClosestCityId($this->website->getLocalization(), $lat, $lon);

        $this->setUserLocationCookie($lat, $lon, $cityId, Client::LOCATION_STRATEGY_DEVICE, true);
        $this->sendUserLocationResponse($lat, $lon, $cityId, true);
    }

    public function actionBrochureImpression()
    {
        $body = $this->getRequestBody();

        if ($body->brochureId === null) {
            $this->error('Brochure click is missing');
        }

        $location = $this->getUserLocation();

        if ($location === null) {
            $this->sendJson(['status' => 'error', 'message' => 'User location not found']);
        }

        $this->client->trackBrochureImpression($this->website, $this->utmSource, $this->userId, $this->getRequestUuid(), $body->brochureId, $location);

        $this->sendJson(['status' => 'ok']);
    }

    public function actionBrochureClick()
    {
        $body = $this->getRequestBody();

        if ($body->brochureId === null) {
            $this->error('Brochure click is missing');
        }

        $location = $this->getUserLocation();

        if ($location === null) {
            $this->sendJson(['status' => 'error', 'message' => 'User location not found']);
        }

        $this->client->trackBrochureClick($this->website, $this->utmSource, $this->userId, $this->getRequestUuid(), $body->brochureId, $location);

        $this->sendJson(['status' => 'ok']);
    }

    public function actionBrochurePageView()
    {
        $body = $this->getRequestBody();

        if ($body->brochureId === null || $body->page === null) {
            $this->error('Brochure page view is missing');
        }

        $location = $this->getUserLocation();

        if ($location === null) {
            $this->sendJson(['status' => 'error', 'message' => 'User location not found']);
        }

        $this->client->trackBrochurePageView($this->website, $this->utmSource, $this->userId, $this->getRequestUuid(), $body->brochureId, $location, $body->page);

        $this->sendJson(['status' => 'ok', 'utmSource' => $this->utmSource]);
    }

    public function actionBrochurePageViewDuration()
    {
        $body = $this->getRequestBody();

        Debugger::log($body, 'offerista-brochure-page-view-duration-body');

        if ($body->duration === null || $body->brochureId === null) {
            $this->error('Brochure page view duration is missing');
        }

        $location = $this->getUserLocation();

        if ($location === null) {
            $this->sendJson(['status' => 'error', 'message' => 'User location not found']);
        }

        $this->client->trackBrochurePageViewDuration($this->website, $this->utmSource, $this->userId, $this->getRequestUuid(), $body->brochureId, $location, $body->page, (float) $body->duration);

        $this->sendJson(['status' => 'ok']);
    }

    private function getRequestBody()
    {
        $body = $this->getHttpRequest()->getRawBody();

        try {
            return Json::decode($body);
        } catch (JsonException $e) {
            Debugger::log($e, 'offerista-json-error');
        }
    }

    private function getRequestUuid(): string
    {
        return sprintf('%08x-%04x-%04x-%04x-%012x',
            time(), // Include current timestamp
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    private function getUserUuid(): string
    {
        return sprintf('%08x-%04x-%04x-%04x-%012x',
            mt_rand(0, 0xffffffff) & 0xffffffff,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff) & 0xffff,
            mt_rand(0, 0xffff) & 0xffff,
            mt_rand(0, 0xffffffffffff) & 0xffffffffffff
        );
    }

    private function getClientIp() {
        if (isset($_SERVER['HTTP_CLIENT_IP']) && !empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        }

        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            // The X-Forwarded-For header may contain a comma-separated list of IPs
            $ipList = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            return trim($ipList[0]); // The first IP is the client's real IP
        }

        if (isset($_SERVER['REMOTE_ADDR'])) {
            return $_SERVER['REMOTE_ADDR'];
        }

        return null;
    }

    private function getUserLocation(): ?array
    {
        $location = $this->getHttpRequest()->getCookie('userLocation');

        if ($location) {
            try {
                return Json::decode($location, Json::FORCE_ARRAY);
            } catch (JsonException $e) {
                $this->getHttpResponse()->deleteCookie('userLocation');
                Debugger::log($e, 'offerista-json-error');
            }
        }

        $ipAddress = $this->getClientIp();

        if ($ipAddress) {
            $locationData = $this->client->findUserLocationByIp($ipAddress);

            if ($locationData) {
                $cityId = $this->geoFacade->findClosestCityId($this->website->getLocalization(), $locationData->latitude, $locationData->longitude);

                if ($cityId) {
                    return [
                        'latitude' => $locationData->latitude,
                        'longitude' => $locationData->longitude,
                        'cityId' => $cityId,
                        'strategy' => Client::LOCATION_STRATEGY_IP,
                        'ip' => $ipAddress,
                    ];
                }
            }
        }

        $fallbackCity = $this->geoFacade->findTopCityByPopulation($this->website->getLocalization());

        return [
            'latitude' => $fallbackCity->getLat(),
            'longitude' => $fallbackCity->getLng(),
            'cityId' => $fallbackCity->getId(),
            'strategy' => Client::LOCATION_STRATEGY_FALLBACK,
            'ip' => $ipAddress,
        ];
    }

    public function actionClickout(string $target, int $brochureId, int $page)
    {
        $this->client->trackBrochureClickout($this->website, $this->utmSource, $this->userId, $this->getRequestUuid(), $brochureId, $this->getUserLocation(), $target, $page);

        $this->redirectUrl($target);
    }
}
