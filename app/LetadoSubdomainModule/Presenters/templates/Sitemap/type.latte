{contentType application/xml}
{('<' . '?xml version="1.0" encoding="UTF-8"?>') |noescape}

<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
    <url n:foreach="$feed as $item">
        <loc>{$item->getUrl()}</loc>
        <changefreq n:if="$item->getFrequency()">{$item->getFrequency()}</changefreq>
        <lastmod n:if="$item->getLastModified() && $item->getLastModified() instanceOf \DateTime">{$item->getLastModified()|date: '%Y-%m-%d'}</lastmod>
        <priority n:if="$item->getPriority()">{$item->getPriority()}</priority>
    </url>
</urlset>