{block scripts}
    {include parent}
{/block}

{block description}{_"$websiteType.homepage.text"}{/block}

{block content}

<div class="container">
    <div class="mb-6">
        <h1 n:block="title" class="k__title ta-center mt-5 mb-4">{_"$websiteType.homepage.title"}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.homepage.text"}</p>
    </div>

    <div class="k-leaflets__wrapper  k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            <div class="k-leaflets__item mb-5">                
                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                    <picture n:if="$leaflet->getFirstPage()">
                        <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                    </picture>                    
                </a>
                <div class="k-leaflets__title mt-0 mb-0">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                        {$leaflet->getName()}
                    </a>
                </div>                
                <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
            </div>            
        {/foreach}
    </div>

    <div class="ta-center">
        <a href="{link Leaflets:leaflets}" class="color-grey td-hover-underline">
            {_"$websiteType.homepage.allLeaflets"}
        </a>
    </div>

    <div class="mb-4">
        <h2 class="k__title ta-center">
            {_"$websiteType.homepage.shops"}
        </h2>     
    </div>

    <div class="k-shop">    
        {foreach $shops as $shop}
            <a n:href="Shop:shop $shop" class="k-shop__item">
                <span class="k-shop__image-wrapper">
                    <picture>
                        <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                    </picture>                    
                </span>
                <small class="k-shop__title">{$shop->getName()}</small>
            </a>
        {/foreach}
    </div>

    <div class="ta-center mb-5">
        <a href="{link Shops:shops}" class="color-grey td-hover-underline">
            {_"$websiteType.homepage.allShops"}
        </a>
    </div>     
</div>