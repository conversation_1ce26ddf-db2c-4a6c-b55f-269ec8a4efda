<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class TagPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	public function actionTag(Tag $tag): void
	{
		if ($tag->isShopsType()) {
			$this->prepareTagShops($tag);
		}

		if ($tag->isOffersType()) {
			$this->prepareTagOffers($tag);
		}

		$this->responseCacheTags[] = 'tag/' . $tag->getId();

		$this->template->tag = $tag;
	}

	private function prepareTagShops(Tag $tag): void
	{
		$this->setView('tagShops');

        $shops = $this->shopFacade->findLeafletShopsByTag($tag, true, 100, Website::MODULE_LETADO);
        $leaflets = $this->leafletFacade->findLeafletsByTag($tag, false, 60, Website::MODULE_LETADO);

        if (count($leaflets) < 60) {
            $leaflets = array_merge($leaflets, $this->leafletFacade->findLeafletsByShops($shops, 60, true, $leaflets, Website::MODULE_LETADO));
        }

        $this->template->leaflets = array_slice($leaflets, 0, 60);
        $this->template->shops = $shops;
	}

	private function prepareTagOffers(Tag $tag): void
	{
		$this->setView('tagOffers');

		$this->template->leafletPages = $this->leafletFacade->findLeafletPagesByFulltext($tag->getMatchRule(), $tag->getLocalization());
		$this->template->offers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), true);
		$this->template->expiredOffers = $this->offerFacade->findOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), false);
	}
}
