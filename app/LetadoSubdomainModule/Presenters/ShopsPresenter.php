<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoSubdomainModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class ShopsPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	public function actionShops(): void
	{
		$this->responseCacheTags[] = 'shops';

		$this->template->shops = $this->shopFacade->findTopLeafletShops($this->localization, true, null, Website::MODULE_LETADO);
	}
}
