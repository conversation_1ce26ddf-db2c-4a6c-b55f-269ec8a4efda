/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./**/*.latte', './www/js/**/*.{html,js}'],
  theme: {
    container: {
      center: true,
      screens: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1200px',
        '2xl': '1240px',
      },
      padding: '20px'
    },
    extend: {
      colors: {
        primary: {
          DEFAULT: '#BC2026',
          hover: '#A31C22',
          'light-hover': '#ffebec'
        },
        /*primary: {
          DEFAULT: '#2BB673',
          hover: '#259D63',
          'light-hover': '#E0F5EA'
        },*/
        green: '#5DBD20',
        orange: '#F8992A',
        'green-light': 'rgba(93, 189, 32, 0.20);',
        'orange-light': 'rgba(248, 153, 42, 0.20);',
        'dark-grey': "#b3b3b3",
        grey: "#d9d9d9",
        'grey-description': "#646C7C",
        light: {
          1: '#ADB3BF',
          2: '#CCD0D7',
          3: '#D7DBE0',
          4: '#E1E4E8',
          5: '#ECEDF0',
          6: '#F4F4F6',
        },
      },
      boxShadow: {
        'sm': '0 3px 10px rgba(0, 0, 0, 0.102)',
      },
    },
  },
  plugins: [],
}
