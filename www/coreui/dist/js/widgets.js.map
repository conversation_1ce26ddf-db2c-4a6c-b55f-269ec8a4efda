{"version": 3, "sources": ["src/widgets.js"], "names": ["Chart", "defaults", "global", "pointHitDetectionRadius", "tooltips", "enabled", "mode", "position", "custom", "CustomTooltips", "cardChart1", "$", "type", "data", "labels", "datasets", "label", "backgroundColor", "getStyle", "borderColor", "options", "maintainAspectRatio", "legend", "display", "scales", "xAxes", "gridLines", "color", "zeroLineColor", "ticks", "fontSize", "fontColor", "yAxes", "min", "max", "elements", "line", "borderWidth", "point", "radius", "hitRadius", "hoverRadius", "cardChart2", "tension", "cardChart3", "cardChart4", "barPercentage", "random", "Math", "floor", "sparklineChart1", "sparklineChart2", "sparklineChart3", "sparklineChart4", "sparklineChart5", "sparklineChart6", "brandBoxChartLabels", "brandBoxChartOptions", "responsive", "hoverBorderWidth", "brandBoxChart1", "pointHoverBackgroundColor", "brandBoxChart2", "brandBoxChart3", "brandBoxChart4"], "mappings": "AAAA;;AAGA;;;;;;;AAOA;AACA;AACAA,KAAK,CAACC,QAAN,CAAeC,MAAf,CAAsBC,uBAAtB,GAAgD,CAAhD;AACAH,KAAK,CAACC,QAAN,CAAeC,MAAf,CAAsBE,QAAtB,CAA+BC,OAA/B,GAAyC,KAAzC;AACAL,KAAK,CAACC,QAAN,CAAeC,MAAf,CAAsBE,QAAtB,CAA+BE,IAA/B,GAAsC,OAAtC;AACAN,KAAK,CAACC,QAAN,CAAeC,MAAf,CAAsBE,QAAtB,CAA+BG,QAA/B,GAA0C,SAA1C;AACAP,KAAK,CAACC,QAAN,CAAeC,MAAf,CAAsBE,QAAtB,CAA+BI,MAA/B,GAAwCC,cAAxC,C,CAEA;;AACA,IAAMC,UAAU,GAAG,IAAIV,KAAJ,CAAUW,CAAC,CAAC,cAAD,CAAX,EAA6B;AAC9CC,EAAAA,IAAI,EAAE,MADwC;AAE9CC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEC,MAAAA,KAAK,EAAE,kBADT;AAEEC,MAAAA,eAAe,EAAEC,QAAQ,CAAC,WAAD,CAF3B;AAGEC,MAAAA,WAAW,EAAE,uBAHf;AAIEN,MAAAA,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;AAJR,KADQ;AAFN,GAFwC;AAa9CO,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNC,QAAAA,SAAS,EAAE;AACTC,UAAAA,KAAK,EAAE,aADE;AAETC,UAAAA,aAAa,EAAE;AAFN,SADL;AAKNC,QAAAA,KAAK,EAAE;AACLC,UAAAA,QAAQ,EAAE,CADL;AAELC,UAAAA,SAAS,EAAE;AAFN;AALD,OAAD,CADD;AAWNC,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE,KADH;AAENM,QAAAA,KAAK,EAAE;AACLN,UAAAA,OAAO,EAAE,KADJ;AAELU,UAAAA,GAAG,EAAE,EAFA;AAGLC,UAAAA,GAAG,EAAE;AAHA;AAFD,OAAD;AAXD,KALD;AAyBPC,IAAAA,QAAQ,EAAE;AACRC,MAAAA,IAAI,EAAE;AACJC,QAAAA,WAAW,EAAE;AADT,OADE;AAIRC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,CADH;AAELC,QAAAA,SAAS,EAAE,EAFN;AAGLC,QAAAA,WAAW,EAAE;AAHR;AAJC;AAzBH;AAbqC,CAA7B,CAAnB,C,CAmDA;;AACA,IAAMC,UAAU,GAAG,IAAI1C,KAAJ,CAAUW,CAAC,CAAC,cAAD,CAAX,EAA6B;AAC9CC,EAAAA,IAAI,EAAE,MADwC;AAE9CC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEC,MAAAA,KAAK,EAAE,kBADT;AAEEC,MAAAA,eAAe,EAAEC,QAAQ,CAAC,QAAD,CAF3B;AAGEC,MAAAA,WAAW,EAAE,uBAHf;AAIEN,MAAAA,IAAI,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,EAAW,EAAX,EAAe,EAAf,EAAmB,EAAnB,EAAuB,EAAvB;AAJR,KADQ;AAFN,GAFwC;AAa9CO,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNC,QAAAA,SAAS,EAAE;AACTC,UAAAA,KAAK,EAAE,aADE;AAETC,UAAAA,aAAa,EAAE;AAFN,SADL;AAKNC,QAAAA,KAAK,EAAE;AACLC,UAAAA,QAAQ,EAAE,CADL;AAELC,UAAAA,SAAS,EAAE;AAFN;AALD,OAAD,CADD;AAWNC,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE,KADH;AAENM,QAAAA,KAAK,EAAE;AACLN,UAAAA,OAAO,EAAE,KADJ;AAELU,UAAAA,GAAG,EAAE,CAAC,CAFD;AAGLC,UAAAA,GAAG,EAAE;AAHA;AAFD,OAAD;AAXD,KALD;AAyBPC,IAAAA,QAAQ,EAAE;AACRC,MAAAA,IAAI,EAAE;AACJO,QAAAA,OAAO,EAAE,OADL;AAEJN,QAAAA,WAAW,EAAE;AAFT,OADE;AAKRC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,CADH;AAELC,QAAAA,SAAS,EAAE,EAFN;AAGLC,QAAAA,WAAW,EAAE;AAHR;AALC;AAzBH;AAbqC,CAA7B,CAAnB,C,CAoDA;;AACA,IAAMG,UAAU,GAAG,IAAI5C,KAAJ,CAAUW,CAAC,CAAC,cAAD,CAAX,EAA6B;AAC9CC,EAAAA,IAAI,EAAE,MADwC;AAE9CC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEC,MAAAA,KAAK,EAAE,kBADT;AAEEC,MAAAA,eAAe,EAAE,sBAFnB;AAGEE,MAAAA,WAAW,EAAE,uBAHf;AAIEN,MAAAA,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;AAJR,KADQ;AAFN,GAFwC;AAa9CO,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD,KALD;AAaPY,IAAAA,QAAQ,EAAE;AACRC,MAAAA,IAAI,EAAE;AACJC,QAAAA,WAAW,EAAE;AADT,OADE;AAIRC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,CADH;AAELC,QAAAA,SAAS,EAAE,EAFN;AAGLC,QAAAA,WAAW,EAAE;AAHR;AAJC;AAbH;AAbqC,CAA7B,CAAnB,C,CAuCA;;AACA,IAAMI,UAAU,GAAG,IAAI7C,KAAJ,CAAUW,CAAC,CAAC,cAAD,CAAX,EAA6B;AAC9CC,EAAAA,IAAI,EAAE,KADwC;AAE9CC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,EAAiE,QAAjE,EAA2E,WAA3E,EAAwF,SAAxF,EAAmG,UAAnG,EAA+G,UAA/G,EAA2H,SAA3H,EAAsI,UAAtI,EAAkJ,OAAlJ,EAA2J,OAA3J,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEC,MAAAA,KAAK,EAAE,kBADT;AAEEC,MAAAA,eAAe,EAAE,sBAFnB;AAGEE,MAAAA,WAAW,EAAE,uBAHf;AAIEN,MAAAA,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,EAA6B,EAA7B,EAAiC,EAAjC,EAAqC,EAArC,EAAyC,EAAzC,EAA6C,EAA7C,EAAiD,EAAjD,EAAqD,EAArD,EAAyD,EAAzD,EAA6D,EAA7D;AAJR,KADQ;AAFN,GAFwC;AAa9CO,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE,KADH;AAENuB,QAAAA,aAAa,EAAE;AAFT,OAAD,CADD;AAKNd,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AALD;AALD;AAbqC,CAA7B,CAAnB,C,CA8BA;;AACA,IAAMwB,MAAM,GAAG,SAATA,MAAS,CAACd,GAAD,EAAMC,GAAN;AAAA,SAAcc,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACD,MAAL,MAAiBb,GAAG,GAAGD,GAAN,GAAY,CAA7B,IAAkCA,GAA7C,CAAd;AAAA,CAAf,C,CAEA;;;AACA,IAAMiB,eAAe,GAAG,IAAIlD,KAAJ,CAAUW,CAAC,CAAC,oBAAD,CAAX,EAAmC;AACzDC,EAAAA,IAAI,EAAE,KADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,GAAxD,EAA6D,GAA7D,EAAkE,GAAlE,EAAuE,GAAvE,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEE,MAAAA,eAAe,EAAEC,QAAQ,CAAC,WAAD,CAD3B;AAEEC,MAAAA,WAAW,EAAE,aAFf;AAGEkB,MAAAA,WAAW,EAAE,CAHf;AAIExB,MAAAA,IAAI,EAAE,CAACkC,MAAM,CAAC,EAAD,EAAK,GAAL,CAAP,EAAkBA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAxB,EAAmCA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAzC,EAAoDA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA1D,EAAqEA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA3E,EAAsFA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA5F,EAAuGA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA7G,EAAwHA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA9H,EAAyIA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA/I,EAA0JA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAhK,EAA2KA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAjL,EAA4LA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAlM,EAA6MA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAnN,EAA8NA,MAAM,CAAC,EAAD,EAAK,GAAL,CAApO,EAA+OA,MAAM,CAAC,EAAD,EAAK,GAAL,CAArP;AAJR,KADQ;AAFN,GAFmD;AAazD3B,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD;AALD;AAbgD,CAAnC,CAAxB,C,CA6BA;;AACA,IAAM4B,eAAe,GAAG,IAAInD,KAAJ,CAAUW,CAAC,CAAC,oBAAD,CAAX,EAAmC;AACzDC,EAAAA,IAAI,EAAE,KADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,GAAxD,EAA6D,GAA7D,EAAkE,GAAlE,EAAuE,GAAvE,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEE,MAAAA,eAAe,EAAEC,QAAQ,CAAC,WAAD,CAD3B;AAEEC,MAAAA,WAAW,EAAE,aAFf;AAGEkB,MAAAA,WAAW,EAAE,CAHf;AAIExB,MAAAA,IAAI,EAAE,CAACkC,MAAM,CAAC,EAAD,EAAK,GAAL,CAAP,EAAkBA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAxB,EAAmCA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAzC,EAAoDA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA1D,EAAqEA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA3E,EAAsFA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA5F,EAAuGA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA7G,EAAwHA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA9H,EAAyIA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA/I,EAA0JA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAhK,EAA2KA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAjL,EAA4LA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAlM,EAA6MA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAnN,EAA8NA,MAAM,CAAC,EAAD,EAAK,GAAL,CAApO,EAA+OA,MAAM,CAAC,EAAD,EAAK,GAAL,CAArP;AAJR,KADQ;AAFN,GAFmD;AAazD3B,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD;AALD;AAbgD,CAAnC,CAAxB,C,CA6BA;;AACA,IAAM6B,eAAe,GAAG,IAAIpD,KAAJ,CAAUW,CAAC,CAAC,oBAAD,CAAX,EAAmC;AACzDC,EAAAA,IAAI,EAAE,KADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,GAAxD,EAA6D,GAA7D,EAAkE,GAAlE,EAAuE,GAAvE,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEE,MAAAA,eAAe,EAAEC,QAAQ,CAAC,WAAD,CAD3B;AAEEC,MAAAA,WAAW,EAAE,aAFf;AAGEkB,MAAAA,WAAW,EAAE,CAHf;AAIExB,MAAAA,IAAI,EAAE,CAACkC,MAAM,CAAC,EAAD,EAAK,GAAL,CAAP,EAAkBA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAxB,EAAmCA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAzC,EAAoDA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA1D,EAAqEA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA3E,EAAsFA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA5F,EAAuGA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA7G,EAAwHA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA9H,EAAyIA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA/I,EAA0JA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAhK,EAA2KA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAjL,EAA4LA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAlM,EAA6MA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAnN,EAA8NA,MAAM,CAAC,EAAD,EAAK,GAAL,CAApO,EAA+OA,MAAM,CAAC,EAAD,EAAK,GAAL,CAArP;AAJR,KADQ;AAFN,GAFmD;AAazD3B,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD;AALD;AAbgD,CAAnC,CAAxB,C,CA6BA;;AACA,IAAM8B,eAAe,GAAG,IAAIrD,KAAJ,CAAUW,CAAC,CAAC,oBAAD,CAAX,EAAmC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEE,MAAAA,eAAe,EAAE,aADnB;AAEEE,MAAAA,WAAW,EAAED,QAAQ,CAAC,QAAD,CAFvB;AAGEmB,MAAAA,WAAW,EAAE,CAHf;AAIExB,MAAAA,IAAI,EAAE,CAACkC,MAAM,CAAC,EAAD,EAAK,GAAL,CAAP,EAAkBA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAxB,EAAmCA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAzC,EAAoDA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA1D,EAAqEA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA3E,EAAsFA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA5F,EAAuGA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA7G;AAJR,KADQ;AAFN,GAFmD;AAazD3B,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD,KALD;AAaPY,IAAAA,QAAQ,EAAE;AACRG,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE;AADH;AADC;AAbH;AAbgD,CAAnC,CAAxB,C,CAkCA;;AACA,IAAMe,eAAe,GAAG,IAAItD,KAAJ,CAAUW,CAAC,CAAC,oBAAD,CAAX,EAAmC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEE,MAAAA,eAAe,EAAE,aADnB;AAEEE,MAAAA,WAAW,EAAED,QAAQ,CAAC,WAAD,CAFvB;AAGEmB,MAAAA,WAAW,EAAE,CAHf;AAIExB,MAAAA,IAAI,EAAE,CAACkC,MAAM,CAAC,EAAD,EAAK,GAAL,CAAP,EAAkBA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAxB,EAAmCA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAzC,EAAoDA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA1D,EAAqEA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA3E,EAAsFA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA5F,EAAuGA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA7G;AAJR,KADQ;AAFN,GAFmD;AAazD3B,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD,KALD;AAaPY,IAAAA,QAAQ,EAAE;AACRG,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE;AADH;AADC;AAbH;AAbgD,CAAnC,CAAxB,C,CAkCA;;AACA,IAAMgB,eAAe,GAAG,IAAIvD,KAAJ,CAAUW,CAAC,CAAC,oBAAD,CAAX,EAAmC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,CADJ;AAEJC,IAAAA,QAAQ,EAAE,CACR;AACEE,MAAAA,eAAe,EAAE,aADnB;AAEEE,MAAAA,WAAW,EAAED,QAAQ,CAAC,UAAD,CAFvB;AAGEmB,MAAAA,WAAW,EAAE,CAHf;AAIExB,MAAAA,IAAI,EAAE,CAACkC,MAAM,CAAC,EAAD,EAAK,GAAL,CAAP,EAAkBA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAxB,EAAmCA,MAAM,CAAC,EAAD,EAAK,GAAL,CAAzC,EAAoDA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA1D,EAAqEA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA3E,EAAsFA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA5F,EAAuGA,MAAM,CAAC,EAAD,EAAK,GAAL,CAA7G;AAJR,KADQ;AAFN,GAFmD;AAazD3B,EAAAA,OAAO,EAAE;AACPC,IAAAA,mBAAmB,EAAE,KADd;AAEPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,OAAO,EAAE;AADH,KAFD;AAKPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,KAAK,EAAE,CAAC;AACNF,QAAAA,OAAO,EAAE;AADH,OAAD,CADD;AAINS,MAAAA,KAAK,EAAE,CAAC;AACNT,QAAAA,OAAO,EAAE;AADH,OAAD;AAJD,KALD;AAaPY,IAAAA,QAAQ,EAAE;AACRG,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE;AADH;AADC;AAbH;AAbgD,CAAnC,CAAxB;AAkCA,IAAMiB,mBAAmB,GAAG,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CAA5B;AACA,IAAMC,oBAAoB,GAAG;AAC3BC,EAAAA,UAAU,EAAE,IADe;AAE3BrC,EAAAA,mBAAmB,EAAE,KAFM;AAG3BC,EAAAA,MAAM,EAAE;AACNC,IAAAA,OAAO,EAAE;AADH,GAHmB;AAM3BC,EAAAA,MAAM,EAAE;AACNC,IAAAA,KAAK,EAAE,CAAC;AACNF,MAAAA,OAAO,EAAC;AADF,KAAD,CADD;AAINS,IAAAA,KAAK,EAAE,CAAC;AACNT,MAAAA,OAAO,EAAC;AADF,KAAD;AAJD,GANmB;AAc3BY,EAAAA,QAAQ,EAAE;AACRG,IAAAA,KAAK,EAAE;AACLC,MAAAA,MAAM,EAAE,CADH;AAELC,MAAAA,SAAS,EAAE,EAFN;AAGLC,MAAAA,WAAW,EAAE,CAHR;AAILkB,MAAAA,gBAAgB,EAAE;AAJb;AADC,GAdiB,CAwB7B;;AAxB6B,CAA7B;AAyBA,IAAMC,cAAc,GAAG,IAAI5D,KAAJ,CAAUW,CAAC,CAAC,qBAAD,CAAX,EAAoC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE0C,mBADJ;AAEJzC,IAAAA,QAAQ,EAAE,CAAC;AACTE,MAAAA,eAAe,EAAE,sBADR;AAETE,MAAAA,WAAW,EAAE,uBAFJ;AAGT0C,MAAAA,yBAAyB,EAAE,MAHlB;AAITxB,MAAAA,WAAW,EAAE,CAJJ;AAKTxB,MAAAA,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;AALG,KAAD;AAFN,GAFmD;AAYzDO,EAAAA,OAAO,EAAEqC;AAZgD,CAApC,CAAvB,C,CAeA;;AACA,IAAMK,cAAc,GAAG,IAAI9D,KAAJ,CAAUW,CAAC,CAAC,qBAAD,CAAX,EAAoC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE0C,mBADJ;AAEJzC,IAAAA,QAAQ,EAAE,CAAC;AACTE,MAAAA,eAAe,EAAE,sBADR;AAETE,MAAAA,WAAW,EAAE,uBAFJ;AAGT0C,MAAAA,yBAAyB,EAAE,MAHlB;AAITxB,MAAAA,WAAW,EAAE,CAJJ;AAKTxB,MAAAA,IAAI,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,EAAW,EAAX,EAAe,EAAf,EAAmB,EAAnB,EAAuB,EAAvB;AALG,KAAD;AAFN,GAFmD;AAYzDO,EAAAA,OAAO,EAAEqC;AAZgD,CAApC,CAAvB,C,CAeA;;AACA,IAAMM,cAAc,GAAG,IAAI/D,KAAJ,CAAUW,CAAC,CAAC,qBAAD,CAAX,EAAoC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE0C,mBADJ;AAEJzC,IAAAA,QAAQ,EAAE,CAAC;AACTE,MAAAA,eAAe,EAAE,sBADR;AAETE,MAAAA,WAAW,EAAE,uBAFJ;AAGT0C,MAAAA,yBAAyB,EAAE,MAHlB;AAITxB,MAAAA,WAAW,EAAE,CAJJ;AAKTxB,MAAAA,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;AALG,KAAD;AAFN,GAFmD;AAYzDO,EAAAA,OAAO,EAAEqC;AAZgD,CAApC,CAAvB,C,CAeA;;AACA,IAAMO,cAAc,GAAG,IAAIhE,KAAJ,CAAUW,CAAC,CAAC,qBAAD,CAAX,EAAoC;AACzDC,EAAAA,IAAI,EAAE,MADmD;AAEzDC,EAAAA,IAAI,EAAE;AACJC,IAAAA,MAAM,EAAE0C,mBADJ;AAEJzC,IAAAA,QAAQ,EAAE,CAAC;AACTE,MAAAA,eAAe,EAAE,sBADR;AAETE,MAAAA,WAAW,EAAE,uBAFJ;AAGT0C,MAAAA,yBAAyB,EAAE,MAHlB;AAITxB,MAAAA,WAAW,EAAE,CAJJ;AAKTxB,MAAAA,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;AALG,KAAD;AAFN,GAFmD;AAYzDO,EAAAA,OAAO,EAAEqC;AAZgD,CAApC,CAAvB", "sourcesContent": ["/* global Chart, CustomTooltips, getStyle */\nimport $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * CoreUI Free Boostrap Admin Template (v2.1.11): main.js\n * Licensed under MIT (https://coreui.io/license)\n * --------------------------------------------------------------------------\n */\n\n/* eslint-disable no-magic-numbers */\n// Disable the on-canvas tooltip\nChart.defaults.global.pointHitDetectionRadius = 1\nChart.defaults.global.tooltips.enabled = false\nChart.defaults.global.tooltips.mode = 'index'\nChart.defaults.global.tooltips.position = 'nearest'\nChart.defaults.global.tooltips.custom = CustomTooltips\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart1 = new Chart($('#card-chart1'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: getStyle('--primary'),\n        borderColor: 'rgba(255,255,255,.55)',\n        data: [65, 59, 84, 84, 51, 55, 40]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        gridLines: {\n          color: 'transparent',\n          zeroLineColor: 'transparent'\n        },\n        ticks: {\n          fontSize: 2,\n          fontColor: 'transparent'\n        }\n      }],\n      yAxes: [{\n        display: false,\n        ticks: {\n          display: false,\n          min: 35,\n          max: 89\n        }\n      }]\n    },\n    elements: {\n      line: {\n        borderWidth: 1\n      },\n      point: {\n        radius: 4,\n        hitRadius: 10,\n        hoverRadius: 4\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart2 = new Chart($('#card-chart2'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: getStyle('--info'),\n        borderColor: 'rgba(255,255,255,.55)',\n        data: [1, 18, 9, 17, 34, 22, 11]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        gridLines: {\n          color: 'transparent',\n          zeroLineColor: 'transparent'\n        },\n        ticks: {\n          fontSize: 2,\n          fontColor: 'transparent'\n        }\n      }],\n      yAxes: [{\n        display: false,\n        ticks: {\n          display: false,\n          min: -4,\n          max: 39\n        }\n      }]\n    },\n    elements: {\n      line: {\n        tension: 0.00001,\n        borderWidth: 1\n      },\n      point: {\n        radius: 4,\n        hitRadius: 10,\n        hoverRadius: 4\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart3 = new Chart($('#card-chart3'), {\n  type: 'line',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'rgba(255,255,255,.2)',\n        borderColor: 'rgba(255,255,255,.55)',\n        data: [78, 81, 80, 45, 34, 12, 40]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    },\n    elements: {\n      line: {\n        borderWidth: 2\n      },\n      point: {\n        radius: 0,\n        hitRadius: 10,\n        hoverRadius: 4\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst cardChart4 = new Chart($('#card-chart4'), {\n  type: 'bar',\n  data: {\n    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December', 'January', 'February', 'March', 'April'],\n    datasets: [\n      {\n        label: 'My First dataset',\n        backgroundColor: 'rgba(255,255,255,.2)',\n        borderColor: 'rgba(255,255,255,.55)',\n        data: [78, 81, 80, 45, 34, 12, 40, 85, 65, 23, 12, 98, 34, 84, 67, 82]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false,\n        barPercentage: 0.6\n      }],\n      yAxes: [{\n        display: false\n      }]\n    }\n  }\n})\n\n// Random Numbers\nconst random = (min, max) => Math.floor(Math.random() * (max - min + 1) + min)\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart1 = new Chart($('#sparkline-chart-1'), {\n  type: 'bar',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n    datasets: [\n      {\n        backgroundColor: getStyle('--primary'),\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart2 = new Chart($('#sparkline-chart-2'), {\n  type: 'bar',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n    datasets: [\n      {\n        backgroundColor: getStyle('--warning'),\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart3 = new Chart($('#sparkline-chart-3'), {\n  type: 'bar',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n    datasets: [\n      {\n        backgroundColor: getStyle('--success'),\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart4 = new Chart($('#sparkline-chart-4'), {\n  type: 'line',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],\n    datasets: [\n      {\n        backgroundColor: 'transparent',\n        borderColor: getStyle('--info'),\n        borderWidth: 2,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    },\n    elements: {\n      point: {\n        radius: 0\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart5 = new Chart($('#sparkline-chart-5'), {\n  type: 'line',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],\n    datasets: [\n      {\n        backgroundColor: 'transparent',\n        borderColor: getStyle('--success'),\n        borderWidth: 2,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    },\n    elements: {\n      point: {\n        radius: 0\n      }\n    }\n  }\n})\n\n// eslint-disable-next-line no-unused-vars\nconst sparklineChart6 = new Chart($('#sparkline-chart-6'), {\n  type: 'line',\n  data: {\n    labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S'],\n    datasets: [\n      {\n        backgroundColor: 'transparent',\n        borderColor: getStyle('--danger'),\n        borderWidth: 2,\n        data: [random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100), random(40, 100)]\n      }\n    ]\n  },\n  options: {\n    maintainAspectRatio: false,\n    legend: {\n      display: false\n    },\n    scales: {\n      xAxes: [{\n        display: false\n      }],\n      yAxes: [{\n        display: false\n      }]\n    },\n    elements: {\n      point: {\n        radius: 0\n      }\n    }\n  }\n})\n\nconst brandBoxChartLabels = ['January', 'February', 'March', 'April', 'May', 'June', 'July']\nconst brandBoxChartOptions = {\n  responsive: true,\n  maintainAspectRatio: false,\n  legend: {\n    display: false\n  },\n  scales: {\n    xAxes: [{\n      display:false\n    }],\n    yAxes: [{\n      display:false\n    }]\n  },\n  elements: {\n    point: {\n      radius: 0,\n      hitRadius: 10,\n      hoverRadius: 4,\n      hoverBorderWidth: 3\n    }\n  }\n}\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart1 = new Chart($('#social-box-chart-1'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [65, 59, 84, 84, 51, 55, 40]\n    }]\n  },\n  options: brandBoxChartOptions\n})\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart2 = new Chart($('#social-box-chart-2'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [1, 13, 9, 17, 34, 41, 38]\n    }]\n  },\n  options: brandBoxChartOptions\n})\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart3 = new Chart($('#social-box-chart-3'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [78, 81, 80, 45, 34, 12, 40]\n    }]\n  },\n  options: brandBoxChartOptions\n})\n\n// eslint-disable-next-line no-unused-vars\nconst brandBoxChart4 = new Chart($('#social-box-chart-4'), {\n  type: 'line',\n  data: {\n    labels: brandBoxChartLabels,\n    datasets: [{\n      backgroundColor: 'rgba(255,255,255,.1)',\n      borderColor: 'rgba(255,255,255,.55)',\n      pointHoverBackgroundColor: '#fff',\n      borderWidth: 2,\n      data: [35, 23, 56, 22, 97, 23, 64]\n    }]\n  },\n  options: brandBoxChartOptions\n})\n"], "file": "widgets.js"}