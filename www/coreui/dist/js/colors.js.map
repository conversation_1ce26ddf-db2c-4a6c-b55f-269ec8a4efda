{"version": 3, "sources": ["src/colors.js"], "names": ["$", "each", "Color", "css", "parent", "append", "rgbToHex"], "mappings": "AAAA;;AAGA;;;;;;AAOAA,CAAC,CAAC,cAAD,CAAD,CAAkBC,IAAlB,CAAuB,YAAY;AACjC,MAAMC,KAAK,GAAGF,CAAC,CAAC,IAAD,CAAD,CAAQG,GAAR,CAAY,iBAAZ,CAAd;AACAH,EAAAA,CAAC,CAAC,IAAD,CAAD,CAAQI,MAAR,GAAiBC,MAAjB,oIAIqCC,QAAQ,CAACJ,KAAD,CAJ7C,2HAQqCA,KARrC;AAYD,CAdD", "sourcesContent": ["/* global rgbToHex */\nimport $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * CoreUI Free Boostrap Admin Template (v2.1.11): colors.js\n * Licensed under MIT (https://coreui.io/license)\n * --------------------------------------------------------------------------\n */\n\n$('.theme-color').each(function () {\n  const Color = $(this).css('backgroundColor')\n  $(this).parent().append(`\n    <table class=\"w-100\">\n      <tr>\n        <td class=\"text-muted\">HEX:</td>\n        <td class=\"font-weight-bold\">${rgbToHex(Color)}</td>\n      </tr>\n      <tr>\n        <td class=\"text-muted\">RGB:</td>\n        <td class=\"font-weight-bold\">${Color}</td>\n      </tr>\n    </table>\n  `)\n})\n"], "file": "colors.js"}