function CustomTooltips(s){var e,t,a=this,o="above",n="below",l="chartjs-tooltip",i="no-transform",c="tooltip-body",r="tooltip-body-item",d="tooltip-body-item-color",p="tooltip-body-item-label",m="tooltip-body-item-value",h="tooltip-header",u="tooltip-header-item",v={DIV:"div",SPAN:"span",TOOLTIP:(this._chart.canvas.id||(e=function(){return(65536*(1+Math.random())|0).toString(16)},t="_canvas-"+(e()+e()),a._chart.canvas.id=t))+"-tooltip"},y=document.getElementById(v.TOOLTIP);if(y||((y=document.createElement("div")).id=v.TOOLTIP,y.className=l,this._chart.canvas.parentNode.appendChild(y)),0!==s.opacity){if(y.classList.remove(o,n,i),s.yAlign?y.classList.add(s.yAlign):y.classList.add(i),s.body){var f=s.title||[],N=document.createElement(v.DIV);N.className=h,f.forEach(function(e){var t=document.createElement(v.DIV);t.className=u,t.innerHTML=e,N.appendChild(t)});var b=document.createElement(v.DIV);b.className=c,s.body.map(function(e){return e.lines}).forEach(function(e,t){var a=document.createElement(v.DIV);a.className=r;var o=s.labelColors[t],n=document.createElement(v.SPAN);if(n.className=d,n.style.backgroundColor=o.backgroundColor,a.appendChild(n),1<e[0].split(":").length){var l=document.createElement(v.SPAN);l.className=p,l.innerHTML=e[0].split(": ")[0],a.appendChild(l);var i=document.createElement(v.SPAN);i.className=m,i.innerHTML=e[0].split(": ").pop(),a.appendChild(i)}else{var c=document.createElement(v.SPAN);c.className=m,c.innerHTML=e[0],a.appendChild(c)}b.appendChild(a)}),y.innerHTML="",y.appendChild(N),y.appendChild(b)}var C=this._chart.canvas.offsetTop,T=this._chart.canvas.offsetLeft;y.style.opacity=1,y.style.left=T+s.caretX+"px",y.style.top=C+s.caretY+"px"}else y.style.opacity=0}
//# sourceMappingURL=custom-tooltips.min.js.map