// _vars.scss
@use "sass:map";

$themes: (
  front: (
    color-primary: #bc2026    
  ),
  letado: (
    color-primary: #009e74
  ),
  oferto: (
    color-primary: #2bb673    
  )
) !default;

$current-theme: null !default;

@function get-theme-value($key) {
  @if $current-theme == null {
    @error "Není nastaveno current-theme";
  }
  @return map.get(map.get($themes, $current-theme), $key);
}

// Exportované proměnné
$color-primary: get-theme-value('color-primary');
$color-secondary: get-theme-value('color-secondary');

$img-dir: "../images/"; // define the base path

// Base colors
// -------------------------

$color-grey: #2c2c2c;

// Screen sizes
// -------------------------

$phone: 320px;
$phone-md: 375px;
$phone--land: 481px;
$tablet: 768px;
$tablet--land: 1024px;
$desktop: 1180px;
$desktop--land: 1600px;

$min-lg: 1200px;
$min-xl: 1510px;
