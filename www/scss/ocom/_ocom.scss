@use "sass:color";
@use "tools";
@use "vars";

.k-content p,
.k-content li,
.k__text, 
.k__text p {
    @include tools.breakpoint(vars.$tablet) {
        font-size: 16px;
    }
}

.ocom-coupon__wrapper {
    display: flex;    
    flex-wrap: wrap;
}

.ocom-coupon__wrapper .k-coupon {    
    flex: 1 1 auto;
    grid-template-columns: 110px auto;    

    @include tools.breakpoint(vars.$tablet--land) {
        flex: 1 1 calc(50% - 2rem);
    }
}

.ocom-coupon__wrapper .k-coupon__button {
    margin-top: 2rem;
    grid-row: 3;
    grid-column: 1/3;    
}

.k-profile-header__button {
    background-color: vars.$color-primary;
    border-color: vars.$color-primary;
}

.k-profile-header__button:hover {
    background-color: color.adjust(vars.$color-primary, $lightness: -10%);
}

.k-profile-header__button-wrapper {
    //margin-top: 1rem;
}

.k-offers__inner {
    border: 0;
    padding: 0;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e5e5;
}

.k-profile-header__logo-wrapper--smaller {
    padding: 1rem;
    //border: 1px solid #d9d9d9;
    margin-bottom: 1rem;

    @include tools.breakpoint(vars.$tablet--land) {
        margin-bottom: 0;
    }
}

.ocom-shop .k-profile-header__title {    
    font-size: 24px;
    font-weight: 700;    
}

.ocom-shop .k-coupon {
    @include tools.breakpoint(vars.$tablet) {
        grid-template-columns: 160px auto 220px;  
    }  

    @include tools.breakpoint(vars.$tablet--land) {
        min-height: 120px;
        padding: 1rem 2rem;        
    }
}

.ocom-shop .k-coupon__content {
    @include tools.breakpoint(vars.$tablet--land) {
        justify-content: center;
    }
}

.ocom-shop .k-coupon__box-value {
    @include tools.breakpoint(vars.$tablet--land) {
        font-size: 32px;
    }
}

.ocom-shop .k-coupon__box-type {
    @include tools.breakpoint(vars.$tablet--land) {
        font-size: 12px;
        line-height: 1.25;
        padding: 0.5rem;
    }
}

.ocom-shop .k-coupon__button {
    @include tools.breakpoint(vars.$tablet) {
        font-size: 16px;
        height: 60px;
    }
}

.ocom-shop .k-coupon__button--code:after {
    @include tools.breakpoint(vars.$tablet) {
        transform: rotate(33deg);
    }
}

.ocom-shop .k-modal .k-coupon {
    @include tools.breakpoint(vars.$tablet) {
        grid-template-columns: 160px auto;  
    }  
}

.ocom-offer__title {
    @include tools.breakpoint(vars.$tablet--land) {
        font-size: 20px;
        line-height: 1.5;
    }
}

.ocom-shop .k-profile-header {
    flex-direction: column;
    align-items: center;

    @include tools.breakpoint(vars.$tablet--land) {
        flex-direction: row;
    }
}

.ocom-shop .k-profile-header__button {
    margin-top: 1rem;
    font-size: 16px;
    padding: 2rem 3rem;

    @include tools.breakpoint(vars.$tablet--land) {
        font-size: 20px;        
    }
}

.ocom-shop .k-profile-header__logo-wrapper {
    @include tools.breakpoint(vars.$tablet--land) {
        width: 200px;
        height: 150px;
        flex: 0 0 200px;    
    }
}

.ocom-shop .k-profile-header--sm-center {
    @include tools.breakpoint(vars.$tablet--land) {
        align-items: flex-start;
    }
}

.ocom-shop .k-offers__title{
    font-size: 14px;
    text-align: center;
}

.ocom-shop .k-offers__title a {
    text-decoration: none;
}

.ocom-shop .k-offers__title a:hover {
    text-decoration: underline;
}

.ocom-shop .k-offers__price {
    margin: auto;
    text-align: center;
}

.ocom-shop__container {
    @include tools.breakpoint(vars.$tablet--land) {
        padding-left: 5%;
    }
}

.ocom-star svg {
    width: 26px;
    height: 26px;
    color: rgb(255, 208, 85);
}

.ocom-shop-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap; 
    padding: 1rem 0;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #D9D9D9;

    @include tools.breakpoint(vars.$tablet) {
        padding: 30px 0;
        margin-top: 3rem;
        margin-bottom: 3rem;
    }
}

.ocom-shop-nav a {    
    flex: 0 0 auto;
    font-size: 14px;
    color: #2b2930;
    padding: 1rem 2rem;
    font-weight: 700;
    
    @include tools.breakpoint(vars.$tablet) {
        font-size: 16px;
        padding: 0 2rem;
    }
}

.ocom-shop-nav .hover-line {
    width: 130%;
    display: block;
    left: -15%;
    top: 31px;
    height: 1px;
    background-color: transparent;
    position: relative;
}

.ocom-shop-nav a:hover .hover-line {
    background-color: vars.$color-primary;
}

.ocom-coupon .k-coupon__box {
    height: 100%;
}

.ocom-coupon .k-coupon__box-value {
    height: 100%;
    color: #fff;
    background-color: vars.$color-primary;
    margin-bottom: 0;
}

.ocom-coupon .k-coupon__box-logo {
    justify-content: flex-start;
}

.ocom-coupon .k-coupon__box-logo img {    
    width: auto;
    max-width: 80px;
    max-height: 60px;
}

// SD content
.sd-content {
	position: relative;
	max-height: 100px;
	overflow: hidden;
	margin-bottom: 2rem;
	transition: max-height 0.2s ease-out;	
}

.sd-content__show {
	position: absolute;
	display: flex;
	width: 100%;
	height: 76px;
	left: 0;
	bottom: 0;
	align-items: flex-end;
	justify-content: flex-start;
	background: linear-gradient(rgba(255, 255, 255, 0.1) 0%, #fff 60%);
	cursor: pointer;

    @include tools.breakpoint(vars.$tablet) {		
        font-size: 16px;
    }
}

.sd-content__show-inner {
	display: flex;
	align-items: center;
}

.sd-content__show-inner:hover {
	@include tools.breakpoint(vars.$desktop) {
		text-decoration: underline;
	}
}

.sd-content__show-icon {
	margin-left: 8px;
}

// Content show content btn
.sd-content__content-btn {
	display: none;
}

.sd-content__content-btn:checked ~ .sd-content {
	max-height: 600px;
}

.sd-content__content-btn:checked ~ .sd-content .sd-content__show {
	display: none;
}

// product-content
.product-content {
	position: relative;
	max-height: 380px;
	overflow: hidden;
	margin-bottom: 2rem;
	transition: max-height 0.2s ease-out;	

    @include tools.breakpoint(vars.$tablet) {
        margin-bottom: 4rem;
    }
}

.product-content__show {
	position: absolute;
	display: flex;
	width: 100%;
	height: 76px;
	left: 0;
	bottom: 0;
	align-items: flex-end;
	justify-content: center;
	background: linear-gradient(rgba(255, 255, 255, 0.1) 0%, #fff 60%);
	cursor: pointer;

    @include tools.breakpoint(vars.$tablet) {		
        font-size: 16px;
    }
}

.product-content__show-inner {
	display: flex;
	align-items: center;
}

.product-content__show-inner:hover {
	@include tools.breakpoint(vars.$desktop) {
		text-decoration: underline;
	}
}

.product-content__show-icon {
	margin-left: 8px;
}

// Content show content btn
.product-content__content-btn {
	display: none;
}

.product-content__content-btn:checked ~ .product-content {
	max-height: 1200px;
}

.product-content__content-btn:checked ~ .product-content .product-content__show {
	display: none;
}


// k-shop__image-wrapper 
.k-shop__image-wrapper img {        
    @include tools.breakpoint(vars.$tablet) {
        max-width: 120px;
    }
}

