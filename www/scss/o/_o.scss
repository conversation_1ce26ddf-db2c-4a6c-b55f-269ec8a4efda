@use "sass:color";
@use "tools";
@use "vars";

.k-header {
  box-shadow: none;
}

.k-header__search {
  border: 0;
  border-radius: 0;
}

.k-header__search-input {
  border: 1px solid #d9d9d9;
  border-radius: 0;
}

.k-header__search-submit {
  border-radius: 0;
  height: 38px;
}

.k-lf-layout .container {
  @include tools.breakpoint(vars.$min-xl) {
    width: 1170px;
  }
}

.k-paginator__wrapper {  
  padding: 0 1rem;
  margin-bottom: 1rem;

  @include tools.breakpoint(vars.$desktop) {
    padding: 0;
  }
}

.k-paginator__item {
  width: 40px;
  height: 40px;
  font-size: 16px;
  font-weight: 400;  
  border: 1px solid #d9d9d9;
  margin: 0.5rem;
}

.k-paginator__item.active {
  background-color: vars.$color-primary;
  border-color: vars.$color-primary;
  color: #fff;
  font-weight: 700;
}

.lf__box,
.lf__box-lg-border {
  border: 0;
}

.lf__box-item {
  @include tools.breakpoint(vars.$tablet--land) {
    flex: 0 0 90%;
  }
}

.k-profile-header__logo-wrapper--smaller {
  border: 0;
  padding: 0;
}

.k-leaflets__title,
.k-leaflets__date {
  text-align: left;
  line-height: 1.25;    
}

.o-leaflet-brand .k-profile-header__title {
  font-size: 22px;
  margin-bottom: 0;

  @include tools.breakpoint(vars.$tablet) {
    font-size: 24px;
  }
}

.o-leaflet-brand .k-profile-header__content {
  justify-content: center;
}


// Cookie banner
#cookiescript_injected {
  transform: translateY(110vh);
}

.slide-up {
	animation: slide-up 1s forwards;
}

@keyframes fade-in {
  0% {opacity: 0;}
  100% {opacity: 1;}
}
@keyframes slide-up {
	0% {transform: translateY(110vh);}
	100% {transform: translateY(0vh);}
}

.oferto-ppc .k-offers__image-wrapper picture {
  height: auto;
}

.o-products {
  position: relative;
  display: block;
  width: 232px;
  background-color: #fff;
  padding: 1rem;  
  border: 1px solid #e5e5e5;
  margin-bottom: 2rem;
}

.o-products img {
  display: block;
  max-width: 100%;
  height: auto;
  margin-bottom: 1rem;
}

.o-products p {
  font-size: 16px;
  margin: 0;
  padding: 0;
  margin-bottom: 1rem;
}

.o-products__button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  padding: 1rem;
  border: 1px solid #2bb673;  
  font-weight: bold;
  background-color: #2bb673;
  text-transform: uppercase;
  cursor: pointer;  
}

.o-products__button:hover {
  background-color: color.adjust(#2bb673, $lightness: -10%);
}

.ads-center {
    display: flex;
    align-items: center;
    justify-content: center;
}