@use "sass:color";
@use "tools";
@use "vars";

.k-leaflets__wrapper {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  @include tools.breakpoint(vars.$tablet--land) {
    margin-left: -15px;
    margin-right: -15px;
  }
}

.k-leaflets__wrapper--xs-mx {
  margin-left: -15px;
  margin-right: -15px;
}

.k-leaflets__wrapper--scroll-x {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
}

.k-leaflets__wrapper.mx-0 {
  margin: 0;
}

.k-leaflets__item {
  //display: grid;
  //grid-template-rows: 280px 1fr;
  box-shadow: 0 1px 6px 0 rgba(0,0,0,.2);
  position: relative;
  flex: 0 0 calc(50% - 12px);
  transition: transform 0.2s ease-in-out;
  border-radius: 8px;

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 calc(33.333% - 12px);
  }

  @include tools.breakpoint(vars.$desktop) {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 0 0 calc(20% - 12px);
  }
}

.k-leaflets__info-wrapper {
  display: flex;
  flex-direction: column;
  height: 70px;
}
.k-leaflets__info {
  display: flex;
  align-items: center;
  padding: 0 8px;
  margin-bottom: 8px;
  flex: 1;
}
.k-leaflets__info img {
  width: 28px;
  height: auto;
  //border-radius: 50%;
  margin-right: 8px;
}
.k-leaflets__info-wrapper .k-leaflets__date {
  padding: 0 8px;
  margin-bottom: 4px;
}
.blur-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
  z-index: -1;
}
.blur-image {
  filter: blur(30px);
}

.k-leaflets__item .k-leaflets__link {
  height: 220px; 
  overflow: hidden;
  
  @include tools.breakpoint(vars.$tablet--land) {
    height: 280px;    
  }
}

.k-leaflets__item:hover {
  transform: scale(1.02);
}

.k-leaflets__item:hover .k-leaflets__title a {
  text-decoration: underline;
}

.k-leaflets__item--mobile {
  flex: 0 0 100%;

  @include tools.breakpoint(vars.$tablet) {
    display: none;    
  }
}

.k-leaflets__item--first {
  width: 100%;
  flex: 0 0 100%;
  height: 312px;  
  overflow: hidden;
  box-shadow: none;

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 33%;    
  }

  @include tools.breakpoint(vars.$desktop) {
    flex: 0 0 40%;
  }
}

.k-leaflets__large-wrapper {
  position: relative;
  width: 100%;

  @include tools.breakpoint(vars.$tablet--land) {
    width: 40%;
  }
}

.k-leaflets__large {
  box-shadow: 0 1px 6px rgba(0,0,0,.2);  
  border-radius: 10px;

  @include tools.breakpoint(vars.$tablet--land) {
    //margin-bottom: 3rem;
  }

  .k-leaflets__large-thumbnail {
    position: relative;
    border-radius: 10px 10px 0 0;
    justify-content: center;
    display: flex;
    height: 220px;
    overflow: hidden;    

    img {
      max-width: 242px;
      //max-height: 336px;
      width: 100%;
      height: auto;
      margin-bottom: -4px;
    }    
    
    .chevron-right {
      cursor: pointer;
      transition: 0.2s ease-in-out;
      position: absolute;
      right: 8px;
      top: 50%;
      width: 42px;
      height: 42px;
      padding: 8px;
      border-radius: 50%;
      transform: translateY(-50%);
      border: 1px solid transparent;
      background-color: hsla(0,0%,100%,.8);
      &:hover {
        border: 1px solid #bc2026;
      }
    }

    @include tools.breakpoint(vars.$tablet) {
      height: 336px;
    }
  }
  .k-leaflets__large-detail {
    display: flex;
    align-items: center;
    padding: 8px;
    min-height: 66px;
    flex-direction: column;

    @include tools.breakpoint(vars.$tablet--land) {
      flex-direction: row;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 10px;
      img {
        //border-radius: 50%;
        width: 36px;
        height: auto;
      }
      a {
        font-weight: 700;
        font-size: 18px;
      }
      a:hover {
        text-decoration: underline;
      }
    }
    .note {
      font-size: 10px;      
    }
  }
}

.k-leaflets__large-detail-title {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;
}

.k-leaflets__large-detail .k-leaflets__button {
  margin: 0;
  width: 300px;
  height: 40px;
  font-size: 14px;
  font-weight: 700;
  border-radius: 10px;

  @include tools.breakpoint(vars.$tablet--land) {
    width: 166px;
  }
}

.k-leaflets__large-detail-title .k-leaflets__date {
  padding: 0;
}

.k-leaflets__button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 14px;
  color: #fff;
  background: vars.$color-primary;
  padding: 1rem;
  margin: 8px;
  font-weight: bold;
}

.k-leaflets__button:hover {
  @include tools.breakpoint(vars.$desktop) {
    background: color.adjust(vars.$color-primary,$lightness: -10%);
  }
}

.k-leaflets__button--secondary {
  border: 1px dashed vars.$color-primary;
  background: #fff;
  color: vars.$color-primary;
}

.k-leaflets__button--secondary:hover {
  @include tools.breakpoint(vars.$desktop) {
    color: #fff;
    background: vars.$color-primary;
  }
}


.adslot-1 {
  width: 100%;
  height: 312px;

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 33%;    
  }

  @include tools.breakpoint(vars.$desktop) {
    flex: 0 0 40%;
  }
}

.adslot-2 {
  width: 100%;
  height: 312px;

  @include tools.breakpoint(vars.$tablet) {
    height: 280px;
  }  
}

.k-leaflets__title {
  position: relative;
  display: block;
  color: #000;
  font-size: 12px;
  font-weight: 700;
  line-height: 1.5;
  padding: 0 8px;
  max-height: 36px;
  overflow: hidden;
}

.k-leaflets__date {
  color: #000;
  font-size: 12px;
  line-height: 1.5;
  padding: 0 8px;
}

.k-leaflets__link {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.k-leaflets__image {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
  margin: auto;
  padding: 8px;
  border-radius: 12px;
}

// 4 in row
.k-leaflets__wrapper--4 .k-leaflets__item {
  flex: 0 0 calc(50% - 12px);

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 calc(33% - 12px);
  }

  @include tools.breakpoint(vars.$tablet--land) {
    flex: 0 0 calc(25% - 12px);
  }
}

// 3 in row
.k-leaflets__wrapper--3 .k-leaflets__item {
  flex: 0 0 calc(50% - 12px);

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 calc(33% - 12px);
  }  
}

// Ads
.k-leaflet__ads-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  justify-content: space-evenly;
}

.k-leaflets__link.expired:before,
.lf__box-image-wrapper.expired:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 1;
}

.k-leaflets__corner {
  position: absolute;
  display: flex;
  justify-content: center;
  bottom: 0rem;
  left: 50%;
  margin-left: -65px;
  top: -13px;
  width: 130px;
  bottom: auto;
  border-radius: 10px;  
  padding: 0.75rem;
  font-weight: 700;
  background-color: #4CAF50;
  color: #fff;
  font-size: 14px;  
  text-align: center;
}

@media only screen and (max-width: 767px) {
  .k-leaflets__large-wrapper {
    width: 100%;
    padding: 0 1rem;
    margin-right: 0;
  }
}

@media (max-width: 1179px) {
  .k-leaflets__item {
    grid-template-rows: auto 1fr;
  }
}

.k-leaflets__bubble {
  position: absolute;    
  top: 0;
  right: 0;
  z-index: 9;  
}

.k-leaflets__bubble-primary {
  padding: 0.5rem 1rem;
  background: vars.$color-primary;
  border-radius: 18px;
  font-size: 12px;
  color: #fff;
  font-weight: 700;
  margin: 0 0.25rem;
}

.k-leaflets__bubble-top {
  padding: 0.5rem 1rem;
  background: #f8992b;
  border-radius: 18px;
  font-size: 12px;
  color: #fff;
  font-weight: 700;
  margin: 0 0.25rem;
}

.k-leaflets__bubble small {
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  margin: 0 0.25rem;
}

@media only screen and (max-width: 420px) {
  .k-leaflets__info-wrapper {
    height: 90px;
  }
}

.inline-flex {
  display: inline-flex;
}

.overflow-x-auto {
  overflow-x: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}