@use "sass:color";
@use "tools";
@use "vars";

.k-profile-header {
  display: flex;
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  padding: 0 1rem;

  @include tools.breakpoint(vars.$tablet) {
    padding: 0;
  }
}

.k-profile-header.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}

.k-profile-header__logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100px;
  height: 80px;
  flex: 0 0 100px;
  padding: 1rem;
  //border: 1px solid #d9d9d9;
  border-radius: 4px;

  @include tools.breakpoint(vars.$tablet) {
    width: 160px;
    height: 115px;
    flex: 0 0 160px;
    padding: 2rem;
  }
}

.k-profile-header__logo-wrapper picture {
  display: block;
  width: 100%;
}

.k-profile-header__logo {
  position: relative;
  display: block;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 60px;
  margin: auto;

  @include tools.breakpoint(vars.$tablet) {
    max-height: 75px;
  }
}

.k-profile-header__content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 0 0 calc(100% - 100px);
  padding: 0 1rem;

  @include tools.breakpoint(vars.$tablet) {
    flex: 1 1 auto;
    padding: 0 2rem;
  }
}

.k-profile-header__title {
  font-size: 22px;
  font-weight: 600;
  margin-top: 0rem;
  margin-bottom: 0rem;  

  @include tools.breakpoint(vars.$tablet) {
    font-size: 24px;
  }
}

.k-profile-header__title.mb-0 {
  margin-bottom: 0;
}

.k-profile-header__text {
  line-height: 1.5;  
  margin-bottom: 2rem;  
}

.k-profile-header__text a {
  text-decoration: underline;
}

.k-profile-header__text a:hover {
  text-decoration: none;
}

.k-profile-header__button-wrapper {
  margin-top: auto;
}

.k-profile-header__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #bc2026;
  border-radius: 40px;
  font-weight: bold;
  font-size: 12px;
  line-height: 14px;
  padding: 1rem;
  color: #fff;
  background-color: #bc2026;

  @include tools.breakpoint(vars.$tablet) {
    min-width: 220px;
  }
}

.k-profile-header__button:hover {
  background-color: color.adjust(#bc2026, $lightness: -10%);
}

.k-profile-header--sm-center {
  @include tools.breakpoint(vars.$tablet) {
    align-items: center;
  }
}

.k-profile-header--sm-center .k-profile-header__text {
  margin-bottom: 0;
}

// k-profile-header__logo-wrapper--smaller
.k-profile-header__logo-wrapper--smaller {
  @include tools.breakpoint(vars.$tablet) {
    width: 100px;
    height: 80px;
    flex: 0 0 100px;
    padding: 1rem;
  }
}

.k-profile-header__logo-wrapper--smaller .k-profile-header__logo {
  @include tools.breakpoint(vars.$tablet) {
    max-height: 60px;
  }
}
