@use "tools";
@use "vars";

.k-header {
  position: relative;
  display: flex;
  align-items: center;
  height: 50px;
  //box-shadow: 0 3px 10px rgba(0,0,0,.102); //0px 3px 10px rgba(0, 0, 0, 0.1);

  @include tools.breakpoint(vars.$desktop) {
    height: 80px;
  }
}

.k-header .container {
  padding: 0;
}

.k-header__logo-link {
  position: relative;
  display: block;
  width: 90px;
  flex: 0 0 90px;

  @include tools.breakpoint(vars.$phone-md) {
    width: 100px;
    flex: 0 0 100px;
  }

  @include tools.breakpoint(vars.$desktop) {
    width: 186px;
    flex: 0 0 186px;
  }
}

.k-header__logo-link--flex {
  display: flex;
  align-items: center;
}

.k-header__logo-link__logo {
  flex-shrink: 0;
  width: 26px;
  height: auto;

  @include tools.breakpoint(vars.$desktop) {
    width: 40px;
  }
}

.k-header__logo-text {
  margin-left: 0.5rem;
  font-size: 18px;
  flex-shrink: 0;
  letter-spacing: -0.8px;

  @include tools.breakpoint(vars.$desktop) {
    font-size: 32px;
  }
}

.k-header__logo-link img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.k-header__search {
  position: relative;
  display: flex;
  align-items: center;
  width: 200px;
  height: 40px;
  margin-left: auto;
  border: 1px solid #d9d9d9;
  border-radius: 40px;

  @include tools.breakpoint(vars.$tablet) {
    width: 270px;
  }
}

.k-header__search-input {
  position: relative;
  display: block;
  width: 100px;
  flex: 1 1 auto;
  height: 38px;
  margin-left: 1px;
  border-radius: 40px;
  padding-left: 1rem;
  border: 0;
  outline: none;
  line-height: 16px;
  color: #2c2c2c;
  font-size: 14px;

  @include tools.breakpoint(vars.$tablet) {
    padding-left: 2rem;
  }
}

.k-header__search-submit {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-width: 67px;
  height: 32px;
  margin-right: 4px;
  background: #fff;
  border: 1px solid vars.$color-primary;
  border-radius: 40px;
  font-weight: bold;
  color: vars.$color-primary;
  font-size: 12px;
  line-height: 14px;
  text-transform: uppercase;
  cursor: pointer;
  -webkit-appearance: none;

  @include tools.breakpoint(vars.$tablet) {
    min-width: 87px;
  }
}

.k-header__search-submit:hover {
  background-color: vars.$color-primary;
  color: #fff;
}

.k-header__search-wrapper {
  position: absolute;
  display: none;
  width: 100%;
  left: 0;
  top: 44px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  z-index: 99;
}

.k-header__search-wrapper.show {
  display: block;
}

.k-header__nav {
  position: absolute;
  display: none;
  align-items: center;
  flex-direction: column;
  background: #fff;
  width: 100%;
  top: 46px;
  z-index: 9;
  left: 0;

  @include tools.breakpoint(vars.$tablet--land) {
    position: relative;
    display: flex;
    width: auto;
    margin-left: auto;
    flex-direction: row;
    top: 0;
  }
}

.k-header__nav.show {
  display: flex;
}

.k-header__nav-item {
  display: block;
  flex: 0 0 auto;
  width: 100%;
  color: #000;
  font-size: 16px;
  line-height: 19px;
  padding: 15px;
  text-align: center;

  @include tools.breakpoint(vars.$tablet--land) {
    width: auto;
    text-align: left;
  }
}

.k-header__nav-item:hover {
  color: vars.$color-primary;
  text-decoration: underline;
}

.k-header__nav-item.color-grey {
  color: vars.$color-grey;
}

.k-header__nav-item--more {
  @include tools.breakpoint(vars.$tablet--land) {
  }
}

.k-header__nav-dropdown-wrapper {
  position: relative;
  width: 100%;
}

.k-header__nav-dropdown-wrapper:hover .k-header__nav-dropdown {
  display: block;
}

.k-header__nav-dropdown {
  position: relative;
  display: block;

  @include tools.breakpoint(vars.$tablet--land) {
    position: absolute;
    display: none;
    min-width: 110px;
    height: auto;
    top: 44px;
    left: 15px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    z-index: 99;
  }
}

.k-header__nav-dropdown-item {
  position: relative;
  display: block;
  width: 100%;
  color: #2c2c2c;
  font-size: 16px;
  line-height: 19px;
  padding: 15px;
  text-align: center;

  @include tools.breakpoint(vars.$tablet--land) {
    color: #2c2c2c;
    font-size: 14px;
    text-align: left;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #d9d9d9;
  }
}

.k-header__nav-dropdown-item:hover {
  background-color: #d9d9d9;
}

.k-header__nav-dropdown-item:last-child {
  border-bottom: 0;
}

.k-header__menu-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0;
  outline: none;
  background: none;
  padding: 0;
  margin-left: 1rem;
  outline: none;
  -webkit-appearance: none;

  @include tools.breakpoint(vars.$phone-md) {
    margin-left: 1rem;
  }

  @include tools.breakpoint(vars.$tablet--land) {
    display: none;
  }
}

.k-header__menu-icon svg {
  position: relative;
  display: block;
  width: 20px;
  height: 20px;
  outline: none;
  -webkit-appearance: none;
  color: vars.$color-primary;
}

.k-header__nav-separator {
  display: none;

  @include tools.breakpoint(vars.$tablet--land) {
    display: block;
  }
}

.k-header .google-auto-placed {
  max-height: 50px;
  overflow: hidden;

  @include tools.breakpoint(vars.$desktop) {
    max-height: 80px;
  }
}

.k-header__category {
  display: none;    
  overflow-y: auto;

  @include tools.breakpoint(vars.$desktop) {
    display: block;
  }

  @include tools.breakpoint(vars.$min-lg) {
    overflow-y: hidden;
  }
}

.k-header__category .container {
  justify-content: start;  
  //background-color: #f8f8f8;

  @include tools.breakpoint(vars.$min-lg) {
    justify-content: center;    
    padding-bottom: 2rem;
  }
}

.k-header__category-item {
  flex: 1 0 auto;
  padding: 1rem 0;  
}

//city picker
.k-header--city-picker {
  height: auto;
}

.k-header--city-picker .container {
  flex-wrap: wrap;
  padding: 1rem 0;

  @include tools.breakpoint(vars.$tablet) {    
    padding: 1rem;
  }
}

.k-header--city-picker .k-header__menu-icon {
  margin-left: auto;
}


.k-header__search--city-picker {
  width: 100%;
  margin-top: 1.5rem;  
  margin-left: auto;
  margin-right: auto;  
  order: 1;

  @include tools.breakpoint(vars.$tablet) {    
    width: auto;
    order: 0;    
    margin-top: 0;
    margin-right: 0;
  }
}

.k-header__search--city-picker form {
  display: flex;  
  width: 100%;
  align-items: center;
}

.k-header__search--city-picker input {
  width: 110px;  
  padding-left: 0;
  flex-grow: 1;

  @include tools.breakpoint(vars.$tablet) {
    width: 130px;
    padding-left: 10px;
  }
}

.k-header__search--city-picker input.k-header__search-submit {
  width: 68px;
  padding: 0;

  @include tools.breakpoint(vars.$tablet) {
    width: 88px;
  }
}

.k-header__search--city-picker select {
  border: 0;
  max-width: 68px;
  flex-grow: 1;
  margin-right: 1rem;

  @include tools.breakpoint(vars.$tablet) {
    max-width: 150px;
  }
}