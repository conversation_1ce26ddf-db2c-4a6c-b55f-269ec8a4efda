@use "tools";
@use "vars";

.k-offers {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;

  @include tools.breakpoint(vars.$tablet--land) {
    margin-left: -1rem;
    margin-right: -1rem;
  }
}

.k-offers__item {
  position: relative;
  display: flex;
  flex: 0 0 calc(100% / 2);
  padding: 0.5rem;

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 calc(100% / 4);
    padding: 1rem;
  }

  @include tools.breakpoint(vars.$tablet--land) {
    flex: 0 0 calc(100% / 5);
  }
}

.k-offers__inner {
  position: relative;
  display: flex;
  width: 100%;
  background-color: #f8f9fa;
  padding: 1rem;
  flex-direction: column;
  //border: 1px solid #e5e5e5;
  border-radius: 8px;
  //box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
}

.k-offers__image-wrapper {
  display: block;
}

.k-offers__image-wrapper picture {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex: 0 0 150px;
  height: 150px;
  overflow: hidden;
  margin-bottom: 1rem;
  margin-left: auto;
  margin-right: auto;
}

.k-offers__image {
  display: block;
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  border-radius: 8px;
}

.k-offers__content {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  margin-top: auto;
  text-align: center;
}

.k-offers__title {
  font-size: 16px;
  margin: 0;
  padding: 0;
  line-height: 1.25;
  margin-bottom: 1.5rem;
}

.k-offers__title a {
  color: #000;
  font-weight: 500;    
}

.k-offers__title a:hover {
  //text-decoration: none;
  text-decoration: underline;
}

.k-offers__small {
  display: block;
  color: vars.$color-grey;
  font-size: 14px;
  margin-bottom: 0.5rem;
}

.k-offers__small a {
  color: vars.$color-grey;
}

.k-offers__small a:hover {
  text-decoration: underline;
}

.k-offers__text {
  font-size: 12px;
  margin-bottom: 0.5rem;
}

.k-offers__right {
  position: relative;
  //margin-left: auto;
  flex: 0 0 auto;
}

.k-offers__price {
  display: flex;
  align-items: flex-end;
  margin-top: auto;
  justify-content: center;
}

.k-offers__price strong {
  display: block;
  font-size: 22px;
  color: vars.$color-primary;
}

.k-offers__price small {
  font-size: 14px;
  text-decoration: line-through;
  margin-left: auto;
}

.k-offers__link {
  color: vars.$color-grey;
  font-size: 12px;
  text-decoration: underline;
  margin-right: 1rem;
}

.k-offers__link:hover {
  text-decoration: none;
}

// expired
.k-offers__item.expired .k-offers__inner {
  //opacity: 0.5;
}

.k-offers__item.expired .k-offers__inner:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(219, 219, 219, 0.9);
}

.k-offers__item.expired .k-offers__inner:after {
  position: absolute;
  display: flex;
  content: attr(data-line);
  color: vars.$color-primary;
  font-size: 18px;
  font-weight: 700;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9;
  align-items: center;
  justify-content: center;
}

.k-offers--4 .k-offers__item {    
  flex: 0 0 calc(100% / 2);
  padding: 0.5rem;

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 calc(100% / 3);
    padding: 1rem;
  }

  @include tools.breakpoint(vars.$tablet--land) {
    flex: 0 0 calc(100% / 4);
  }
}

.k-offers__badge {  
  position: relative;
  top: -5px;  
  font-size: 14px;
  line-height: 1.5rem;
  background: #c30404;
  color: #fff;
  border-radius: .5rem;
  text-align: center;
  padding: .5rem .5rem;
  margin-left: 0.5rem;
  font-weight: 600;
}

.k-offers__small--logo {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: -50px;
}

.k-offers__logo-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  width: 50px;
  height: 50px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.2s ease;
  border: 2px solid #bc2026;
  overflow: hidden;
  background-color: #fff;
}

.k-offers__logo-wrapper img {
  max-width: 80%;
  height: auto;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.k-offers__tag {
  color: grey;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
}

.k-offers__tag.red {
  color: vars.$color-primary;  
}

.k-offers__tag.orange {
  color: #ff9800;  
}

.k-offers__tag.green {
  color: #4caf50;  
}