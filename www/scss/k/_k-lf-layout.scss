@use "tools";
@use "vars";

.k-lf-layout .container {
  display: flex;
  width: 100%;
  flex-direction: column;

  @include tools.breakpoint(vars.$tablet--land) {
    width: 995px;
    flex-direction: row;
  }

  @include tools.breakpoint(vars.$min-lg) {
    width: 1170px;
  }

  @include tools.breakpoint(vars.$min-xl) {
    width: 1170px;
  }
}

.k-lf-layout .container.d-block {
  display: block;
}

.leaflet__content {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;

  @include tools.breakpoint(vars.$tablet--land) {
    flex-direction: row;
    width: calc(100% - 315px);
  }
}

.leaflet__content.w100 {
  width: 100%;
}

.leaflet__aside {
  position: relative;
  display: block;

  @include tools.breakpoint(vars.$min-lg) {
    display: block;
    //flex: 0 0 195px;
    //width: 195px;
    width: 300px;
    order: -1;
    margin: 0;
    margin-right: 15px;
    padding-top: 1rem;
  }

  @include tools.breakpoint(vars.$min-xl) {
    //flex: 0 0 270px;
    //width: 100%;
  }
}

.leaflet__sidebar {
  margin-top: 0;

  @include tools.breakpoint(vars.$tablet--land) {
    margin-left: 15px;
    flex: 0 0 160px;
    width: 160px;
    padding-top: 1rem;
  }

  @include tools.breakpoint(vars.$min-lg) {
    flex: 0 0 300px;
    width: 300px;
  }
}

.leaflet__detail-header {
  margin-top: 1.5rem;

  @include tools.breakpoint(vars.$tablet) {
    margin-top: 1rem;
  }
}

.page-header__title {
  font-size: 20px;
  font-weight: 700;

  @include tools.breakpoint(vars.$tablet) {
    font-size: 24px;
  }
}

.page-header__text {
  margin-bottom: 0;
  font-size: 12px;

  @include tools.breakpoint(vars.$tablet) {
    font-size: 14px;
  }
}

.page-header__text a {
  text-decoration: underline;
}

.page-header__text a:hover {
  text-decoration: none;
}

.leaflet__date {
  display: inline-block;
  font-size: 20px;
  font-weight: 400;

  @include tools.breakpoint(vars.$tablet) {
    font-size: 28px;
  }
}

.lf__box {
  position: relative;
  display: block;
  text-align: left;
  margin-bottom: 1rem;

  @include tools.breakpoint(vars.$tablet--land) {
    border: 1px solid #d9d9d9;
    padding: 1rem;
  }
}

.lf__box-lg-border {
  @include tools.breakpoint(vars.$tablet--land) {
    border: 0;
    padding: 0rem;
  }

  @include tools.breakpoint(vars.$min-lg) {
    border: 1px solid #d9d9d9;
    padding: 1rem;
  }
}

.lf__box-title {
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 400;

  @include tools.breakpoint(vars.$min-lg) {
    font-size: 18px;
  }
}

.lf__box-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  @include tools.breakpoint(vars.$min-lg) {
    flex-direction: column;
  }
}

.lf__box-item {
  position: relative;
  display: flex;
  text-align: center;
  flex: 0 0 33%;
  padding: 0.5rem;

  @include tools.breakpoint(vars.$min-lg) {
    text-align: left;
    padding: 0;
  }
}

.lf__box-item--md-100 {
  @include tools.breakpoint(vars.$tablet--land) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.lf__box-item--md-100 .lf__box-image-wrapper {
  flex: 0 0 100px;
}

.lf__box-item:last-child {
  margin-bottom: 0;
}

.lf__box-item:hover .lf__box-link {
  text-decoration: underline;
}

.lf__box-item:hover .lf__box-image-wrapper:before {
  position: absolute;
  display: block;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 9;
}

.lf__box-image-wrapper {
  position: relative;
  display: block;
  flex: 0 0 60px;
  height: auto;
}

.lf__box-image--medium {
  flex: 0 0 100px;
}

.leaflet-preview {
  position: relative;
  display: block;
  width: 100%;
  max-width: 870px;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
}

.leaflet-preview img {
  position: relative;
  display: block;
  max-width: 100%;
  height: auto;
}

.leaflet-list-compact__item {
  border-color: #d9d9d9;
}

// --------

.leaflet__detail-header {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  z-index: 1;

  @include tools.breakpoint(vars.$tablet) {
    flex-direction: row;
  }
}

.leaflet__detail-header-content {
  position: relative;
  display: block;
  flex: 1 1 auto;
}

.leaflet__detail-header-side {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 80px;
  height: 60px;
  order: -1;
  margin-bottom: 20px;

  @include tools.breakpoint(vars.$tablet) {
    flex: 0 0 160px;
    order: 0;
    margin-bottom: 0;
  }
}

.leaflet__detail-header-logo-wrapper {
  position: relative;
  display: flex;
  height: auto;
  align-items: center;
  justify-content: center;
}

.leaflet__detail-header-logo {
  position: relative;
  display: block;
  margin: auto;
  width: auto;
  max-width: 80px;
  height: auto;
  max-height: 60px;
}

// Mobile version 2
.leaflet__detail-header--mobile-row {
  flex-direction: row;
  text-align: left;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding: 0 1rem;

  @include tools.breakpoint(vars.$min-lg) {
    padding: 0;
    margin-bottom: 2rem;
    align-items: center;
  }
}

.leaflet__detail-header--mobile-row .page-header__title {
  margin-top: 0;
  margin-bottom: 0.5rem;  
}

.k-subdomain.leaflet__detail-header--mobile-row .page-header__title {
  font-size: 26px;
  font-weight: normal;
  line-height: 1.25;
}

.leaflet__detail-header--mobile-row .page-header__title,
.leaflet__detail-header--mobile-row .page-header__text,
.leaflet__detail-header--mobile-row .leaflet__date {
  text-align: left;
}

.leaflet__detail-header--mobile-row .leaflet__detail-header-side {
  margin-bottom: 0;
  margin-right: 1.5rem;

  @include tools.breakpoint(vars.$tablet) {
    display: flex;
    justify-content: flex-end;
    margin-right: 0;
  }
}

.leaflet__detail-header--mobile-row .leaflet__detail-header-logo {
  margin: 0;
}

// Shops layout

.k-lf-layout--wide-content .leaflet__content {
  @include tools.breakpoint(vars.$min-lg) {
    width: calc(100% - 175px);
  }
}

.k-lf-layout--wide-content .leaflet__sidebar {
  @include tools.breakpoint(vars.$tablet--land) {
    margin-left: 15px;
    flex: 0 0 160px;
    width: 160px;
    padding-top: 2rem;
  }
}

// Fixed container

.k-lf-layout--fixed-container .container {
  @include tools.breakpoint(vars.$min-xl) {
    width: 1170px;
  }
}

// Eshop layout
.leaflet__detail-header--bigger-logo .leaflet__detail-header-side {
  justify-content: flex-start;
  flex: 0 0 auto;
  margin-right: 2rem;
  padding: 2rem;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
}

.leaflet__detail-header--bigger-logo.leaflet__detail-header--mobile-row {
  align-items: center;
}
