@use "tools";
@use "vars";

.k-shop {
    position: relative;
    display: flex;
    flex-wrap: wrap;
}

.k-shop__item {
    position: relative;
    display: block;
    flex: 0 0 calc(100% / 3);
    margin-bottom: 3rem;

    @include tools.breakpoint(vars.$desktop) {
        flex: 0 0 calc(100% / 6);
    }
}

.k-shop__item:hover .k-shop__title {
    text-decoration: underline;
}

.k-shop__image-wrapper {
    position: relative;
    display: flex;    
    height: 100px;
    align-items: center;
    justify-content: center;
    margin: auto;
    margin-bottom: 1rem;
}

.k-shop__image-wrapper img {
    position: relative;
    display: block;
    width: auto;
    max-width: 80px;
    height: auto;
    max-height: 70px;    
    height: auto;
}

.k-shop__title {
    position: relative;
    display: block;
    color: #000;
    font-size: 12px;
    font-weight: 700;
    line-height: 1.5;
    text-align: center;    
    margin: 0 1rem;
}

.k-shop__bubble {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 40px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    //border-radius: 50%;    
    font-size: 10px;
    color: #fff;
    font-weight: 700;
    padding: 0.5rem;
    background-color: vars.$color-primary;
}

.k-shop__bubble.green {
    color: green;
    border: 1px solid green;
    background-color: #fff;
}

.k-shop__input {
    position: relative;
    display: block;
    width: auto;
    flex: 1 1 auto;
    height: 32px;
    margin-left: 1px;    
    padding-left: 1rem;
    border: 1px solid #d9d9d9;
    outline: none;
    line-height: 16px;
    color: #2c2c2c;    
  }
  
  .k-shop__submit {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    min-width: 67px;
    height: 32px;
    margin-right: 4px;
    background: #fff;
    border: 1px solid vars.$color-primary;    
    font-weight: bold;
    color: vars.$color-primary;
    font-size: 12px;
    line-height: 14px;
    text-transform: uppercase;
    cursor: pointer;
    -webkit-appearance: none;
  
    @include tools.breakpoint(vars.$tablet) {
      min-width: 87px;
    }
  }
  
  .k-shop__submit:hover {
    background-color: vars.$color-primary;
    color: #fff;
  }

  .k-tabs__tabLink {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      cursor: pointer;
      color: #c4c4c4;
      transition: color 0.3s ease;
      font-size: 18px;
      font-weight: 700;
      width: 100%;
      padding-bottom: 5px;
      svg {
        margin-right: 4px;
        fill: currentColor;
      }
      &:hover {
        color: vars.$color-primary;
        transition: color 0.2s;
      }
      &:not(.active)::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%; 
        width: 0%; 
        height: 2px; 
        background: vars.$color-primary; 
        transition: width 0.3s ease-out, left 0.3s ease-out; 
      }
      &:not(.active):hover::after {
        width: 100%; 
        left: 0;
      }
      &.active {
        color: vars.$color-primary;
        padding-bottom: 3px;
        border-bottom: 2px solid vars.$color-primary;
      }
  }
  .k-tabs_tab {
    display: none;
  }
  .k-tabs_tab.active {
      display: block;
  }
  .k-tabs__buttons {
      display: flex;
      text-align: center;
      gap: 12px;
      //max-width: 800px;
      margin-bottom: 20px;
  }
  @media only screen and (max-width: 767px) {
      .k-tabs__tabLink {
          font-size: 14px;
          flex-direction: column;
          svg {
            width: 18px;
            height: 18px;
          }
      }
  }