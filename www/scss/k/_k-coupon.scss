@use "sass:color";
@use "tools";
@use "vars";

.k-coupon {
    position: relative;
    display: grid;
    min-height: 100px;
    grid-template-rows: auto;    
    grid-row-gap: 0;
    grid-column-gap: 1rem;    
    text-align: center;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem;
    border: 1px solid #D9D9D9;
    grid-template-columns: 100px auto;

    @include tools.breakpoint(vars.$tablet) {
        //margin-left: 2rem;
        //margin-right: 2rem;
        //margin-bottom: 2rem;
        grid-column-gap: 2rem;    
        grid-template-columns: 110px auto 190px;
    }
}

.k-coupon__box {
    display: flex;
    flex-direction: column;
    align-self: center;
    justify-content: flex-start;
    //flex-wrap: wrap;
    //border: 1px solid #D9D9D9;
    //border-radius: 8px;
    overflow: hidden;
    color: vars.$color-primary;
}

.k-coupon__box-value {
    display: flex;
    min-height: 60px;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-bottom: 1rem; 

    @include tools.breakpoint(vars.$tablet) {
        font-size: 24px;
    }
}

.k-coupon__box-value--small {
    font-size: 18px;

    @include tools.breakpoint(vars.$tablet) {
        font-size: 22px;
    }
}


.k-coupon__box-type {
    display: block;
    width: 100%;
    align-self: flex-end;
    color: #fff;                 
    font-size: 1rem;
    line-height: 100%;
    font-weight: 600;    
    text-align: center;
    text-transform: uppercase;
    padding: 5px 2px;
    background: vars.$color-primary;
}

.k-coupon__box-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 35px;
}

.k-coupon__content {
    display: flex;
    flex-flow: column wrap;
    //justify-content: center;
    text-align: left;
}

.k-coupon__button {
    position: relative;
    display: flex;
    align-self: center;       
    align-items: center;
    justify-content: center;    
    height: 50px;    
    background: vars.$color-primary;
    border: 1px solid vars.$color-primary;
    border-radius: 40px;
    font-weight: bold;
    color: #fff;
    font-size: 14px; 
    cursor: pointer;    
    margin-top: 2rem;
    grid-row: 3;
    grid-column: 1/3;

    @include tools.breakpoint(vars.$tablet) {
        grid-row: auto;
        grid-column: 3/4;        
        margin-top: 0; 
    }
}

.k-coupon__button:hover {    
    background: color.adjust(vars.$color-primary,$lightness: -10%);
}

.k-coupon__box--sale .k-coupon__box-type {
    background: #ff9300;
}

.k-coupon__box--sale {
    color: #ff9300;
}

.k-coupon__button--sale .k-coupon__button-label{
    width: 100%;
    //background: #ff9300;
}

.k-coupon__button--code:hover:before {
    background-color: color.adjust(vars.$color-primary, $lightness: -5%);
    width: 80%;
}

.k-coupon__button--code:hover:after {
    left: 80%;
}

.k-coupon__button--code:before {
    content: "";
    position: absolute;
    width: 85%;
    height: 100%;
    left: 0;
    top: 0;
    border-top-left-radius: 30px;
    border-bottom-left-radius: 30px;
    transition: width .2s ease-out;
    z-index: 5;
    background: vars.$color-primary;    
}

.k-coupon__button--code:after {
    left: 85%;
    top: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 48px 0 0 35px;
    transform: rotate(17deg);
    transition: left .2s ease-out;
    transform-origin: top left;
    content: "";
    position: absolute;
    z-index: 99;
    display: block;
    border-color: transparent transparent transparent color.adjust(vars.$color-primary,$lightness: -10%);    
}

.k-coupon__button-label {
    position: absolute;
    width: 85%;    
    z-index: 10;
    top: 50%;
    left: 0%;
    transform: translate(0%,-50%);
}

.k-coupon__button-code {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    top: 0;
    left: 0;    
    text-align: right;
    padding: 0 1rem;
    width: 100%;
    height: 100%;    
    z-index: 0;
    color: #000;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;    
    background-color: #fff;
    border: 1px solid #cecece;    
    overflow: hidden;    
}
