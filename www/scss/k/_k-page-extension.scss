@use "sass:color";
@use "vars";

.k-page-extension {
    position: fixed;
    display: flex;
    bottom: 30px;
    right: 10px;
    z-index: 99;
}

.k-page-extension__btn {
    position: relative;
    display: inline-block;    
    font-size: 12px;
    color: #fff;
    font-weight: 700;    
    text-decoration: none;    
    padding: 0.5rem 1rem;
    border-radius: 4px;
    background-color: vars.$color-primary;
    margin-left: 0.5rem;
}

.k-page-extension__btn:hover {
    background-color: color.adjust(vars.$color-primary, $lightness: -10%);
}

.k-page-extension__tag {
    position: relative;
    display: inline-block;    
    font-size: 10px;
    color: #000;
    font-weight: 700;    
    text-decoration: none;    
    padding: 0.5rem;
    border-radius: 4px;
    text-transform: uppercase;    
    background-color: #fff;
    border: 1px solid black;
    margin: 0 0.25rem;
}

.k-page-extension__tag--green {
    background-color: #4caf50;
    border-color: #4caf50;
    color: #fff;
}