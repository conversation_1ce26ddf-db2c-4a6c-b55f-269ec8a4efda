document.addEventListener("DOMContentLoaded", function () {
  const cityPicker = document.getElementById(
    "frm-cityPickerControl-form-cityId"
  );
  const form = document.getElementById("frm-cityPickerControl-form");
  const niceSelect = NiceSelect.bind(cityPicker, { searchable: true });

  const cityPickerId = document.getElementById("city-picker");
  const cityFromIP = cityPickerId.getAttribute("data-city-from-ip");
  const locationFromBrowser = cityPickerId.getAttribute(
    "data-location-from-browser"
  );
  const locationError = cityPickerId.getAttribute("data-location-error");

  cityPicker.addEventListener("change", function (event) {
    const selectedCityId = event.target.value;

    if (selectedCityId === "currentLocation") {
      getLocationFromBrowser();
    } else {
      form.submit();
    }
  });

  function checkCookie(name) {
    var cookies = document.cookie.split(";");
    for (var i = 0; i < cookies.length; i++) {
      var cookie = cookies[i].trim();
      if (cookie.indexOf(name + "=") === 0) {
        return true;
      }
    }
    return false;
  }

  function getCookie(name) {
    let cookieArr = document.cookie.split(";");

    for (let i = 0; i < cookieArr.length; i++) {
      let cookie = cookieArr[i].trim();

      if (cookie.indexOf(name + "=") === 0) {
        return cookie.substring(name.length + 1);
      }
    }

    return null;
  }

  function resolveCityIdFromUserIp() {
    const xhr = new XMLHttpRequest();
    xhr.open("POST", cityFromIP, true);
    xhr.setRequestHeader("Content-Type", "application/json");

    xhr.onload = function () {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        setUserCityId(response.cityId);

        return response;
      } else {
        console.error("Error:", xhr.status, xhr.statusText);
      }
    };

    xhr.onerror = function () {
      console.error("Request failed.");
    };

    xhr.send();
  }

  function getLocationFromBrowser() {
    navigator.geolocation.getCurrentPosition(
      function (position) {
        let latitude = position.coords.latitude;
        let longitude = position.coords.longitude;

        const xhr = new XMLHttpRequest();
        xhr.open("POST", locationFromBrowser, true);
        xhr.setRequestHeader("Content-Type", "application/json");

        xhr.onload = function () {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            setUserCityId(response.cityId);

            return response;
          } else {
            console.error("Error:", xhr.status, xhr.statusText);
          }
        };

        xhr.onerror = function () {
          console.error("Request failed.");
        };

        xhr.send(JSON.stringify({ latitude: latitude, longitude: longitude }));
      },
      function (error) {
        alert(locationError);

        userLocation = JSON.parse(
          decodeURIComponent(getCookie("userLocation"))
        );
        setUserCityId(userLocation.cityId);
      }
    );
  }

  function setUserCityId(cityId) {
    cityPicker.value = cityId;
    niceSelect.update();
  }

  let userLocation;
  if (checkCookie("userLocation") === false) {
    userLocation = resolveCityIdFromUserIp();
  } else {
    userLocation = JSON.parse(decodeURIComponent(getCookie("userLocation")));
    setUserCityId(userLocation.cityId);
  }
});
