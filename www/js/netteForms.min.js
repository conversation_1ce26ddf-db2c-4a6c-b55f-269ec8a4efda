/*! netteForms.js | (c) 2004, 2016 <PERSON> (http://davidgrudl.com) */
(function(f,p){if(f.JSON)if("function"===typeof define&&define.amd)define(function(){return p(f)});else if("object"===typeof module&&"object"===typeof module.exports)module.exports=p(f);else{var d=!f.Nette||!f.Nette.noInit;f.Nette=p(f);d&&f.Nette.initOnLoad()}})("undefined"!==typeof window?window:this,function(f){function p(a){return function(b){return a.call(this,b)}}var d={formErrors:[],version:"2.4",addEvent:function(a,b,c){a.addEventListener?a.addEventListener(b,c):"DOMContentLoaded"===b?a.attachEvent("onreadystatechange",
function(){"complete"===a.readyState&&c.call(this)}):a.attachEvent("on"+b,p(c))},getValue:function(a){var b;if(a){if(a.tagName){if("radio"===a.type){var c=a.form.elements;for(b=0;b<c.length;b++)if(c[b].name===a.name&&c[b].checked)return c[b].value;return null}if("file"===a.type)return a.files||a.value;if("select"===a.tagName.toLowerCase()){b=a.selectedIndex;var c=a.options,e=[];if("select-one"===a.type)return 0>b?null:c[b].value;for(b=0;b<c.length;b++)c[b].selected&&e.push(c[b].value);return e}if(a.name&&
a.name.match(/\[\]$/)){c=a.form.elements[a.name].tagName?[a]:a.form.elements[a.name];e=[];for(b=0;b<c.length;b++)("checkbox"!==c[b].type||c[b].checked)&&e.push(c[b].value);return e}return"checkbox"===a.type?a.checked:"textarea"===a.tagName.toLowerCase()?a.value.replace("\r",""):a.value.replace("\r","").replace(/^\s+|\s+$/g,"")}return a[0]?d.getValue(a[0]):null}return null},getEffectiveValue:function(a){var b=d.getValue(a);a.getAttribute&&b===a.getAttribute("data-nette-empty-value")&&(b="");return b},
validateControl:function(a,b,c,e,g){a=a.tagName?a:a[0];b=b||d.parseJSON(a.getAttribute("data-nette-rules"));e=void 0===e?{value:d.getEffectiveValue(a)}:e;for(var r=0,f=b.length;r<f;r++){var h=b[r],k=h.op.match(/(~)?([^?]+)/),m=h.control?a.form.elements.namedItem(h.control):a;h.neg=k[1];h.op=k[2];h.condition=!!h.rules;if(m)if("optional"===h.op)g=!d.validateRule(a,":filled",null,e);else if(!g||h.condition||":filled"===h.op)if(m=m.tagName?m:m[0],k=a===m?e:{value:d.getEffectiveValue(m)},k=d.validateRule(m,
h.op,h.arg,k),null!==k)if(h.neg&&(k=!k),h.condition&&k){if(!d.validateControl(a,h.rules,c,e,":blank"===h.op?!1:g))return!1}else if(!h.condition&&!k&&!d.isDisabled(m)){if(!c){var p=d.isArray(h.arg)?h.arg:[h.arg];b=h.msg.replace(/%(value|\d+)/g,function(b,c){return d.getValue("value"===c?m:a.form.elements.namedItem(p[c].control))});d.addError(m,b)}return!1}}return c||"number"!==a.type||a.validity.valid?!0:(d.addError(a,"Please enter a valid value."),!1)},validateForm:function(a){a=a.form||a;var b=!1;
d.formErrors=[];if(a["nette-submittedBy"]&&null!==a["nette-submittedBy"].getAttribute("formnovalidate"))if(b=d.parseJSON(a["nette-submittedBy"].getAttribute("data-nette-validation-scope")),b.length)b=new RegExp("^("+b.join("-|")+"-)");else return d.showFormErrors(a,[]),!0;var c={},e,g;for(e=0;e<a.elements.length;e++)if(g=a.elements[e],!g.tagName||g.tagName.toLowerCase()in{input:1,select:1,textarea:1,button:1}){if("radio"===g.type){if(c[g.name])continue;c[g.name]=!0}if(!(b&&!g.name.replace(/]\[|\[|]|$/g,
"-").match(b)||d.isDisabled(g)||d.validateControl(g)||d.formErrors.length))return!1}b=!d.formErrors.length;d.showFormErrors(a,d.formErrors);return b},isDisabled:function(a){if("radio"===a.type){for(var b=0,c=a.form.elements;b<c.length;b++)if(c[b].name===a.name&&!c[b].disabled)return!1;return!0}return a.disabled},addError:function(a,b){d.formErrors.push({element:a,message:b})},showFormErrors:function(a,b){for(var c=[],e,g=0;g<b.length;g++){var r=b[g].element,f=b[g].message;d.inArray(c,f)||(c.push(f),
!e&&r.focus&&(e=r))}c.length&&(alert(c.join("\n")),e&&e.focus())},expandRuleArgument:function(a,b){if(b&&b.control){var c=a.elements.namedItem(b.control),e={value:d.getEffectiveValue(c)};d.validateControl(c,null,!0,e);b=e.value}return b},validateRule:function(a,b,c,e){e=void 0===e?{value:d.getEffectiveValue(a)}:e;":"===b.charAt(0)&&(b=b.substr(1));b=b.replace("::","_");b=b.replace(/\\/g,"");for(var g=d.isArray(c)?c.slice(0):[c],f=0,p=g.length;f<p;f++)g[f]=d.expandRuleArgument(a.form,g[f]);return d.validators[b]?
d.validators[b](a,d.isArray(c)?g:g[0],e.value,e):null},validators:{filled:function(a,b,c){return"number"===a.type&&a.validity.badInput?!0:""!==c&&!1!==c&&null!==c&&(!d.isArray(c)||!!c.length)&&(!f.FileList||!(c instanceof f.FileList)||c.length)},blank:function(a,b,c){return!d.validators.filled(a,b,c)},valid:function(a,b,c){return d.validateControl(a,null,!0)},equal:function(a,b,c){function e(a){return"number"===typeof a||"string"===typeof a?""+a:!0===a?"1":""}if(void 0===b)return null;c=d.isArray(c)?
c:[c];b=d.isArray(b)?b:[b];a=0;var g=c.length;a:for(;a<g;a++){for(var f=0,p=b.length;f<p;f++)if(e(c[a])===e(b[f]))continue a;return!1}return!0},notEqual:function(a,b,c){return void 0===b?null:!d.validators.equal(a,b,c)},minLength:function(a,b,c){if("number"===a.type){if(a.validity.tooShort)return!1;if(a.validity.badInput)return null}return c.length>=b},maxLength:function(a,b,c){if("number"===a.type){if(a.validity.tooLong)return!1;if(a.validity.badInput)return null}return c.length<=b},length:function(a,
b,c){if("number"===a.type){if(a.validity.tooShort||a.validity.tooLong)return!1;if(a.validity.badInput)return null}b=d.isArray(b)?b:[b,b];return(null===b[0]||c.length>=b[0])&&(null===b[1]||c.length<=b[1])},email:function(a,b,c){return/^("([ !#-[\]-~]|\\[ -~])+"|[-a-z0-9!#$%&'*+\/=?^_`{|}~]+(\.[-a-z0-9!#$%&'*+\/=?^_`{|}~]+)*)@([0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)+[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?$/i.test(c)},
url:function(a,b,c,d){/^[a-z\d+.-]+:/.test(c)||(c="http://"+c);return/^https?:\/\/((([-_0-9a-z\u00C0-\u02FF\u0370-\u1EFF]+\.)*[0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)?[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|\[[0-9a-f:]{3,39}\])(:\d{1,5})?(\/\S*)?$/i.test(c)?(d.value=c,!0):!1},regexp:function(a,b,c){a="string"===typeof b?b.match(/^\/(.*)\/([imu]*)$/):
!1;try{return a&&(new RegExp(a[1],a[2].replace("u",""))).test(c)}catch(d){}},pattern:function(a,b,c){try{return"string"===typeof b?(new RegExp("^(?:"+b+")$")).test(c):null}catch(d){}},integer:function(a,b,c){return"number"===a.type&&a.validity.badInput?!1:/^-?[0-9]+$/.test(c)},"float":function(a,b,c,d){if("number"===a.type&&a.validity.badInput)return!1;c=c.replace(" ","").replace(",",".");return/^-?[0-9]*[.,]?[0-9]+$/.test(c)?(d.value=c,!0):!1},min:function(a,b,c){if("number"===a.type){if(a.validity.rangeUnderflow)return!1;
if(a.validity.badInput)return null}return d.validators.range(a,[b,null],c)},max:function(a,b,c){if("number"===a.type){if(a.validity.rangeOverflow)return!1;if(a.validity.badInput)return null}return d.validators.range(a,[null,b],c)},range:function(a,b,c){if("number"===a.type){if(a.validity.rangeUnderflow||a.validity.rangeOverflow)return!1;if(a.validity.badInput)return null}return d.isArray(b)?(null===b[0]||parseFloat(c)>=b[0])&&(null===b[1]||parseFloat(c)<=b[1]):null},submitted:function(a,b,c){return a.form["nette-submittedBy"]===
a},fileSize:function(a,b,c){if(f.FileList)for(a=0;a<c.length;a++)if(c[a].size>b)return!1;return!0},image:function(a,b,c){if(f.FileList&&c instanceof f.FileList)for(a=0;a<c.length;a++)if((b=c[a].type)&&"image/gif"!==b&&"image/png"!==b&&"image/jpeg"!==b)return!1;return!0}},toggleForm:function(a,b){var c;d.toggles={};for(c=0;c<a.elements.length;c++)a.elements[c].tagName.toLowerCase()in{input:1,select:1,textarea:1,button:1}&&d.toggleControl(a.elements[c],null,null,!b);for(c in d.toggles)d.toggle(c,d.toggles[c],
b)},toggleControl:function(a,b,c,e,g){b=b||d.parseJSON(a.getAttribute("data-nette-rules"));g=void 0===g?{value:d.getEffectiveValue(a)}:g;for(var f=!1,p=[],h=function(){d.toggleForm(a.form,a)},k,m=0,v=b.length;m<v;m++){var n=b[m],t=n.op.match(/(~)?([^?]+)/),l=n.control?a.form.elements.namedItem(n.control):a;if(l){k=c;if(!1!==c){n.neg=t[1];n.op=t[2];k=a===l?g:{value:d.getEffectiveValue(l)};k=d.validateRule(l,n.op,n.arg,k);if(null===k)continue;else n.neg&&(k=!k);n.rules||(c=k)}if(n.rules&&d.toggleControl(a,
n.rules,k,e,g)||n.toggle){f=!0;if(e)for(var t=!document.addEventListener,w=l.tagName?l.name:l[0].name,l=l.tagName?l.form.elements:l,q=0;q<l.length;q++)l[q].name!==w||d.inArray(p,l[q])||(d.addEvent(l[q],t&&l[q].type in{checkbox:1,radio:1}?"click":"change",h),p.push(l[q]));for(var u in n.toggle||[])Object.prototype.hasOwnProperty.call(n.toggle,u)&&(d.toggles[u]=d.toggles[u]||(n.toggle[u]?k:!k))}}}return f},parseJSON:function(a){return"{op"===(a||"").substr(0,3)?eval("["+a+"]"):JSON.parse(a||"[]")},
toggle:function(a,b,c){if(a=document.getElementById(a))a.style.display=b?"":"none"},initForm:function(a){a.noValidate="novalidate";d.addEvent(a,"submit",function(b){d.validateForm(a)||(b&&b.stopPropagation?(b.stopPropagation(),b.preventDefault()):f.event&&(event.cancelBubble=!0,event.returnValue=!1))});d.toggleForm(a)},initOnLoad:function(){d.addEvent(document,"DOMContentLoaded",function(){for(var a=0;a<document.forms.length;a++)for(var b=document.forms[a],c=0;c<b.elements.length;c++)if(b.elements[c].getAttribute("data-nette-rules")){d.initForm(b);
break}d.addEvent(document.body,"click",function(a){a=a.target||a.srcElement;a.form&&a.type in{submit:1,image:1}&&(a.form["nette-submittedBy"]=a)})})},isArray:function(a){return"[object Array]"===Object.prototype.toString.call(a)},inArray:function(a,b){if([].indexOf)return-1<a.indexOf(b);for(var c=0;c<a.length;c++)if(a[c]===b)return!0;return!1},webalize:function(a){a=a.toLowerCase();var b="",c,e;for(c=0;c<a.length;c++)e=d.webalizeTable[a.charAt(c)],b+=e?e:a.charAt(c);return b.replace(/[^a-z0-9]+/g,
"-").replace(/^-|-$/g,"")},webalizeTable:{"\u00e1":"a","\u00e4":"a","\u010d":"c","\u010f":"d","\u00e9":"e","\u011b":"e","\u00ed":"i","\u013e":"l","\u0148":"n","\u00f3":"o","\u00f4":"o","\u0159":"r","\u0161":"s","\u0165":"t","\u00fa":"u","\u016f":"u","\u00fd":"y","\u017e":"z"}};return d});
