// Kaufino JavaScript
var kaufino = {};

kaufino.loader = {
  ready: function (fn) {
    if (document.readyState == "complete") {
      return fn();
    }

    if (window.addEventListener) {
      window.addEventListener("load", fn, false);
    } else if (window.attachEvent) {
      window.attachEvent("onload", fn);
    } else {
      window.onload = fn;
    }
  },
};

let originalParentMap = null;
let nextSiblingMap = null;
let originalParentBranch = null;
let nextSiblingBranch = null;

function moveElements() {
  var map = document.getElementById("map");
  var storeInfo = document.querySelector(".storeInfo");
  var nearestStoreBranch = document.querySelector(".nearest-store-branch");
  var storeContentMap = document.querySelector(".storeContent__map");

  if (!storeContentMap && !storeInfo) return;

  // Moving map after storeInfo
  if (map && storeInfo) {
    if (!originalParentMap) {
      originalParentMap = map.parentNode;
      nextSiblingMap = map.nextSibling;
    }

    if (window.innerWidth < 1179) {
      if (storeInfo.nextSibling) {
        storeInfo.parentNode.insertBefore(map, storeInfo.nextSibling);
      } else {
        storeInfo.parentNode.appendChild(map);
      }
    } else {
      if (nextSiblingMap) {
        originalParentMap.insertBefore(map, nextSiblingMap);
      } else {
        originalParentMap.appendChild(map);
      }
    }
  }

  // Moving nearestStoreBranch after map or storeInfo if map is not present
  if (nearestStoreBranch) {
    if (!originalParentBranch) {
      originalParentBranch = nearestStoreBranch.parentNode;
      nextSiblingBranch = nearestStoreBranch.nextSibling;
    }

    var targetElement = storeContentMap ? storeContentMap : storeInfo;

    if (window.innerWidth < 1179) {
      if (targetElement.nextSibling) {
        targetElement.parentNode.insertBefore(
          nearestStoreBranch,
          targetElement.nextSibling
        );
      } else {
        targetElement.parentNode.appendChild(nearestStoreBranch);
      }
    } else {
      if (nextSiblingBranch) {
        originalParentBranch.insertBefore(
          nearestStoreBranch,
          nextSiblingBranch
        );
      } else {
        originalParentBranch.appendChild(nearestStoreBranch);
      }
    }
  }
}
moveElements();
window.addEventListener("resize", moveElements);

kaufino.loader.ready(function () {
  // FAQ SECTION
  const faqQuestions = document.querySelectorAll(".faq-question");

  faqQuestions.forEach((question) => {
    const arrow = question.querySelector(".arrow");
    const answer = question.querySelector(".faq-answer");

    question.addEventListener("click", () => {
      answer.classList.toggle("show");
      question.classList.toggle("show");
      arrow.classList.toggle("rotate");
    });
  });

  // Mobile menu click
  var el_menuIcon = document.getElementsByClassName("k-header__menu-icon");
  var el_menuNav = document.getElementsByClassName("k-header__nav");

  el_menuIcon[0].addEventListener("click", function (e) {
    e.preventDefault();
    el_menuNav[0].classList.toggle("show");
  });

  // TAB MENU
  const tabLinks = document.querySelectorAll(".k-tabs__tabLink");
  const tabs = document.querySelectorAll(".k-tabs_tab");

  tabLinks.forEach((link) => {
    link.addEventListener("click", () => {
      const targetTab = document.getElementById(link.dataset.tab);

      tabs.forEach((tab) => tab.classList.remove("active"));
      tabLinks.forEach((link) => link.classList.remove("active"));

      targetTab.classList.add("active");
      link.classList.add("active");
    });
  });

  // SEARCH
  var el_search_input = document.getElementsByClassName("js-search-input");
  var el_search_submit = document.getElementsByClassName("js-search-submit");
  var el_wrapper = document.getElementsByClassName("k-header__search-wrapper");
  var search_url = el_search_input[0].dataset.searchUrl;
  var search_submit_url = el_search_submit[0].dataset.searchUrl;

  // Click SEARCH - Submit
  el_search_submit[0].addEventListener("click", function (e) {
    if (el_search_input[0].value == "") {
      return false;
    }

    e.preventDefault();
    var s_query = el_search_input[0].value;
    var replace_search_submit_url = search_submit_url.replace("/q", "/");
    var q_search_url = replace_search_submit_url + s_query;

    window.location.href = q_search_url;
  });

  // Click SEARCH - Input
  el_search_input[0].addEventListener("click", function () {
    getSearch(search_url);
  });

  // Type SEARCH - Input
  el_search_input[0].addEventListener("input", function () {
    var s_query = el_search_input[0].value;
    var q_search_url = search_url + "/" + s_query;
    getSearch(q_search_url);
  });

  // Click outside SEARCH
  window.addEventListener("click", function () {
    el_wrapper[0].classList.remove("show");
  });

  // getSearch function
  function getSearch(q_search_url) {
    getJSON(
      q_search_url,
      function (data) {
        var search_markup = "";
        el_wrapper[0].innerHTML = "";

        for (index = 0; index < data.length; ++index) {
          search_markup +=
            '<a href="' +
            data[index].destinationUrl +
            '" class="k-search__item"><span class="k-search__image-wrapper"><img src="' +
            data[index].logoUrl +
            '" alt="" class="k-search__image"></span><small class="k-search__name">' +
            data[index].name +
            "</small></a>";
        }

        el_wrapper[0].innerHTML = search_markup;
        el_wrapper[0].classList.add("show");
      },
      function (xhr) {
        console.error(xhr);
      }
    );
  }

  // getJSON function
  function getJSON(path, success, error) {
    var xhr = new XMLHttpRequest();
    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === 200) {
          if (success) success(JSON.parse(xhr.responseText));
        } else {
          if (error) error(xhr);
        }
      }
    };
    xhr.open("GET", path, true);
    xhr.send();
  }

  // Coupon popup
  var c_el_wrapper = document.getElementsByClassName("k-modal");
  var c_el_code_copy = document.getElementsByClassName("k-modal__code-copy");
  var c_el_close_button = document.getElementsByClassName("k-modal__close");
  var c_el_copy_button = document.getElementsByClassName("k-modal__code-btn");

  // Click coupon link
  var c_js_click_link = document.getElementsByClassName("js-click-link");

  if (c_js_click_link.length > 0) {
    c_js_click_link[0].addEventListener("click", function () {
      var redirect_link = c_js_click_link[0].dataset.popupLink;
      setTimeout(function () {
        window.location.replace(redirect_link);
      }, 250);
    });
  }

  // Click outside Coupon Popup
  document.addEventListener("click", function (event) {
    if (document.getElementsByClassName("k-modal")[0] == event.target) {
      c_el_wrapper[0].classList.remove("show");
    }
  });

  // Click close button
  if (c_el_close_button.length > 0) {
    c_el_close_button[0].addEventListener("click", function () {
      c_el_wrapper[0].classList.remove("show");
    });
  }

  // Copy code
  if (c_el_copy_button.length > 0) {
    c_el_copy_button[0].addEventListener("click", function () {
      /* Get the text field */
      var copyText = document.getElementById("copyInput");

      /* Select the text field */
      copyText.select();
      copyText.setSelectionRange(0, 99999); /* For mobile devices */

      /* Copy the text inside the text field */
      document.execCommand("copy");

      /* Alert the copied text */
      console.log("Copied: " + copyText.value);
      c_el_code_copy[0].classList.add("copied");

      setTimeout(function () {
        c_el_code_copy[0].classList.remove("copied");
      }, 3000);
    });
  }

  // ADS scroll from users
  var windowW = window.innerWidth;
  var sidebar = document.getElementsByClassName("float-wrapper");

  if (windowW >= 1024 && sidebar.length > 0) {
    const floatWrapper = document.querySelector(".float-wrapper");
    const stopElement = document.querySelector(".float-wrapper__stop");

    if (!floatWrapper || !stopElement) {
      console.log(".float-wrapper nebo .float-wrapper__stop nebyly nalezeny.");
      return;
    }

    let floatWrapperOffsetTop =
      floatWrapper.getBoundingClientRect().top + window.scrollY;
    let stopElementOffsetTop =
      stopElement.getBoundingClientRect().top + window.scrollY;

    function recalculateOffsets() {
      if (window.scrollY < 250) {
        floatWrapperOffsetTop =
          floatWrapper.getBoundingClientRect().top + window.scrollY;
        stopElementOffsetTop =
          stopElement.getBoundingClientRect().top + window.scrollY;
      }
    }

    function handleScroll() {
      const scrollY = window.scrollY;
      const floatWrapperHeight = floatWrapper.offsetHeight;

      // Zastavovací bod - vršek float-wrapper__stop mínus výška float-wrapper
      const stopPoint = stopElementOffsetTop - floatWrapperHeight;

      // Výpočet nového posunu
      if (scrollY >= floatWrapperOffsetTop && scrollY <= stopPoint) {
        floatWrapper.style.position = "fixed";
        floatWrapper.style.top = "0px";
      } else if (scrollY > stopPoint) {
        floatWrapper.style.position = "absolute";
        floatWrapper.style.top = `${stopPoint - floatWrapperOffsetTop}px`;
      } else {
        floatWrapper.style.position = "relative";
        floatWrapper.style.top = "0px";
      }
    }

    window.addEventListener("scroll", handleScroll);
    window.addEventListener("load", recalculateOffsets);
    handleScroll(); // Spuštění při načtení pro správné nastavení
    console.log("sticky sidebar");
  }

  // Funkce pro odstranění třídy "hidden" ze všech prvků s určitou třídou
  function removeHiddenClass(targetClass) {
    var elements = document.querySelectorAll("." + targetClass);
    elements.forEach(function (element) {
      element.classList.remove("hidden");
    });
  }

  // Najdeme tlačítko s třídou "toggle-button"
  var offerButton = document.querySelector(".js-show-offer");

  if (offerButton) {
    // Připojíme událost "click" a zavoláme funkci removeHiddenClass s konkrétní třídou po kliknutí
    offerButton.addEventListener("click", function () {
      removeHiddenClass("k-offers__item");
      offerButton.style.display = "none";
    });
  }

  // Najdeme tlačítko s třídou "toggle-button"
  var shopButton = document.querySelector(".js-show-shop");

  if (shopButton) {
    // Připojíme událost "click" a zavoláme funkci removeHiddenClass s konkrétní třídou po kliknutí
    shopButton.addEventListener("click", function () {
      removeHiddenClass("k-shop__item");
      shopButton.style.display = "none";
    });
  }

  // Najdeme všetky tlačídla s třídou "toggle-button"
  var tagButtons = document.querySelectorAll(".js-show-tag");

  tagButtons.forEach(function (tagButton) {
    tagButton.addEventListener("click", function () {
      var parentSection = this.closest(".k-tag");

      var container = parentSection ? parentSection : this.closest("div");

      if (container) {
        var hiddenElements = container.querySelectorAll(".k-tag__inner.hidden");
        hiddenElements.forEach(function (element) {
          element.classList.remove("hidden");
        });
      }

      this.style.display = "none";
    });
  });

  // Najdeme tlačítko s třídou "toggle-button"
  var leafletButton = document.querySelector(".js-show-leaflet");

  if (leafletButton) {
    // Připojíme událost "click" a zavoláme funkci removeHiddenClass s konkrétní třídou po kliknutí
    leafletButton.addEventListener("click", function () {
      removeHiddenClass("k-leaflets__item");
      leafletButton.style.display = "none";
    });
  }

  // Najdeme tlačítko s třídou "toggle-button"
  var allButton = document.querySelector(".js-show-all-btn");

  if (allButton) {
    // Připojíme událost "click" a zavoláme funkci removeHiddenClass s konkrétní třídou po kliknutí
    allButton.addEventListener("click", function () {
      document
        .getElementsByClassName("js-all-btn")[0]
        .classList.remove("hidden");
    });
  }

  // Adsense - SHOW visible ADS
  function showVisibleAds() {
    var analyticsOff = 0;

    var el_adsbygoogle = document.getElementsByClassName("adsbygoogle");
    //window.getComputedStyle(el_adsbygoogle[0]).display === "none"

    //console.log(el_adsbygoogle.length);

    // Remove hide Ads
    /*
        for (index = 0; index < el_adsbygoogle.length; ++index) {
            console.log(window.getComputedStyle(el_adsbygoogle[index]).display === "none");

            if (window.getComputedStyle(el_adsbygoogle[index]).display === "none") {
                el_adsbygoogle[index].remove();
                analyticsOff += 1;
            }            
        }
        
        console.log("Remove ads: " + analyticsOff);
        */

    // Show visible Ads
    el_adsbygoogle = document.getElementsByClassName("adsbygoogle");

    for (index = 0; index < el_adsbygoogle.length; ++index) {
      var adsbygoogleStatus = el_adsbygoogle[index].dataset.adsbygoogleStatus;

      //console.log(adsbygoogleStatus);
      //console.log(el_adsbygoogle[index].style.width);
      //console.log("##");

      if (adsbygoogleStatus != "done") {
        (adsbygoogle = window.adsbygoogle || []).push({});
      }
    }
  }

  let originalPosition = null;
});
