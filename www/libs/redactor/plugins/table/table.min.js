!function(a){a.add("plugin","table",{translations:{en:{table:"Table","insert-table":"Insert table","insert-row-above":"Insert row above","insert-row-below":"Insert row below","insert-column-left":"Insert column left","insert-column-right":"Insert column right","add-head":"Add head","delete-head":"Delete head","delete-column":"Delete column","delete-row":"Delete row","delete-table":"Delete table"}},init:function(e){this.app=e,this.lang=e.lang,this.opts=e.opts,this.caret=e.caret,this.editor=e.editor,this.toolbar=e.toolbar,this.component=e.component,this.inspector=e.inspector,this.insertion=e.insertion,this.selection=e.selection},ondropdown:{table:{observe:function(e){this._observeDropdown(e)}}},onbottomclick:function(){this.insertion.insertToEnd(this.editor.getLastNode(),"table")},start:function(){var e={observe:"table","insert-table":{title:this.lang.get("insert-table"),api:"plugin.table.insert"},"insert-row-above":{title:this.lang.get("insert-row-above"),classname:"redactor-table-item-observable",api:"plugin.table.addRowAbove"},"insert-row-below":{title:this.lang.get("insert-row-below"),classname:"redactor-table-item-observable",api:"plugin.table.addRowBelow"},"insert-column-left":{title:this.lang.get("insert-column-left"),classname:"redactor-table-item-observable",api:"plugin.table.addColumnLeft"},"insert-column-right":{title:this.lang.get("insert-column-right"),classname:"redactor-table-item-observable",api:"plugin.table.addColumnRight"},"add-head":{title:this.lang.get("add-head"),classname:"redactor-table-item-observable",api:"plugin.table.addHead"},"delete-head":{title:this.lang.get("delete-head"),classname:"redactor-table-item-observable",api:"plugin.table.deleteHead"},"delete-column":{title:this.lang.get("delete-column"),classname:"redactor-table-item-observable",api:"plugin.table.deleteColumn"},"delete-row":{title:this.lang.get("delete-row"),classname:"redactor-table-item-observable",api:"plugin.table.deleteRow"},"delete-table":{title:this.lang.get("delete-table"),classname:"redactor-table-item-observable",api:"plugin.table.deleteTable"}},t={title:this.lang.get("table")},n=this.toolbar.addButtonBefore("link","table",t);n.setIcon('<i class="re-icon-table"></i>'),n.setDropdown(e)},insert:function(){for(var e=this.component.create("table"),t=0;t<2;t++)e.addRow(3);e=this.insertion.insertHtml(e),this.caret.setStart(e)},addRowAbove:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent(),n=e.addRowTo(t,"before");this.caret.setStart(n)}},addRowBelow:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent(),n=e.addRowTo(t,"after");this.caret.setStart(n)}},addColumnLeft:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent();this.selection.save(),e.addColumnTo(t,"left"),this.selection.restore()}},addColumnRight:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent();this.selection.save(),e.addColumnTo(t,"right"),this.selection.restore()}},addHead:function(){var e=this._getComponent();e&&(this.selection.save(),e.addHead(),this.selection.restore())},deleteHead:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent();0!==a.dom(t).closest("thead").length?(e.removeHead(),this.caret.setStart(e)):(this.selection.save(),e.removeHead(),this.selection.restore())}},deleteColumn:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent(),n=a.dom(t).closest("td, th"),i=n.nextElement().get(),o=n.prevElement().get();e.removeColumn(t),i?this.caret.setStart(i):o?this.caret.setEnd(o):this.deleteTable()}},deleteRow:function(){var e=this._getComponent();if(e){var t=this.selection.getCurrent(),n=a.dom(t).closest("tr"),i=n.nextElement().get(),o=n.prevElement().get();e.removeRow(t),i?this.caret.setStart(i):o?this.caret.setEnd(o):this.deleteTable()}},deleteTable:function(){var e=this._getTable();e&&this.component.remove(e)},_getTable:function(){var e=this.selection.getCurrent(),t=this.inspector.parse(e);if(t.isTable())return t.getTable()},_getComponent:function(){var e=this.selection.getCurrent(),t=this.inspector.parse(e);if(t.isTable()){var n=t.getTable();return this.component.create("table",n)}},_observeDropdown:function(e){var t=this._getTable(),n=e.getItemsByClass("redactor-table-item-observable"),i=e.getItem("insert-table");t?(this._observeItems(n,"enable"),i.disable()):(this._observeItems(n,"disable"),i.enable())},_observeItems:function(e,t){for(var n=0;n<e.length;n++)e[n][t]()}})}(Redactor),function(r){r.add("class","table.component",{mixins:["dom","component"],init:function(e,t){return this.app=e,t&&void 0!==t.cmnt?t:this._init(t)},addHead:function(){this.removeHead();var e=this.$element.find("tr").first().children("td, th").length,t=r.dom("<thead>"),n=this._buildRow(e,"<th>");t.append(n),this.$element.prepend(t)},addRow:function(e){var t=this._buildRow(e);return this.$element.append(t),t},addRowTo:function(e,t){return this._addRowTo(e,t)},addColumnTo:function(e,o){var t=r.dom(e),n=t.closest("tr"),i=t.closest("td, th"),a=0;n.find("td, th").each(function(e,t){e===i.get()&&(a=t)}),this.$element.find("tr").each(function(e){var t=r.dom(e).find("td, th").get(a),n=r.dom(t),i=n.clone();i.html('<div data-redactor-tag="tbr"></div>'),"right"===o?n.after(i):n.before(i)})},removeHead:function(){var e=this.$element.find("thead");0!==e.length&&e.remove()},removeRow:function(e){r.dom(e).closest("tr").remove()},removeColumn:function(e){var t=r.dom(e),n=t.closest("tr"),i=t.closest("td, th"),o=0;n.find("td, th").each(function(e,t){e===i.get()&&(o=t)}),this.$element.find("tr").each(function(e){var t=r.dom(e).find("td, th").get(o);r.dom(t).remove()})},_init:function(e){var t,n;if(void 0!==e){var i=r.dom(e),o=i.get(),a=i.closest("figure");0!==a.length?n=(t=a).find("table").get():"TABLE"===o.tagName&&(n=o)}this._buildWrapper(t),this._buildElement(n),this._initWrapper()},_addRowTo:function(e,t){var n=r.dom(e).closest("tr");if(0!==n.length){var i=n.children("td, th").length,o=this._buildRow(i);return n[t](o),o}},_buildRow:function(e,t){t=t||"<td>";for(var n=r.dom("<tr>"),i=0;i<e;i++){var o=r.dom(t);o.attr("contenteditable",!0),o.html('<div data-redactor-tag="tbr"></div>'),n.append(o)}return n},_buildElement:function(e){e?this.$element=r.dom(e):(this.$element=r.dom("<table>"),this.append(this.$element))},_buildWrapper:function(e){e=e||"<figure>",this.parse(e)},_initWrapper:function(){this.addClass("redactor-component"),this.attr({"data-redactor-type":"table",tabindex:"-1",contenteditable:!1}),this.app.detector.isIe()&&this.removeAttr("contenteditable")}})}(Redactor);