!function(i){i.add("plugin","fullscreen",{translations:{en:{fullscreen:"Fullscreen"}},init:function(t){this.app=t,this.opts=t.opts,this.lang=t.lang,this.$win=t.$win,this.$doc=t.$doc,this.$body=t.$body,this.editor=t.editor,this.toolbar=t.toolbar,this.container=t.container,this.selection=t.selection,this.isOpen=!1,this.docScroll=0},start:function(){var t={title:this.lang.get("fullscreen"),api:"plugin.fullscreen.toggle"};this.toolbar.addButton("fullscreen",t).setIcon('<i class="re-icon-expand"></i>'),this.$target=this.toolbar.isTarget()?this.toolbar.getTargetElement():this.$body,this.opts.fullscreen&&this.toggle()},toggle:function(){return this.isOpen?this.close():this.open()},open:function(){this.docScroll=this.$doc.scrollTop(),this._createPlacemarker(),this.selection.save();var t=this.container.getElement(),e=this.editor.getElement(),s=this.toolbar.isTarget()?i.dom("body, html"):this.$target;this.opts.toolbarExternal&&this._buildInternalToolbar(),this.$target.prepend(t),this.$target.addClass("redactor-body-fullscreen"),t.addClass("redactor-box-fullscreen"),this.isTarget&&t.addClass("redactor-box-fullscreen-target"),s.css("overflow","hidden"),this.opts.maxHeight&&e.css("max-height",""),this.opts.minHeight&&e.css("min-height",""),this._resize(),this.$win.on("resize.redactor-plugin-fullscreen",this._resize.bind(this)),this.$doc.scrollTop(0),this.toolbar.getButton("fullscreen").setIcon('<i class="re-icon-retract"></i>'),this.selection.restore(),this.isOpen=!0,this.opts.zindex=1051,window.jQuery&&jQuery(document).off("focusin.modal")},close:function(){this.isOpen=!1,this.opts.zindex=!1,this.selection.save();var t=this.container.getElement(),e=this.editor.getElement(),s=i.dom("body, html");this.opts.toolbarExternal&&this._buildExternalToolbar(),this.$target.removeClass("redactor-body-fullscreen"),this.$win.off("resize.redactor-plugin-fullscreen"),s.css("overflow",""),t.removeClass("redactor-box-fullscreen redactor-box-fullscreen-target"),e.css("height","auto"),this.opts.minHeight&&e.css("minHeight",this.opts.minHeight),this.opts.maxHeight&&e.css("maxHeight",this.opts.maxHeight),this.toolbar.getButton("fullscreen").setIcon('<i class="re-icon-expand"></i>'),this._removePlacemarker(t),this.selection.restore(),this.$doc.scrollTop(this.docScroll)},_resize:function(){var t=this.toolbar.getElement(),e=this.editor.getElement(),s=this.$win.height()-t.height();e.height(s)},_buildInternalToolbar:function(){var t=this.toolbar.getWrapper(),e=this.toolbar.getElement();t.addClass("redactor-toolbar-wrapper"),t.append(e),e.removeClass("redactor-toolbar-external"),$container.prepend(t)},_buildExternalToolbar:function(){var t=this.toolbar.getWrapper(),e=this.toolbar.getElement();this.$external=i.dom(this.opts.toolbarExternal),e.addClass("redactor-toolbar-external"),this.$external.append(e),t.remove()},_createPlacemarker:function(){var t=this.container.getElement();this.$placemarker=i.dom("<span />"),t.after(this.$placemarker)},_removePlacemarker:function(t){this.$placemarker.before(t),this.$placemarker.remove()}})}(Redactor);