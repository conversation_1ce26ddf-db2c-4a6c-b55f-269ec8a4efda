!function(o){o.add("plugin","variable",{translations:{en:{change:"Change",variable:"Variable","variable-select":"Please, select a variable"}},modals:{variable:""},init:function(t){this.app=t,this.lang=t.lang,this.opts=t.opts,this.toolbar=t.toolbar,this.component=t.component,this.insertion=t.insertion,this.inspector=t.inspector,this.selection=t.selection},onmodal:{variable:{open:function(t,e){this._build(t)}}},oncontextbar:function(t,e){var i=this.inspector.parse(t.target);if(i.isComponentType("variable")){var a=i.getComponent(),n={change:{title:this.lang.get("change"),api:"plugin.variable.open",args:a},remove:{title:this.lang.get("delete"),api:"plugin.variable.remove",args:a}};e.set(t,a,n,"bottom")}},start:function(){if(this.opts.variables){var t={title:this.lang.get("variable"),api:"plugin.variable.open"};this.toolbar.addButton("variable",t).setIcon('<i class="re-icon-variable"></i>')}},open:function(){var t={title:this.lang.get("variable"),width:"600px",name:"variable"};this.$currentItem=this._getCurrent(),this.app.api("module.modal.build",t)},insert:function(t){this.app.api("module.modal.close");var e=t.attr("data-type"),i=this.component.create("variable");i.html(e),this.insertion.insertRaw(i)},remove:function(t){this.component.remove(t)},_getCurrent:function(){var t=this.selection.getCurrent(),e=this.inspector.parse(t);if(e.isComponentType("variable"))return this.component.build(e.getComponent())},_build:function(t){var e=t.getBody(),i=this._buildLabel(),a=this._buildList();this._buildItems(a),e.html(""),e.append(i),e.append(a)},_buildLabel:function(){var t=o.dom("<label>");return t.html(this.lang.parse("## variable-select ##:")),t},_buildList:function(){var t=o.dom("<ul>");return t.addClass("redactor-variables-list"),t},_buildItems:function(t){for(var e=this._getCurrentType(),i=this.opts.variables,a=0;a<i.length;a++){var n=i[a].trim(),r=o.dom("<li>"),s=o.dom("<span>");s.attr("data-type",n),s.html(n),s.on("click",this._toggle.bind(this)),e===n&&s.addClass("redactor-variables-item-selected"),r.append(s),t.append(r)}},_getCurrentType:function(){return!!this.$currentItem&&this.$currentItem.getData().type},_toggle:function(t){var e=o.dom(t.target);this.app.api("plugin.variable.insert",e)}})}(Redactor),Redactor.add("class","variable.component",{mixins:["dom","component"],init:function(t,e){return this.app=t,this.utils=t.utils,e&&void 0!==e.cmnt?e:this._init(e)},getData:function(){return{type:this._getType()}},_init:function(t){t=t||"<span>",this.parse(t),this._initWrapper()},_getType:function(){var t=this.text().trim();return this.utils.removeInvisibleChars(t)},_initWrapper:function(){this.addClass("redactor-component"),this.attr({"data-redactor-type":"variable",tabindex:"-1",contenteditable:!1})}});