!function(d){d.add("plugin","definedlinks",{init:function(t){this.app=t,this.opts=t.opts,this.component=t.component,this.links=[]},onmodal:{link:{open:function(t,i){this.opts.definedlinks&&(this.$modal=t,this.$form=i,this._load())}}},_load:function(){"object"==typeof this.opts.definedlinks?this._build(this.opts.definedlinks):d.ajax.get({url:this.opts.definedlinks,success:this._build.bind(this)})},_build:function(t){if(0===(s=this.$modal.find("#redactor-defined-links")).length){var i=this.$modal.getBody(),n=d.dom('<div class="form-item" />'),s=d.dom('<select id="redactor-defined-links" />');n.append(s),i.prepend(n)}for(var e in this.links=[],s.html(""),s.off("change"),t)if(t.hasOwnProperty(e)&&"object"==typeof t[e]){this.links[e]=t[e];var o=d.dom("<option>");o.val(e),o.html(t[e].name),s.append(o)}s.on("change",this._select.bind(this))},_select:function(t){var i=this.$form.getData(),n=d.dom(t.target).val(),s={text:"",url:""};"0"!==n&&(s.text=this.links[n].name,s.url=this.links[n].url),""!==i.text&&(s={url:s.url}),this.$form.setData(s)}})}(Redactor);