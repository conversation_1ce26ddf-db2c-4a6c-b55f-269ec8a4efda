Redactor.add("plugin","inlinestyle",{translations:{en:{style:"Style"}},init:function(t){this.app=t,this.lang=t.lang,this.toolbar=t.toolbar,this.styles={marked:{title:"Marked",args:"mark"},code:{title:"Code",args:"code"},variable:{title:"Variable",args:"var"},shortcut:{title:"Shortcut",args:"kbd"},sup:{title:"Superscript",args:"sup"},sub:{title:"Subscript",args:"sub"}}},start:function(){var t={};for(var i in this.styles){var s=this.styles[i];t[i]={title:s.title,api:"module.inline.format",args:s.args}}var a=this.toolbar.addButtonAfter("format","inline",{title:this.lang.get("style")});a.setIcon('<i class="re-icon-inline"></i>'),a.setDropdown(t)}});