Redactor.add("plugin","counter",{translations:{en:{words:"words",chars:"chars"}},init:function(t){this.app=t,this.lang=t.lang,this.utils=t.utils,this.editor=t.editor,this.statusbar=t.statusbar},start:function(){this.editor.getElement().on("keyup.redactor-plugin-counter paste.redactor-plugin-counter",this.count.bind(this)),this.count()},stop:function(){this.editor.getElement().off(".redactor-plugin-counter"),this.statusbar.remove("words"),this.statusbar.remove("chars")},count:function(){var t=0,s=0,r=0,e=this.editor.getElement().html();if(""!==(e=this._clean(e))){var a=e.split(/\s+/),i=e.match(/\s/g);t=a?a.length:0,r=i?i.length:0,s=e.length}var n={words:t,characters:s,spaces:r};this.app.broadcast("counter",n),this.statusbar.add("words",this.lang.get("words")+": "+n.words),this.statusbar.add("chars",this.lang.get("chars")+": "+n.characters)},_clean:function(t){return t=(t=(t=(t=(t=(t=(t=t.replace(/<\/(.*?)>/gi," ")).replace(/<(.*?)>/gi,"")).replace(/\t/gi,"")).replace(/\n/gi," ")).replace(/\r/gi," ")).replace(/&nbsp;/g,"1")).trim(),t=this.utils.removeInvisibleChars(t)}});