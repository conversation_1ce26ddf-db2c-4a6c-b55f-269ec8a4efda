Redactor.add("plugin","fontfamily",{translations:{en:{fontfamily:"Font","remove-font-family":"Remove Font Family"}},init:function(t){this.app=t,this.opts=t.opts,this.lang=t.lang,this.inline=t.inline,this.toolbar=t.toolbar,this.fonts=this.opts.fontfamily?this.opts.fontfamily:["Arial","Helvetica","Georgia","Times New Roman","Monospace"]},start:function(){for(var t={},i=0;i<this.fonts.length;i++){var n=this.fonts[i];t[i]={title:n.replace(/'/g,""),api:"plugin.fontfamily.set",args:n}}t.remove={title:this.lang.get("remove-font-family"),api:"plugin.fontfamily.remove"};var o=this.toolbar.addButton("fontfamily",{title:this.lang.get("fontfamily")});o.setIcon('<i class="re-icon-fontfamily"></i>'),o.setDropdown(t)},set:function(t){var i={tag:"span",style:{"font-family":t},type:"toggle"};this.inline.format(i)},remove:function(){this.inline.remove({style:"font-family"})}});