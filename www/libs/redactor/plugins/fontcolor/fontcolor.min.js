!function(a){a.add("plugin","fontcolor",{translations:{en:{fontcolor:"Text Color",text:"Text",highlight:"Highlight"}},init:function(t){this.app=t,this.opts=t.opts,this.lang=t.lang,this.inline=t.inline,this.toolbar=t.toolbar,this.selection=t.selection,this.colors=this.opts.fontcolors?this.opts.fontcolors:["#ffffff","#000000","#eeece1","#1f497d","#4f81bd","#c0504d","#9bbb59","#8064a2","#4bacc6","#f79646","#ffff00","#f2f2f2","#7f7f7f","#ddd9c3","#c6d9f0","#dbe5f1","#f2dcdb","#ebf1dd","#e5e0ec","#dbeef3","#fdeada","#fff2ca","#d8d8d8","#595959","#c4bd97","#8db3e2","#b8cce4","#e5b9b7","#d7e3bc","#ccc1d9","#b7dde8","#fbd5b5","#ffe694","#bfbfbf","#3f3f3f","#938953","#548dd4","#95b3d7","#d99694","#c3d69b","#b2a2c7","#b7dde8","#fac08f","#f2c314","#a5a5a5","#262626","#494429","#17365d","#366092","#953734","#76923c","#5f497a","#92cddc","#e36c09","#c09100","#7f7f7f","#0c0c0c","#1d1b10","#0f243e","#244061","#632423","#4f6128","#3f3151","#31859b","#974806","#7f6000"]},onfontcolor:{set:function(t,e){this._set(t,e)},remove:function(t){this._remove(t)}},start:function(){var t={title:this.lang.get("fontcolor")},e=this._buildDropdown();this.$button=this.toolbar.addButton("fontcolor",t),this.$button.setIcon('<i class="re-icon-fontcolor"></i>'),this.$button.setDropdown(e)},_buildDropdown:function(){var t=a.dom('<div class="redactor-dropdown-cells">');return this.$selector=this._buildSelector(),this.$selectorText=this._buildSelectorItem("text",this.lang.get("text")),this.$selectorText.addClass("active"),this.$selectorBack=this._buildSelectorItem("back",this.lang.get("highlight")),this.$selector.append(this.$selectorText),this.$selector.append(this.$selectorBack),this.$pickerText=this._buildPicker("textcolor"),this.$pickerBack=this._buildPicker("backcolor"),t.append(this.$selector),t.append(this.$pickerText),t.append(this.$pickerBack),this._buildSelectorEvents(),t.width(242),t},_buildSelector:function(){var t=a.dom("<div>");return t.addClass("redactor-dropdown-selector"),t},_buildSelectorItem:function(t,e){var o=a.dom("<span>");return o.attr("rel",t).html(e),o.addClass("redactor-dropdown-not-close"),o},_buildSelectorEvents:function(){this.$selectorText.on("mousedown",function(t){t.preventDefault(),this.$selector.find("span").removeClass("active"),this.$pickerBack.hide(),this.$pickerText.show(),this.$selectorText.addClass("active")}.bind(this)),this.$selectorBack.on("mousedown",function(t){t.preventDefault(),this.$selector.find("span").removeClass("active"),this.$pickerText.hide(),this.$pickerBack.show(),this.$selectorBack.addClass("active")}.bind(this))},_buildPicker:function(t){function e(t){t.preventDefault();var e=a.dom(t.target);c._set(e.data("rule"),e.attr("rel"))}for(var o=a.dom('<div class="re-dropdown-box-'+t+'">'),i="backcolor"==t?"background-color":"color",s=this.colors.length,c=this,r=0;r<s;r++){var n=this.colors[r],d=a.dom("<span>");d.attr({rel:n,"data-rule":i}),d.css({"background-color":n,"font-size":0,border:"2px solid #fff",width:"22px",height:"22px"}),d.on("mousedown",e),o.append(d)}var l=a.dom("<a>");return l.attr({href:"#"}),l.css({display:"block",clear:"both",padding:"8px 5px","font-size":"12px","line-height":1}),l.html(this.lang.get("none")),l.on("click",function(t){t.preventDefault(),c._remove(i)}),o.append(l),"backcolor"==t&&o.hide(),o},_set:function(t,e){var o={};o[t]=e;var i={tag:"span",style:o,type:"toggle"};this.inline.format(i)},_remove:function(t){this.inline.remove({style:t})}})}(Redactor);