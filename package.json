{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "<PERSON><PERSON><PERSON>", "private": true, "contributors": ["<PERSON><PERSON>"], "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/register": "^7.25.9", "autoprefixer": "^10.4.20", "del": "^8.0.0", "gulp": "^4.0.2", "gulp-plumber": "^1.2.1", "gulp-postcss": "^10.0.0", "gulp-sass": "^5.1.0", "sass": "^1.71.0", "tailwindcss": "^3.4.17"}, "dependencies": {"babel-cli": "^6.26.0", "babel-preset-react-app": "^10.0.1", "lazysizes": "^5.3.2"}, "main": "gulpfile.babel.js", "directories": {"test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "tailwind": "npx tailwindcss -i ./www/css2/input.css -o ./www/css2/output.css --watch"}, "repository": {"type": "git", "url": "git+https://github.com/Tipli/kaufino.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Tipli/kaufino/issues"}, "homepage": "https://github.com/Tipli/kaufino#readme", "type": "module"}