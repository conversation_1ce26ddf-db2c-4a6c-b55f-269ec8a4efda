FROM php:8.1-apache


#RUN apt-get install -y libzip-dev
#RUN apt-get install libpng-dev
#RUN apt-get install libxml2-dev
RUN apt-get update
RUN service apache2 start
RUN apt-get install -y curl
RUN apt-get install -y git
RUN apt-get install -y zip
RUN apt-get install -y unzip
RUN apt-get install -y zlib1g-dev
RUN apt-get install -y libzip-dev
RUN apt-get install -y libicu-dev
RUN docker-php-ext-install zip

# php ext gd
RUN apt-get install -y libfreetype6-dev
RUN apt-get install -y libjpeg62-turbo-dev
RUN apt-get install -y libpng-dev
RUN docker-php-ext-install bcmath
RUN docker-php-ext-configure gd --with-freetype --with-jpeg
RUN docker-php-ext-install gd

# pdo_mysql
RUN docker-php-ext-configure pdo_mysql --with-pdo-mysql
RUN docker-php-ext-install pdo_mysql


# php ext soap
RUN apt-get install -y libxml2-dev
#RUN docker-php-ext-configure soap --enable-soap
#RUN docker-php-ext-install soap

# php ext intl
RUN docker-php-ext-configure intl
RUN docker-php-ext-install intl


RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

#COPY . /project

RUN a2enmod ssl
RUN a2enmod rewrite

# Settings
WORKDIR /project

RUN service apache2 start

EXPOSE 80
EXPOSE 443
